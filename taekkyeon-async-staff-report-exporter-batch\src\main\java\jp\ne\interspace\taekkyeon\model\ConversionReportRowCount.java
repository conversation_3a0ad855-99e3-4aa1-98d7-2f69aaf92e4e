/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import javax.inject.Singleton;

import lombok.Getter;
import lombok.Setter;

/**
 * Store the row count of the conversion report.
 *
 * <AUTHOR>
 */
@Singleton @Getter @Setter
public class ConversionReportRowCount {

    private static int rowCount = 0;

    public static int getRowCount() {
        return rowCount;
    }

    public static void setRowCount(int rowCount) {
        ConversionReportRowCount.rowCount = rowCount;
    }
}
