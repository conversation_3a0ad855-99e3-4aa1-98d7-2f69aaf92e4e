/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.google.common.collect.Sets;
import com.google.inject.Inject;
import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.BonusStatus;
import jp.ne.interspace.taekkyeon.model.CampaignClosurePeriod;
import jp.ne.interspace.taekkyeon.model.CampaignReportConversionItem;
import jp.ne.interspace.taekkyeon.model.CampaignReportExportRequest;
import jp.ne.interspace.taekkyeon.model.ConversionBonusReportDetail;
import jp.ne.interspace.taekkyeon.model.ConversionClickParameterRequest;
import jp.ne.interspace.taekkyeon.model.ConversionReport;
import jp.ne.interspace.taekkyeon.model.ConversionReportExportRequest;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.CreativeType;
import jp.ne.interspace.taekkyeon.model.FindMerchantActionApprovalReportDataRequest;
import jp.ne.interspace.taekkyeon.model.MerchantActionApprovalHolder;
import jp.ne.interspace.taekkyeon.model.PublisherConversionLogItem;
import jp.ne.interspace.taekkyeon.model.PublisherConversionReportExportRequest;
import jp.ne.interspace.taekkyeon.model.PublisherType;

import static com.google.common.collect.Sets.newHashSet;
import static java.time.ZoneOffset.UTC;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptySet;
import static jp.ne.interspace.taekkyeon.model.BonusStatus.APPROVED;
import static jp.ne.interspace.taekkyeon.model.BonusStatus.PENDING;
import static jp.ne.interspace.taekkyeon.model.BonusStatus.REJECTED;
import static jp.ne.interspace.taekkyeon.model.CampaignStatus.RUNNING;
import static jp.ne.interspace.taekkyeon.model.CampaignType.CPA;
import static jp.ne.interspace.taekkyeon.model.DeviceType.UNKNOWN;
import static jp.ne.interspace.taekkyeon.model.PostbackStatus.NEEDED;
import static jp.ne.interspace.taekkyeon.model.PublisherType.UNSPECIFIED;
import static jp.ne.interspace.taekkyeon.model.ReportQueryPeriodBase.CONFIRMATION_DATE;
import static jp.ne.interspace.taekkyeon.model.ReportQueryPeriodBase.CONVERSION_DATE;
import static jp.ne.interspace.taekkyeon.model.ReportQueryPeriodBase.PAID_DATE;
import static jp.ne.interspace.taekkyeon.model.RewardType.CPA_FIXED;
import static jp.ne.interspace.taekkyeon.model.SiteType.EMPTY;
import static jp.ne.interspace.taekkyeon.model.SiteType.SEARCH_ENGINE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Integration test for {@link ConversionMapper}.
 *
 * <AUTHOR> Cao
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class ConversionMapperTest {

    private static final int STATUS = 1;
    private static final String VERIFICATION_ID = "oxpueKdEwyrPkbcVzvyh";
    private static final String PUBLISHER_SITE_NAME = "reviewingSiteName";
    private static final String PUBLISHER_SITE_NAME_2 = "approvedSiteName";

    private static final LocalDate FROM = LocalDate.of(2020, 8, 2);
    private static final LocalDate TO = LocalDate.of(2020, 8, 4);
    private static final Set<Long> CAMPAIGN_IDS = newHashSet(1L);
    private static final String SITE_ID = "1";

    private static final LocalDate DEADLINE = LocalDate.of(2020, 9, 2);
    private static final LocalDate ACTION_DATE = LocalDate.of(2020, 8, 3);

    private static final BigDecimal SALES_AMOUNT = new BigDecimal(300).setScale(2);
    private static final BigDecimal DISCOUNT_AMOUNT = BigDecimal.ZERO.setScale(2);
    private static final BigDecimal PUBLISHER_REWARD = new BigDecimal(200).setScale(2);
    private static final String PRODUCT_CATEGORY_ID = "marketplace3";
    private static final int PAGE_NUMBER = 1;
    private static final int OFFSET = 0;
    private static final int PAGE_SIZE = 5;
    private static final LocalDateTime FROM_LOCAL_DATE_TIME = LocalDateTime
            .of(2020, 10, 1, 0, 0, 0);
    private static final LocalDateTime TO_LOCAL_DATE_TIME = LocalDateTime
            .of(2022, 10, 10, 0, 0, 0);
    private static final ZonedDateTime CONVERSION_REPORT_FROM_DATE = ZonedDateTime
            .parse("2023-11-20T00:00:00.000+07:00");
    private static final ZonedDateTime CONVERSION_REPORT_TO_DATE = ZonedDateTime
            .parse("2023-11-30T00:00:00.000+07:00");
    private static final String MV_CONVERSION_OCCURRED_202311 = "MV_CONVERSION_OCCURRED_202311";

    private static final String COUNTRY_CODE = "ID";
    private static final ZonedDateTime FROM_DATE = ZonedDateTime
            .parse("2024-09-01T00:00:00.000+07:00");
    private static final ZonedDateTime TO_DATE = ZonedDateTime
            .parse("2024-10-30T00:00:00.000+07:00");
    private static final List<ConversionStatus> CONVERSION_STATUSES =
            Collections.singletonList(ConversionStatus.APPROVED);
    private static final Set<Long> SITE_IDS = Sets.newHashSet(1L);
    private static final Set<Long> PUBLISHER_IDS =
            new HashSet<>(Collections.singletonList(1L));
    private static final List<String> MV_NAMES = Arrays.asList(
            "MV_CONVERSION_CONFIRMED_202409", "MV_CONVERSION_CONFIRMED_202410");
    private static final List<CampaignClosurePeriod> CAMPAIGN_CLOSURE_PERIODS =
            Collections.singletonList(new CampaignClosurePeriod(1L,
                    ZonedDateTime.parse("2024-09-01T00:00:00.000+07:00"),
                    ZonedDateTime.parse("2024-09-15T00:00:00.000+07:00")));

    @Inject
    private ClickParameterMapper clickParameterMapper;

    @Inject
    private ConversionMapper underTest;

    @Test
    public void testFindMerchantActionApprovalReportDataShouldReturnCorrectDataWhenVerifyIsEmptyAndSiteKeyIsEmptyAndPendingConversionsFoundWithinSpecifiedPeriods() {
        //given
        FindMerchantActionApprovalReportDataRequest request =
                new FindMerchantActionApprovalReportDataRequest(
                        FROM, TO, CAMPAIGN_IDS, "", 1, "", PAGE_NUMBER, PAGE_SIZE, true);

        // when
        List<MerchantActionApprovalHolder> actual = underTest
                .findMerchantActionApprovalReportData(request);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertMerchantActionApprovalHolderForSiteId(actual.get(0));
    }

    @Test
    public void testFindMerchantActionApprovalReportDataShouldReturnEmptyWhenVerifyIsEmptyAndSiteKeyIsEmptyAndNoPendingConversionFoundWithinSpecifiedPeriods() {
        //given
        FindMerchantActionApprovalReportDataRequest request =
                new FindMerchantActionApprovalReportDataRequest(
                    LocalDate.of(2020, 8, 10), TO, CAMPAIGN_IDS, "", 1, "", PAGE_NUMBER,
                    PAGE_SIZE, false);
        // when
        List<MerchantActionApprovalHolder> actual = underTest
                .findMerchantActionApprovalReportData(request);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.size());
    }

    @Test
    public void testFindMerchantActionApprovalReportDataShouldReturnCorrectPendingConversionsWhenVerifyIsNotEmptyAndSiteKeyIsNotEmptyAndPendingConversionsFound() {
        //given
        FindMerchantActionApprovalReportDataRequest request =
                new FindMerchantActionApprovalReportDataRequest(
                    FROM, TO, CAMPAIGN_IDS, VERIFICATION_ID, 1, SITE_ID, PAGE_NUMBER,
                    PAGE_SIZE, true);

        // when
        List<MerchantActionApprovalHolder> actual = underTest
                .findMerchantActionApprovalReportData(request);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertMerchantActionApprovalHolderForSiteId(actual.get(0));
    }

    @Test
    public void testFindMerchantActionApprovalReportDataShouldReturnCorrectPendingConversionsWhenVerifyIsEmptyAndSiteKeyIsNotEmptyAndPendingConversionsFound() {
        //given
        FindMerchantActionApprovalReportDataRequest request =
                new FindMerchantActionApprovalReportDataRequest(
                        FROM, TO, CAMPAIGN_IDS, "", 1, SITE_ID, PAGE_NUMBER, PAGE_SIZE, true);

        // when
        List<MerchantActionApprovalHolder> actual = underTest
                .findMerchantActionApprovalReportData(request);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertMerchantActionApprovalHolderForSiteId(actual.get(0));
    }

    @Test
    public void testFindMerchantActionApprovalReportDataShouldReturnCorrectPendingConversionsWhenVerifyIsNotEmptyAndSiteKeyIsEmptyAndPendingConversionsFound() {
        //given
        FindMerchantActionApprovalReportDataRequest request =
                new FindMerchantActionApprovalReportDataRequest(
                    FROM, TO, CAMPAIGN_IDS, VERIFICATION_ID, 1, "", PAGE_NUMBER, PAGE_SIZE,
                    false);

        // when
        List<MerchantActionApprovalHolder> actual = underTest
                .findMerchantActionApprovalReportData(request);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertMerchantActionApprovalHolderForSiteId(actual.get(0));
    }

    @Test
    public void testFindMerchantActionApprovalReportDataShouldReturnCorrectPendingConversionsWhenVerifyIsNotEmptyAndSiteKeyIsEmptyAndStatusIsAllAndPendingConversionsFound() {
        //given
        FindMerchantActionApprovalReportDataRequest request =
                new FindMerchantActionApprovalReportDataRequest(
                    FROM, TO, CAMPAIGN_IDS, VERIFICATION_ID, -1, "", PAGE_NUMBER, PAGE_SIZE,
                    false);

        // when
        List<MerchantActionApprovalHolder> actual = underTest
                .findMerchantActionApprovalReportData(request);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertMerchantActionApprovalHolderForSiteId(actual.get(0));
    }

    @Test
    public void testFindMerchantActionApprovalReportDataShouldReturnCorrectPendingConversionsWhenVerifyIsNotEmptyAndSiteKeyIsEmptyAndHideSiteIsFalseAndPendingConversionsFound() {
        //given
        FindMerchantActionApprovalReportDataRequest request =
                new FindMerchantActionApprovalReportDataRequest(
                    FROM, TO, newHashSet(2L), "", -1, PUBLISHER_SITE_NAME_2, PAGE_NUMBER,
                    PAGE_SIZE, false);

        // when
        List<MerchantActionApprovalHolder> actual = underTest
                .findMerchantActionApprovalReportData(request);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertMerchantActionApprovalHolderForSiteName(actual.get(0));

    }

    @Test
    public void testFindMerchantActionApprovalReportDataShouldReturnCorrectPendingConversionsWhenVerifyIsEmptyAndSiteKeyIsSiteNameAndHideSiteIsTrueAndAndPendingConversionsFound() {
        //given
        FindMerchantActionApprovalReportDataRequest request =
                new FindMerchantActionApprovalReportDataRequest(
                    FROM, TO, CAMPAIGN_IDS, "", -1, PUBLISHER_SITE_NAME, PAGE_NUMBER,
                    PAGE_SIZE, false);

        // when
        List<MerchantActionApprovalHolder> actual = underTest
                .findMerchantActionApprovalReportData(request);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.size());

    }

    @Test
    public void testFindSiteIdsByShouldReturnCorrectDataWhenFoundByConfirmationDateAndCountryCode() {
        // when
        Set<Long> actual = underTest.findSiteIds(
                ZonedDateTime.of(2021, 11, 9, 0, 0, 0, 0, UTC),
                ZonedDateTime.of(2021, 11, 10, 0, 0, 0, 0, UTC), null, null,
                null, null, "ID", CONFIRMATION_DATE);

        // then
        assertEquals(newHashSet(1507L), actual);
    }

    @Test
    public void testFindSiteIdsShouldReturnCorrectDataWhenFoundByDateAndCountryCode() {
        // when
        Set<Long> actual = underTest.findSiteIds(
                ZonedDateTime.of(2021, 9, 9, 0, 0, 0, 0, UTC),
                ZonedDateTime.of(2021, 9, 10, 0, 0, 0, 0, UTC), null, null,
                null, null, "ID", CONVERSION_DATE);

        // then
        assertEquals(newHashSet(1500L, 1501L, 1503L, 1504L, 1505L, 1506L, 1507L),
                actual);
    }

    @Test
    public void testFindSiteIdsShouldReturnCorrectDataWhenFoundByDateAndCountryCodeAndPublisherType() {
        // when
        Set<Long> actual = underTest.findSiteIds(
                ZonedDateTime.of(2021, 9, 9, 0, 0, 0, 0, UTC),
                ZonedDateTime.of(2021, 9, 10, 0, 0, 0, 0, UTC), null,
                PublisherType.GLOBAL, null, null, "ID", CONVERSION_DATE);

        // then
        assertEquals(newHashSet(1503L, 1504L, 1505L, 1506L, 1507L), actual);
    }

    @Test
    public void testFindSiteIdsShouldReturnCorrectDataWhenFoundByDateAndCountryCodeAndPublisherTypeAndAgencyId() {
        // when
        Set<Long> actual = underTest.findSiteIds(
                ZonedDateTime.of(2021, 9, 9, 0, 0, 0, 0, UTC),
                ZonedDateTime.of(2021, 9, 10, 0, 0, 0, 0, UTC), 1L,
                PublisherType.GLOBAL, null, null, "ID", CONVERSION_DATE);

        // then
        assertEquals(newHashSet(1505L, 1506L, 1507L), actual);
    }

    @Test
    public void testFindSiteIdsShouldReturnCorrectDataWhenFoundByDateAndCountryCodeAndPublisherTypeAndAgencyIdAndSiteType() {
        // when
        Set<Long> actual = underTest.findSiteIds(
                ZonedDateTime.of(2021, 9, 9, 0, 0, 0, 0, UTC),
                ZonedDateTime.of(2021, 9, 10, 0, 0, 0, 0, UTC), 1L,
                PublisherType.GLOBAL, asList(SEARCH_ENGINE), null, "ID",
                CONVERSION_DATE);

        // then
        assertEquals(newHashSet(1506L, 1507L), actual);
    }

    @Test
    public void testFindSiteIdsShouldReturnCorrectDataWhenFoundByDateAndCountryCodeAndPublisherTypeAndAgencyIdAndSiteTypeAndCategory() {
        // when
        Set<Long> actual = underTest.findSiteIds(
                ZonedDateTime.of(2021, 9, 9, 0, 0, 0, 0, UTC),
                ZonedDateTime.of(2021, 9, 10, 0, 0, 0, 0, UTC), 1L,
                PublisherType.GLOBAL, asList(SEARCH_ENGINE), asList(101L),
                "ID", CONVERSION_DATE);

        // then
        assertEquals(newHashSet(1507L), actual);
    }

    @Test
    public void testFindConversionBonusByConfirmationDateShouldReturnCorrectDataWhenTheCampaignIdsAndBonusStatusesIsSpecified() {
        // given
        int limitRow = 10;

        // when
        List<ConversionBonusReportDetail> actual = underTest
                .findConversionBonusByConfirmationDate(
                        "VN", null, Sets.newHashSet(10000001L, 10000002L), null,
                        Collections.singletonList(PENDING), FROM_LOCAL_DATE_TIME,
                        TO_LOCAL_DATE_TIME, 1, limitRow);

        // then
        assertEquals(2, actual.size());
        assertBonusReportDetail(actual.get(0), 10000001, 0, PENDING,
                LocalDateTime.of(2020, 10, 1, 0, 0, 0), new BigDecimal("0.10"),
                new BigDecimal("0.11"), new BigDecimal("0.12"), new BigDecimal("0.13"),
                new BigDecimal("0.14"), new BigDecimal("0.12"), new BigDecimal("0.13"),
                new BigDecimal("0.14"), LocalDateTime.of(2020, 10, 2, 0, 0, 0), "Cuong",
                LocalDateTime.of(2020, 10, 1, 0, 0, 0));
        assertBonusReportDetail(actual.get(1), 10000002, 0, PENDING,
                LocalDateTime.of(2020, 10, 1, 0, 0, 0), new BigDecimal("0.20"),
                new BigDecimal("0.21"), new BigDecimal("0.22"), new BigDecimal("0.23"),
                new BigDecimal("0.24"), new BigDecimal("0.22"), new BigDecimal("0.23"),
                new BigDecimal("0.24"), LocalDateTime.of(2020, 10, 1, 0, 0, 0), "Cuong",
                LocalDateTime.of(2020, 10, 1, 0, 0, 0));
    }

    @Test
    public void testFindConversionBonusByConfirmationDateShouldReturnCorrectDataWhenTheSiteIdsAndBonusStatusesIsSpecified() {
        // given
        int limitRow = 10;

        // when
        List<ConversionBonusReportDetail> actual = underTest
                .findConversionBonusByConfirmationDate(
                        "VN", Sets.newHashSet(10000003L, 10000004L), null, null,
                        Collections.singletonList(APPROVED), FROM_LOCAL_DATE_TIME,
                        TO_LOCAL_DATE_TIME, 1, limitRow);

        // then
        assertEquals(2, actual.size());
        assertBonusReportDetail(actual.get(0), 10000003, 0, APPROVED,
                LocalDateTime.of(2020, 10, 3, 0, 0, 0), new BigDecimal("0.30"),
                new BigDecimal("0.31"), new BigDecimal("0.32"), new BigDecimal("0.33"),
                new BigDecimal("0.34"), new BigDecimal("0.32"), new BigDecimal("0.33"),
                new BigDecimal("0.34"), LocalDateTime.of(2020, 10, 3, 0, 0, 0),
                "CuongTest", LocalDateTime.of(2020, 10, 1, 0, 0, 0));
        assertBonusReportDetail(actual.get(1), 10000004, 0, APPROVED,
                LocalDateTime.of(2020, 10, 1, 7, 46, 34), new BigDecimal("0.40"),
                new BigDecimal("0.41"), new BigDecimal("0.42"), new BigDecimal("0.43"),
                new BigDecimal("0.44"), new BigDecimal("0.42"), new BigDecimal("0.43"),
                new BigDecimal("0.44"), LocalDateTime.of(2020, 11, 1, 0, 0, 0),
                "CuongTest", LocalDateTime.of(2020, 11, 1, 0, 0, 0));
    }

    @Test
    public void testFindAllByConfirmationDateShouldReturnCorrectDataWhenTheBonusIdsAndBonusStatusesAndLimitIsSpecified() {
        // given
        int limitRow = 1;

        // when
        List<ConversionBonusReportDetail> actual = underTest
                .findConversionBonusByConfirmationDate(
                        "VN", null, null, Sets.newHashSet(0L),
                        Collections.singletonList(REJECTED), FROM_LOCAL_DATE_TIME,
                        TO_LOCAL_DATE_TIME, 1, limitRow);

        // then
        assertEquals(1, actual.size());
        assertBonusReportDetail(actual.get(0), 10000005, 0, REJECTED,
                LocalDateTime.of(2020, 10, 5, 7, 46, 34), new BigDecimal("0.50"),
                new BigDecimal("0.51"), new BigDecimal("0.52"), new BigDecimal("0.53"),
                new BigDecimal("0.54"), new BigDecimal("0.52"), new BigDecimal("0.53"),
                new BigDecimal("0.54"), LocalDateTime.of(2020, 10, 2, 7, 46, 34),
                "Cuong", LocalDateTime.of(2020, 10, 1, 7, 46, 34));
    }

    @Test
    public void testFindConversionBonusByConversionDateShouldReturnCorrectDataWhenTheCampaignIdsAndBonusStatusesIsSpecified() {
        // given
        int limitRow = 10;

        // when
        List<ConversionBonusReportDetail> actual = underTest
                .findConversionBonusByConversionDate(
                        "VN", null, Sets.newHashSet(10000006L, 10000007L), null,
                        Collections.singletonList(REJECTED), FROM_LOCAL_DATE_TIME,
                        TO_LOCAL_DATE_TIME, 1, limitRow);

        // then
        assertEquals(2, actual.size());
        assertBonusReportDetail(actual.get(0), 10000006L, 0, REJECTED,
                LocalDateTime.of(2020, 9, 1, 0, 0, 0), new BigDecimal("0.60"),
                new BigDecimal("0.61"), new BigDecimal("0.62"), new BigDecimal("0.63"),
                new BigDecimal("0.64"), new BigDecimal("0.62"), new BigDecimal("0.63"),
                new BigDecimal("0.64"), LocalDateTime.of(2020, 10, 2, 0, 0, 0), "Cuong",
                LocalDateTime.of(2020, 11, 6, 0, 0, 0));
        assertBonusReportDetail(actual.get(1), 10000007L, 0, REJECTED,
                LocalDateTime.of(2020, 10, 1, 0, 0, 0), new BigDecimal("0.70"),
                new BigDecimal("0.71"), new BigDecimal("0.72"), new BigDecimal("0.73"),
                new BigDecimal("0.74"), new BigDecimal("0.72"), new BigDecimal("0.73"),
                new BigDecimal("0.74"), LocalDateTime.of(2020, 10, 2, 0, 0, 0), "Cuong",
                LocalDateTime.of(2020, 11, 7, 0, 0, 0));
    }

    @Test
    public void testFindConversionBonusByConversionDateShouldReturnCorrectDataWhenTheSitesIdsAndBonusStatusesIsSpecified() {
        // given
        int limitRow = 10;

        // when
        List<ConversionBonusReportDetail> actual = underTest
                .findConversionBonusByConversionDate(
                        "VN", Sets.newHashSet(10000008L, 10000009L), null, null,
                        Collections.singletonList(REJECTED), FROM_LOCAL_DATE_TIME,
                        TO_LOCAL_DATE_TIME, 1, limitRow);

        // then
        assertEquals(2, actual.size());
        assertBonusReportDetail(actual.get(0), 10000008, 1, REJECTED,
                LocalDateTime.of(2020, 9, 1, 7, 46, 34), new BigDecimal("0.80"),
                new BigDecimal("0.81"), new BigDecimal("0.82"), new BigDecimal("0.83"),
                new BigDecimal("0.84"), new BigDecimal("0.82"), new BigDecimal("0.83"),
                new BigDecimal("0.84"), LocalDateTime.of(2020, 10, 2, 7, 46, 34),
                "Cuong", LocalDateTime.of(2020, 11, 8, 7, 46, 34));
        assertBonusReportDetail(actual.get(1), 10000009, 1, REJECTED,
                LocalDateTime.of(2020, 10, 1, 7, 46, 34), new BigDecimal("0.90"),
                new BigDecimal("0.91"), new BigDecimal("0.92"), new BigDecimal("0.93"),
                new BigDecimal("0.94"), new BigDecimal("0.92"), new BigDecimal("0.93"),
                new BigDecimal("0.94"), LocalDateTime.of(2020, 11, 2, 7, 46, 34),
                "Cuong", LocalDateTime.of(2020, 11, 9, 7, 46, 34));
    }

    @Test
    public void testFindFullConversionReportFromTempTableShouldReturnCorrectDataWhenCalled() {
        // given
        ConversionReportExportRequest request = new ConversionReportExportRequest(
                null, CONVERSION_REPORT_FROM_DATE, CONVERSION_REPORT_TO_DATE,
                null, null, CONVERSION_DATE, null, null, null, null, null, null, null,
                null,  null, null, 0L, null,
                false);

        ConversionReport expected = new ConversionReport(10000L,
                ConversionStatus.PENDING, "verify10000",
                LocalDateTime.of(2023, 11, 25, 0, 0),
                LocalDateTime.of(2023, 11, 25, 0, 0),
                LocalDateTime.of(2023, 11, 25, 0, 0),
                new BigDecimal("10.01"), new BigDecimal("0.01"), CPA_FIXED,
                new BigDecimal("20.05"), new BigDecimal("10.04"),
                new BigDecimal("10.05"), new BigDecimal("10.06"),
                new BigDecimal("50.20"), 1L, "MERCHANT_CAMPAIGN_01", 3,
                "RESULT_NAME", "PRODUCT_id", "productCategoryId", 1L, "siteName", 5,
                1L, CreativeType.IMAGE, 1L, "creativeGroupName", "creativeSize",
                NEEDED, 1L,
                LocalDateTime.of(2023, 11, 25, 0, 0),
                "postbackUrl10000", UNKNOWN, "0", "clickReferer10000",
                "clickUrl10000", "clickUserAgent10000", "parameter10000",
                "customerType10000", 1, 0,
                new BigDecimal("1.12"), new BigDecimal("1.11"),
                new BigDecimal("1.14"), new BigDecimal("1.13"));


        // when
        underTest.insertConversionTempTable(request, request.getFromDate().toLocalDate(),
                request.getToDate().toLocalDate());
        clickParameterMapper.insertClickParameterTempTable();

        List<ConversionReport> actual = underTest
                .findFullConversionReportFromTempTableBy(OFFSET, PAGE_SIZE, true);

        // then
        assertEquals(asList(expected), actual);
    }

    @Test
    public void testFindCampaignReportDataShouldReturnCorrectDataWhenCalled() {
        // given
        CampaignReportExportRequest request = createCampaignReportRequest(
                PUBLISHER_IDS, SITE_IDS);

        // when
        List<CampaignReportConversionItem> actual = underTest.findCampaignReportData(
                MV_NAMES, request, Sets.newHashSet(1L, 2L), CAMPAIGN_CLOSURE_PERIODS);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertCampaignReportConversionItem(actual.get(0), 1L, new BigDecimal("30.12"),
                new BigDecimal("30.15"), new BigDecimal("30.18"),
                new BigDecimal("150.60"), 0, 0, 2L, new BigDecimal("30.03"), 3, 2L,
                new BigDecimal("30.06"), new BigDecimal("30.09"));
        assertCampaignReportConversionItem(actual.get(1), 2L, new BigDecimal("20.08"),
                new BigDecimal("20.10"), new BigDecimal("20.12"),
                new BigDecimal("100.40"), 0, 0, 2L, new BigDecimal("20.02"), 2, 2L,
                new BigDecimal("20.04"), new BigDecimal("20.06"));
    }

    @Test
    public void testFindCampaignReportDataShouldReturnCorrectDataWhenIsPendingForMerchantPaymentIsTrueAndConversionStatusesIsNull() {
        // given
        CampaignReportExportRequest request = new CampaignReportExportRequest(
                COUNTRY_CODE, FROM_DATE, TO_DATE, SITE_IDS, CAMPAIGN_IDS,
                CONFIRMATION_DATE, null, Sets.newHashSet(1L), PUBLISHER_IDS, UNSPECIFIED,
                Collections.singletonList(EMPTY), Collections.singletonList(1L),
                Collections.singletonList(CPA), Collections.singletonList(1L),
                null, true);
        List<CampaignClosurePeriod> campaignClosurePeriod =
                Collections.singletonList(new CampaignClosurePeriod(1L,
                        ZonedDateTime.parse("2024-09-12T00:00:00.000+07:00"),
                        ZonedDateTime.parse("2024-09-21T00:00:00.000+07:00")));

        // when
        List<CampaignReportConversionItem> actual = underTest.findCampaignReportData(
                MV_NAMES, request, Sets.newHashSet(1L, 2L), campaignClosurePeriod);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertCampaignReportConversionItem(actual.get(0), 1L, new BigDecimal("10.04"),
                new BigDecimal("10.05"), new BigDecimal("10.06"),
                new BigDecimal("50.20"), 0, 0, 1L, new BigDecimal("10.01"), 1, 1L,
                new BigDecimal("10.02"), new BigDecimal("10.03"));
    }

    @Test
    public void testFindCampaignReportDataShouldReturnCorrectDataWhenIsPendingForMerchantPaymentIsFalse() {
        // given
        CampaignReportExportRequest request = new CampaignReportExportRequest(
                COUNTRY_CODE, FROM_DATE, TO_DATE, SITE_IDS, CAMPAIGN_IDS,
                CONFIRMATION_DATE, CONVERSION_STATUSES, Sets.newHashSet(1L),
                PUBLISHER_IDS, UNSPECIFIED, Collections.singletonList(EMPTY),
                Collections.singletonList(1L), Collections.singletonList(CPA),
                Collections.singletonList(1L), null, false);

        // when
        List<CampaignReportConversionItem> actual = underTest.findCampaignReportData(
                MV_NAMES, request, Sets.newHashSet(1L, 2L), CAMPAIGN_CLOSURE_PERIODS);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertCampaignReportConversionItem(actual.get(0), 1L, new BigDecimal("30.12"),
                new BigDecimal("30.15"), new BigDecimal("30.18"),
                new BigDecimal("150.60"), 0, 0, 2L, new BigDecimal("30.03"), 3, 2L,
                new BigDecimal("30.06"), new BigDecimal("30.09"));
        assertCampaignReportConversionItem(actual.get(1), 2L, new BigDecimal("20.08"),
                new BigDecimal("20.10"), new BigDecimal("20.12"),
                new BigDecimal("100.40"), 0, 0, 2L, new BigDecimal("20.02"), 2, 2L,
                new BigDecimal("20.04"), new BigDecimal("20.06"));
    }

    @Test
    public void testFindCampaignReportDataShouldReturnCorrectDataWhenCampaignIdsIsEmpty() {
        // given
        CampaignReportExportRequest request = createCampaignReportRequest(
                PUBLISHER_IDS, SITE_IDS);

        // when
        List<CampaignReportConversionItem> actual = underTest.findCampaignReportData(
                MV_NAMES, request, emptySet(), emptyList());

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertCampaignReportConversionItem(actual.get(0), 1L, new BigDecimal("30.12"),
                new BigDecimal("30.15"), new BigDecimal("30.18"),
                new BigDecimal("150.60"), 0, 0, 2L, new BigDecimal("30.03"), 3, 2L,
                new BigDecimal("30.06"), new BigDecimal("30.09"));
        assertCampaignReportConversionItem(actual.get(1), 2L, new BigDecimal("20.08"),
                new BigDecimal("20.10"), new BigDecimal("20.12"),
                new BigDecimal("100.40"), 0, 0, 2L, new BigDecimal("20.02"), 2, 2L,
                new BigDecimal("20.04"), new BigDecimal("20.06"));
    }

    @Test
    public void testFindCampaignReportDataShouldReturnCorrectDataWhenCampaignClosurePeriodIsEmpty() {
        // given
        CampaignReportExportRequest request = createCampaignReportRequest(
                PUBLISHER_IDS, SITE_IDS);

        // when
        List<CampaignReportConversionItem> actual = underTest.findCampaignReportData(
                MV_NAMES, request, Sets.newHashSet(1L, 2L), emptyList());

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertCampaignReportConversionItem(actual.get(0), 1L, new BigDecimal("30.12"),
                new BigDecimal("30.15"), new BigDecimal("30.18"),
                new BigDecimal("150.60"), 0, 0, 2L, new BigDecimal("30.03"), 3, 2L,
                new BigDecimal("30.06"), new BigDecimal("30.09"));
        assertCampaignReportConversionItem(actual.get(1), 2L, new BigDecimal("20.08"),
                new BigDecimal("20.10"), new BigDecimal("20.12"),
                new BigDecimal("100.40"), 0, 0, 2L, new BigDecimal("20.02"), 2, 2L,
                new BigDecimal("20.04"), new BigDecimal("20.06"));
    }

    @Test
    public void testFindCampaignReportDataShouldReturnCorrectDataWhenSiteIdsIsEmpty() {
        // given
        CampaignReportExportRequest request = createCampaignReportRequest(
                PUBLISHER_IDS, emptySet());

        // when
        List<CampaignReportConversionItem> actual = underTest.findCampaignReportData(
                MV_NAMES, request, Sets.newHashSet(1L, 2L), emptyList());

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertCampaignReportConversionItem(actual.get(0), 1L, new BigDecimal("30.12"),
                new BigDecimal("30.15"), new BigDecimal("30.18"),
                new BigDecimal("150.60"), 0, 0, 2L, new BigDecimal("30.03"), 3, 2L,
                new BigDecimal("30.06"), new BigDecimal("30.09"));
        assertCampaignReportConversionItem(actual.get(1), 2L, new BigDecimal("20.08"),
                new BigDecimal("20.10"), new BigDecimal("20.12"),
                new BigDecimal("100.40"), 0, 0, 2L, new BigDecimal("20.02"), 2, 2L,
                new BigDecimal("20.04"), new BigDecimal("20.06"));
    }

    @Test
    public void testFindAllPublisherTransactionIdsByShouldReturnCorrectDataWhenPeriodTypeIsNotPaidDate() {
        // given
        PublisherConversionReportExportRequest request = new PublisherConversionReportExportRequest(
                null, CONVERSION_REPORT_FROM_DATE, CONVERSION_REPORT_TO_DATE,
                null, null, CONVERSION_DATE, null, null,
                false);
        Set<ConversionClickParameterRequest> expected = Sets.newHashSet(
                new ConversionClickParameterRequest("internalTransactionId10000", 1L));

        // when
        Set<ConversionClickParameterRequest> actual = underTest
                .findAllPublisherClickParameterRequestsBy(
                null, request.getFromDate().toLocalDate(),
                        request.getToDate().toLocalDate(), request);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testFindAllPublisherTransactionIdsByShouldReturnCorrectDataWhenPeriodTypeIsPaidDate() {
        // given
        PublisherConversionReportExportRequest request = new PublisherConversionReportExportRequest(
                null, CONVERSION_REPORT_FROM_DATE, CONVERSION_REPORT_TO_DATE,
                null, null, PAID_DATE, null, null,
                false);
        Set<ConversionClickParameterRequest> expected = Sets.newHashSet(
                new ConversionClickParameterRequest("internalTransactionId10000", 1L));
        List<Long> campaignClosureIds = asList(99L);

        // when
        Set<ConversionClickParameterRequest> actual = underTest
                .findAllPublisherClickParameterRequestsBy(
                campaignClosureIds, request.getFromDate().toLocalDate(),
                        request.getToDate().toLocalDate(), request);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testFindPublisherConversionReportDataShouldReturnCorrectDataWhenCampaignClosurePeriodIsNullOrEmpty() {
        // given
        PublisherConversionReportExportRequest request = new PublisherConversionReportExportRequest(
                null, CONVERSION_REPORT_FROM_DATE, CONVERSION_REPORT_TO_DATE,
                null, null, CONVERSION_DATE, null, null,
                false);

        PublisherConversionLogItem expected = new PublisherConversionLogItem(10000L, 1L,
                1L, 400L, "verify10000", "transactionId10000", "sessionId1",
                "product10000", 1, "category10000", "ID", "ID", "customerType10000",
                "click_ip", "en", "clickUserAgent10000", UNKNOWN, "clickReferer10000", 0,
                "postbackUrl10000", ConversionStatus.PENDING, null,
                new BigDecimal("20.05"), new BigDecimal("10.01"),
                LocalDateTime.of(2023, 11, 25, 0, 0),
                LocalDateTime.of(2023, 11, 25, 0, 0),
                LocalDateTime.of(2023, 11, 25, 0, 0), NEEDED,
                LocalDateTime.of(2023, 11, 25, 0, 0));

        // when
        List<PublisherConversionLogItem> actual = underTest
                .findPublisherConversionReportData(
                        "IDR", null,
                        OFFSET, PAGE_SIZE, request.getFromDate().toLocalDate(),
                        request.getToDate().toLocalDate(), request);

        // then
        assertEquals(asList(expected), actual);
    }

    @Test
    public void testFindPublisherConversionReportDataShouldReturnCorrectDataWhenIsPendingForMerchantPaymentIsTrue() {
        // given
        PublisherConversionReportExportRequest request = new PublisherConversionReportExportRequest(
                null, CONVERSION_REPORT_FROM_DATE, CONVERSION_REPORT_TO_DATE,
                null, null, CONVERSION_DATE, null, null,
                true);

        PublisherConversionLogItem expected = new PublisherConversionLogItem(10000L, 1L,
                1L, 400L, "verify10000", "transactionId10000", "sessionId1",
                "product10000", 1, "category10000", "ID", "ID", "customerType10000",
                "click_ip", "en", "clickUserAgent10000", UNKNOWN, "clickReferer10000", 0,
                "postbackUrl10000", ConversionStatus.PENDING, null,
                new BigDecimal("20.05"), new BigDecimal("10.01"),
                LocalDateTime.of(2023, 11, 25, 0, 0),
                LocalDateTime.of(2023, 11, 25, 0, 0),
                LocalDateTime.of(2023, 11, 25, 0, 0), NEEDED,
                LocalDateTime.of(2023, 11, 25, 0, 0));

        // when
        List<PublisherConversionLogItem> actual = underTest
                .findPublisherConversionReportData(
                        "IDR", null,
                        OFFSET, PAGE_SIZE, request.getFromDate().toLocalDate(),
                        request.getToDate().toLocalDate(), request);

        // then
        assertEquals(asList(expected), actual);
    }

    @Test
    public void testFindPublisherConversionReportDataShouldReturnCorrectDataWhenPeriodTypeIsPaidDateAndCampaignClosureIdsIsNotNull() {
        // given
        PublisherConversionReportExportRequest request = new PublisherConversionReportExportRequest(
                null, CONVERSION_REPORT_FROM_DATE, CONVERSION_REPORT_TO_DATE,
                null, null, PAID_DATE, null, null,
                false);

        PublisherConversionLogItem expected = new PublisherConversionLogItem(10000L, 1L,
                1L, 400L, "verify10000", "transactionId10000", "sessionId1",
                "product10000", 1, "category10000", "ID", "ID", "customerType10000",
                "click_ip", "en", "clickUserAgent10000", UNKNOWN, "clickReferer10000", 0,
                "postbackUrl10000", ConversionStatus.PENDING, null,
                new BigDecimal("20.05"), new BigDecimal("10.01"),
                LocalDateTime.of(2023, 11, 25, 0, 0),
                LocalDateTime.of(2023, 11, 25, 0, 0),
                LocalDateTime.of(2023, 11, 25, 0, 0), NEEDED,
                LocalDateTime.of(2023, 11, 25, 0, 0));
        List<Long> campaignClosureIds = asList(99L);

        // when
        List<PublisherConversionLogItem> actual = underTest
                .findPublisherConversionReportData(
                        "IDR", campaignClosureIds,
                        OFFSET, PAGE_SIZE, request.getFromDate().toLocalDate(),
                        request.getToDate().toLocalDate(), request);

        // then
        assertEquals(asList(expected), actual);
    }

    private void assertMerchantActionApprovalHolderForSiteId(
            MerchantActionApprovalHolder actual) {
        assertNotNull(actual);
        assertEquals(VERIFICATION_ID, actual.getActionId());
        assertEquals(STATUS, actual.getStatus());
        assertEquals(VERIFICATION_ID, actual.getActionId());
        assertEquals(ACTION_DATE, actual.getActionDate());
        assertEquals(SALES_AMOUNT, actual.getSalesAmount());
        assertEquals(DISCOUNT_AMOUNT, actual.getDiscount());
        assertEquals(PUBLISHER_REWARD, actual.getReward());
        assertEquals(PRODUCT_CATEGORY_ID, actual.getCategoryId());
        assertEquals(DEADLINE, actual.getDeadline());
        assertEquals(PUBLISHER_SITE_NAME, actual.getPublisherSite());
    }

    private void assertMerchantActionApprovalHolderForSiteName(
            MerchantActionApprovalHolder actual) {
        assertNotNull(actual);
        assertEquals(VERIFICATION_ID, actual.getActionId());
        assertEquals(STATUS, actual.getStatus());
        assertEquals(VERIFICATION_ID, actual.getActionId());
        assertEquals(ACTION_DATE, actual.getActionDate());
        assertEquals(SALES_AMOUNT, actual.getSalesAmount());
        assertEquals(DISCOUNT_AMOUNT, actual.getDiscount());
        assertEquals(PUBLISHER_REWARD, actual.getReward());
        assertEquals(PRODUCT_CATEGORY_ID, actual.getCategoryId());
        assertEquals(null, actual.getDeadline());
        assertEquals(PUBLISHER_SITE_NAME_2, actual.getPublisherSite());
    }

    private void assertCampaignReportConversionItem(CampaignReportConversionItem actual,
            long campaignId, BigDecimal atCommission, BigDecimal merchantAgentCommission,
            BigDecimal publisherAgentCommission, BigDecimal totalCommission,
            long pendingConversionCount, long rejectedConversionCount,
            long approvedConversionCount, BigDecimal transactionAmount,
            int transactionItems, long conversionCount, BigDecimal salesReward,
            BigDecimal transactionAmountReward) {
        assertNotNull(actual);
        assertEquals(campaignId, actual.getCampaignId());
        assertEquals(atCommission, actual.getAtCommission());
        assertEquals(merchantAgentCommission, actual.getMerchantAgentCommission());
        assertEquals(publisherAgentCommission, actual.getPublisherAgentCommission());
        assertEquals(totalCommission, actual.getTotalCommission());
        assertEquals(pendingConversionCount, actual.getPendingConversionCount());
        assertEquals(rejectedConversionCount, actual.getRejectedConversionCount());
        assertEquals(approvedConversionCount, actual.getApprovedConversionCount());
        assertEquals(transactionAmount, actual.getTransactionAmount());
        assertEquals(transactionItems, actual.getTransactionItems());
        assertEquals(conversionCount, actual.getConversionCount());
        assertEquals(salesReward, actual.getSalesReward());
        assertEquals(transactionAmountReward, actual.getTransactionAmountReward());
    }

    private void assertBonusReportDetail(ConversionBonusReportDetail actual, long siteId,
            long bonusSettingIds, BonusStatus bonusStatus, LocalDateTime confirmationDate,
            BigDecimal atBonus, BigDecimal publisherBonus, BigDecimal merchantAgentBonus,
            BigDecimal publisherAgentBonus, BigDecimal atBonusInUsd,
            BigDecimal publisherBonusInUsd, BigDecimal merchantAgentBonusInUsd,
            BigDecimal publisherAgentBonusInUsd, LocalDateTime createdOn,
            String createdBy, LocalDateTime conversionTime) {
        assertNotNull(actual);
        assertEquals(siteId, actual.getSiteId());
        assertEquals(bonusSettingIds, actual.getBonusSettingId());
        assertEquals(bonusStatus, actual.getStatus());
        assertEquals(confirmationDate, actual.getConfirmationTime());
        assertEquals(atBonus, actual.getAtBonus());
        assertEquals(publisherBonus, actual.getPublisherBonus());
        assertEquals(publisherAgentBonus, actual.getPublisherAgentBonus());
        assertEquals(merchantAgentBonus, actual.getMerchantAgentBonus());
        assertEquals(atBonusInUsd, actual.getAtBonusInUsd());
        assertEquals(publisherBonusInUsd, actual.getPublisherBonusInUsd());
        assertEquals(publisherAgentBonusInUsd, actual.getPublisherAgentBonusInUsd());
        assertEquals(merchantAgentBonusInUsd, actual.getMerchantAgentBonusInUsd());
        assertEquals(createdOn, actual.getCreatedOn());
        assertEquals(createdBy, actual.getCreatedBy());
        assertEquals(conversionTime, actual.getConversionTime());
    }

    private CampaignReportExportRequest createCampaignReportRequest(
            Set<Long> publisherIds, Set<Long> siteIds) {
        return new CampaignReportExportRequest(COUNTRY_CODE, FROM_DATE, TO_DATE, siteIds,
                CAMPAIGN_IDS, CONFIRMATION_DATE, CONVERSION_STATUSES, Sets.newHashSet(1L),
                publisherIds, UNSPECIFIED, Collections.singletonList(EMPTY),
                Collections.singletonList(1L), Collections.singletonList(CPA),
                Collections.singletonList(1L), Collections.singletonList(RUNNING), true);
    }
}
