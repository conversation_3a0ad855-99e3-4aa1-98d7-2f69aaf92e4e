/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

import lombok.Getter;
import lombok.Setter;

/**
 * DTO for holding the data required for finding conversion report.
 *
 * <AUTHOR> Tran
 */
@Getter @Setter
public class ConversionReportExportRequest extends BaseReportExportRequest
        implements ConversionReportExportMapperRequest {

    private final List<ConversionStatus> conversionStatuses;
    private final Set<String> verificationIds;
    private final Set<String> productIds;
    private final Set<Long> creativeIds;
    private final Set<Long> resultIds;
    private final Set<Long> ranks;
    private final Set<Long> conversionIds;
    private final List<DeviceType> deviceTypes;
    private final Set<Long> merchantIds;
    private final Set<Long> publisherIds;
    private final long publisherAgencyId;
    private final PublisherType publisherType;
    private final boolean isPendingForMerchantPayment;
    private long staffId;

    /**
     * Constructor for holding the information of a conversion report.
     * Cannot be auto-generated because of the {@code super()} call.
     */
    public ConversionReportExportRequest(String countryCode, ZonedDateTime fromDate,
            ZonedDateTime toDate, Set<Long> siteIds, Set<Long> campaignIds,
            ReportQueryPeriodBase periodBase, List<ConversionStatus> conversionStatuses,
            Set<String> verificationIds, Set<String> productIds, Set<Long> creativeIds,
            Set<Long> resultIds, Set<Long> ranks, List<DeviceType> deviceTypes,
            Set<Long> conversionIds, Set<Long> merchantIds, Set<Long> publisherIds,
            long publisherAgencyId, PublisherType publisherType,
            Boolean isPendingForMerchantPayment) {
        super(countryCode, fromDate, toDate, siteIds, campaignIds, periodBase);
        this.conversionStatuses = conversionStatuses;
        this.verificationIds = verificationIds;
        this.productIds = productIds;
        this.creativeIds = creativeIds;
        this.resultIds = resultIds;
        this.ranks = ranks;
        this.deviceTypes = deviceTypes;
        this.conversionIds = conversionIds;
        this.merchantIds = merchantIds;
        this.publisherIds = publisherIds;
        this.publisherAgencyId = publisherAgencyId;
        this.publisherType = publisherType;
        this.isPendingForMerchantPayment = isPendingForMerchantPayment;
    }

    @Override
    public String getPublisherCountryCode() {
        return null;
    }

    @Override
    public String getMerchantCountryCode() {
        return getCountryCode();
    }
}
