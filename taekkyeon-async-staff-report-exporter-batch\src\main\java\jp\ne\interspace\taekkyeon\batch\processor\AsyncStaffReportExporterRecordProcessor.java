/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.processor;

import java.io.IOException;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.gson.JsonSyntaxException;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionManager;
import org.easybatch.core.processor.RecordProcessor;
import org.easybatch.core.record.Header;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.model.BaseReportExportRequest;
import jp.ne.interspace.taekkyeon.model.ConversionReportExportRequest;
import jp.ne.interspace.taekkyeon.model.ConversionReportRowCount;
import jp.ne.interspace.taekkyeon.model.ReportExportItem;
import jp.ne.interspace.taekkyeon.model.ReportExportPagedDetails;
import jp.ne.interspace.taekkyeon.model.ReportExportRecord;
import jp.ne.interspace.taekkyeon.model.ReportExportRecordPayload;
import jp.ne.interspace.taekkyeon.model.ReportExportRequest;
import jp.ne.interspace.taekkyeon.model.ReportExportType;
import jp.ne.interspace.taekkyeon.model.SqsRecord;
import jp.ne.interspace.taekkyeon.model.SqsRecordPayload;
import jp.ne.interspace.taekkyeon.module.OracleResolver;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.MyBatisSessionManagerRepository;
import jp.ne.interspace.taekkyeon.service.BaseReportCsvExporter;
import jp.ne.interspace.taekkyeon.service.BaseReportExporter;
import jp.ne.interspace.taekkyeon.service.FileService;
import jp.ne.interspace.taekkyeon.service.JsonSerializerService;

import static com.google.common.base.Strings.isNullOrEmpty;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.CONVERSION_REPORT_REACH_LIMIT;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.CSV_FILE_EXTENSION;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.ENABLE_MULTIPLE_DATABASES;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.TARGET_COUNTRY;
import static jp.ne.interspace.taekkyeon.model.ReportExportType.CONVERSION;
import static jp.ne.interspace.taekkyeon.module.AsyncStaffReportExporterModule.BIND_IS_TEST;
import static jp.ne.interspace.taekkyeon.module.AsyncStaffReportExporterModule.BIND_KEY_CHECK_REPORT_ROWS_LIMIT;
import static jp.ne.interspace.taekkyeon.module.AsyncStaffReportExporterModule.BIND_KEY_REPORT_ROWS_NUMBER_LIMIT;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static lombok.AccessLevel.PACKAGE;
import static org.apache.ibatis.session.TransactionIsolationLevel.READ_COMMITTED;

/**
 * {@link RecordProcessor} implementation transforming {@link SqsRecord}s to
 * {@link ReportExportRecord}s.
 *
 * <AUTHOR> Mayur
 */
@Slf4j
@Singleton
public class AsyncStaffReportExporterRecordProcessor
        implements RecordProcessor<SqsRecord, ReportExportRecord> {

    private static final String DEFAULT_LOCALE = "en";

    private static final String REPORT_EXPORT_PREFIX = getCurrentCountry().getCode() + "-async-report-exporter-";

    @Inject
    private JsonSerializerService jsonSerializerService;

    @Inject
    private Map<ReportExportType, Class<? extends BaseReportExportRequest>> reportTypeMapping;

    @Inject
    private Map<Class<? extends BaseReportExportRequest>,
            BaseReportExporter<? extends BaseReportExportRequest,
                    ? extends ReportExportItem, ?>> reportExporterMapping;

    @Inject
    private Map<Class<? extends BaseReportExportRequest>,
            BaseReportCsvExporter<? extends ReportExportItem>> csvExporterMapping;

    @Inject
    private FileService fileService;

    @Inject(optional = true) @OracleResolver
    private SqlSessionManager sqlSessionManager;

    @Inject(optional = true)
    private MyBatisSessionManagerRepository sessionManagerRepository;

    @Inject @Named(BIND_KEY_CHECK_REPORT_ROWS_LIMIT) @Getter(PACKAGE)
    private boolean isCheckReportRowsLimit;

    @Inject @Named(BIND_KEY_REPORT_ROWS_NUMBER_LIMIT) @Getter(PACKAGE)
    private int reportRowsNumberLimit;

    @Inject @Named(BIND_IS_TEST) @Getter(PACKAGE)
    private boolean isTest;

    @Override
    public ReportExportRecord processRecord(SqsRecord record) throws Exception {
        return parse(record);
    }

    private ReportExportRecord parse(SqsRecord record) {

        fileService.deleteMatchingFilesInFolder(
                REPORT_EXPORT_PREFIX);
        SqsRecordPayload sourcePayload = record.getPayload();

        try {
            ReportExportRequest parsedRequest = jsonSerializerService
                    .fromJson(sourcePayload.getMessage(), ReportExportRequest.class);

            ReportExportType type = parsedRequest.getType();
            BaseReportExportRequest request = jsonSerializerService
                    .fromJson(parsedRequest.getConditions(), reportTypeMapping.get(type));
            Header header = record.getHeader();
            String fileName = REPORT_EXPORT_PREFIX + fileService
                    .createFileName(type.name(), request.getCountryCode(),
                            header.getCreationDate().toInstant()
                                    .atZone(ZoneId.systemDefault()).toLocalDate()
                                    .toString());
            String locale = getLocaleBy(parsedRequest.getLocale());

            writeDataToFile(fileName, request, locale);
            if (isCheckReportRowsLimit() && type == CONVERSION && ConversionReportRowCount
                    .getRowCount() > getReportRowsNumberLimit()) {
                fileName = CONVERSION_REPORT_REACH_LIMIT;
            }

            return new ReportExportRecord(header,
                    new ReportExportRecordPayload(sourcePayload.getReceiptHandle(),
                            type.name(), request.getCountryCode(), fileName,
                            parsedRequest.getStaffId(), parsedRequest.getMerchantId(),
                            parsedRequest.getPublisherId(), locale));

        } catch (JsonSyntaxException ex) {
            getLogger().error("Failed to parse conversion: " + sourcePayload.getMessage(),
                    ex);
            throw ex;
        }
    }

    @SuppressWarnings("unchecked")
    @VisibleForTesting
    void writeDataToFile(String fileName, BaseReportExportRequest request, String locale) {
        SqlSessionManager sessionManager = getSqlSessionManager();
        List<String> fileNames = new ArrayList<>();
        try {
            fileNames.add(fileName);
            BaseReportCsvExporter<ReportExportItem> csvExporter = getCsvExporter(request);
            BaseReportExporter<BaseReportExportRequest, ReportExportItem, Object>
                    reportExporter = getReportExporter(request);
            setupSession(sessionManager);
            request = reportExporter.updateRequestBy(request);
            writeHeaderToFile(csvExporter, request, fileName, locale);
            if (shouldSkipProcessing(request)) {
                return;
            }
            processReportDataInPages(csvExporter, reportExporter, request, fileName, fileNames);
        } catch (Exception e) {
            handleExportFailure(e, fileNames);
        } finally {
            closeAndReopenSession(sessionManager);
        }
    }

    @VisibleForTesting
    SqlSessionManager getSqlSessionManager() {
        return ENABLE_MULTIPLE_DATABASES
                ? sessionManagerRepository.getSessionManagerOf(TARGET_COUNTRY)
                : sqlSessionManager;
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }

    @VisibleForTesting
    String getLocaleBy(String locale) {
        return isNullOrEmpty(locale) ? DEFAULT_LOCALE : locale;
    }

    @SuppressWarnings("unchecked")
    private BaseReportCsvExporter<ReportExportItem>
            getCsvExporter(BaseReportExportRequest request) {
        return (BaseReportCsvExporter<ReportExportItem>) csvExporterMapping.get(request.getClass());
    }

    @SuppressWarnings("unchecked")
    private BaseReportExporter<BaseReportExportRequest, ReportExportItem, Object>
            getReportExporter(BaseReportExportRequest request) {
        return (BaseReportExporter<BaseReportExportRequest, ReportExportItem, Object>)
                reportExporterMapping.get(request.getClass());
    }

    private void setupSession(SqlSessionManager sessionManager) {
        if (!sessionManager.isManagedSessionStarted()) {
            sessionManager.startManagedSession(READ_COMMITTED);
        }
    }

    private boolean shouldSkipProcessing(BaseReportExportRequest request) {
        return isCheckReportRowsLimit()
                && request instanceof ConversionReportExportRequest
                && ConversionReportRowCount.getRowCount() > getReportRowsNumberLimit();
    }

    private void writeHeaderToFile(
            BaseReportCsvExporter<ReportExportItem> csvExporter,
            BaseReportExportRequest request,
            String fileName,
            String locale) throws IOException {
        List<String> customFields = findCustomFields(request);
        csvExporter.writeHeader(customFields, fileName,
                request.getPeriodBase(), locale, request.getSiteIds());
    }

    private List<String> findCustomFields(BaseReportExportRequest request) {
        BaseReportExporter<BaseReportExportRequest, ReportExportItem, Object> reportExporter =
                getReportExporter(request);
        return reportExporter.findCustomFields(request).stream()
                .distinct().collect(Collectors.toList());
    }

    private void processReportDataInPages(
            BaseReportCsvExporter<ReportExportItem> csvExporter,
            BaseReportExporter<BaseReportExportRequest, ReportExportItem, Object> reportExporter,
            BaseReportExportRequest request,
            String originalFileName,
            List<String> fileNames) throws IOException {
        String fileName = originalFileName;
        String originFileName = originalFileName;
        int fileIndex = 1;
        Object paginationData = null;
        boolean hasData;
        do {
            ReportExportPagedDetails<ReportExportItem, Object> pagedDetails =
                    reportExporter.findReportData(request, paginationData);
            List<ReportExportItem> reports = pagedDetails.getReportItems();
            paginationData = pagedDetails.getPaginationData();

            if (!reports.isEmpty()) {
                if (request instanceof ConversionReportExportRequest
                        && !isTest()) {
                    fileName = createNewFileName(originFileName, fileIndex++);
                    fileNames.add(fileName);
                }
                csvExporter.writeData(reports,
                        request.getPeriodBase(), fileName, request.getSiteIds());
                hasData = true;
            } else {
                hasData = false;
            }
            reports.clear();
            pagedDetails = null;
        } while (hasData);
    }

    private String createNewFileName(String originFileName, int fileIndex) {
        return originFileName.replace(CSV_FILE_EXTENSION, "_" + fileIndex + CSV_FILE_EXTENSION);
    }

    private void handleExportFailure(Exception exception, List<String> fileNames) {
        getLogger().error("Failed to process CSV file ", exception);
        try {
            deleteGeneratedFiles(fileNames);
        } catch (IOException ex) {
            getLogger().error("Failed to delete CSV file on temp folder ", ex);
        }
    }

    private void deleteGeneratedFiles(List<String> fileNames) throws IOException {
        for (String fileToDelete : fileNames) {
            fileService.deleteFile(fileToDelete);
        }
        getLogger().info("----Deleted CSV file on temp folder----");
    }

    private void closeAndReopenSession(SqlSessionManager sessionManager) {
        sessionManager.close();
        sessionManager.startManagedSession(READ_COMMITTED);
    }
}
