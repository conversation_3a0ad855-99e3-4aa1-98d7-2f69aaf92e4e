/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.reader;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import com.amazonaws.services.sqs.model.Message;
import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageResult;

import org.easybatch.core.record.Header;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.SqsRecord;
import jp.ne.interspace.taekkyeon.persist.aws.sqs.SimpleQueueServiceQueue;

import static java.time.ZoneOffset.UTC;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link SqsRecordReader}.
 *
 * <AUTHOR> Varga
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class SqsRecordReaderTest {

    private static final LocalDateTime CURRENT_TIME = LocalDateTime.of(2017, 12, 6, 15,
            41, 0);
    private static final LocalDateTime END_TIME = LocalDateTime.of(2017, 12, 6, 15, 41,
            42);

    private static final String QUEUE_URL = "queueUrl";
    private static final String RECEIPT_HANDLE = "receiptHandle";
    private static final String MESSAGE_BODY = "messageBody";

    private static final int MAX_NUMBER_OF_MESSAGES = 10;

    private static final int MAX_EXECUTION_SECONDS = 42;
    private static final int VISIBILITY_TIMEOUT_SECONDS = 60;
    private static final int WAIT_TIME_SECONDS = 5;

    @Spy
    private SqsRecordReader underTest;

    @Mock
    private SimpleQueueServiceQueue sourceQueue;

    @Test
    public void testOpenShouldInitializeReaderStateWhenCalled() throws Exception {
        // given
        doReturn(CURRENT_TIME).when(underTest).getCurrentTime();
        doReturn(MAX_EXECUTION_SECONDS).when(underTest).getMaxExecutionSeconds();
        doReturn(sourceQueue).when(underTest).getSourceQueue();

        when(sourceQueue.getQueueUrl()).thenReturn(QUEUE_URL);

        // when
        underTest.open();

        // then
        assertEquals(END_TIME, underTest.getEndTime());
        assertEquals(0, underTest.getCurrentRecordNumber());
        assertEquals(QUEUE_URL, underTest.getQueueUrl());
    }

    @Test
    public void testReadRecordShouldReturnCorrectRecordWhenEndTimeIsNotReachedYetAndMessageIsAvailable()
            throws Exception {
        // given
        doReturn(CURRENT_TIME).when(underTest).getCurrentTime();
        doReturn(END_TIME).when(underTest).getEndTime();

        Message message = new Message().withReceiptHandle(RECEIPT_HANDLE)
                .withBody(MESSAGE_BODY);
        doReturn(message).when(underTest).receiveMessage();

        long currentRecordNumber = 1;
        doReturn(currentRecordNumber).when(underTest).getCurrentRecordNumber();

        Header expectedHeader = new Header(currentRecordNumber, null,
                Date.from(CURRENT_TIME.toInstant(UTC)));

        // when
        SqsRecord actual = underTest.readRecord();

        // then
        assertNotNull(actual);
        assertFields(expectedHeader, actual.getHeader());

        assertNotNull(actual.getPayload());
        assertEquals(RECEIPT_HANDLE, actual.getPayload().getReceiptHandle());
        assertEquals(MESSAGE_BODY, actual.getPayload().getMessage());
    }

    @Test
    public void testReadRecordShouldReturnNullWhenEndTimeIsNotReachedYetAndNoMessageIsAvailable()
            throws Exception {
        // given
        doReturn(CURRENT_TIME).when(underTest).getCurrentTime();
        doReturn(END_TIME).when(underTest).getEndTime();
        doReturn(null).when(underTest).receiveMessage();

        // when
        SqsRecord actual = underTest.readRecord();

        // then
        assertNull(actual);
    }

    @Test
    public void testReadRecordShouldReturnNullWithoutReadingSqsWhenEndTimeIsReached()
            throws Exception {
        // given
        doReturn(END_TIME).when(underTest).getCurrentTime();
        doReturn(END_TIME).when(underTest).getEndTime();
        doReturn(null).when(underTest).receiveMessage();
        doReturn(sourceQueue).when(underTest).getSourceQueue();

        // when
        SqsRecord actual = underTest.readRecord();

        // then
        assertNull(actual);
        verifyZeroInteractions(sourceQueue);
        verify(underTest, never()).receiveMessage();
    }

    @Test
    public void testReceiveMessageShouldReturnCorrectMessageFromCacheWhenCacheContainsMessage() {
        // given
        Message message = new Message();
        List<Message> cachedMessages = new LinkedList<>();
        cachedMessages.add(message);
        doReturn(cachedMessages).when(underTest).getCachedMessages();
        doReturn(sourceQueue).when(underTest).getSourceQueue();

        // when
        Message actual = underTest.receiveMessage();

        // then
        assertSame(message, actual);
        assertTrue(cachedMessages.isEmpty());
        verifyZeroInteractions(sourceQueue);
    }

    @Test
    public void testReceiveMessageShouldReturnCorrectMessageFromSqsQueueWhenCacheIsEmptyAndAMessageIsAvailableInSqs() {
        // given
        Message message1 = new Message();
        Message message2 = new Message();
        List<Message> cachedMessages = new LinkedList<>();
        doReturn(cachedMessages).when(underTest).getCachedMessages();

        doReturn(QUEUE_URL).when(underTest).getQueueUrl();
        doReturn(VISIBILITY_TIMEOUT_SECONDS).when(underTest)
                .getVisibilityTimeoutSeconds();
        doReturn(WAIT_TIME_SECONDS).when(underTest).getWaitTimeSeconds();
        doReturn(sourceQueue).when(underTest).getSourceQueue();

        ArgumentCaptor<ReceiveMessageRequest> requestCaptor = ArgumentCaptor
                .forClass(ReceiveMessageRequest.class);
        ReceiveMessageResult expectedResult = new ReceiveMessageResult()
                .withMessages(message1, message2);
        when(sourceQueue.receiveMessage(requestCaptor.capture()))
                .thenReturn(expectedResult);

        // when
        Message actual = underTest.receiveMessage();

        // then
        assertSame(message1, actual);
        assertEquals(1, cachedMessages.size());
        assertSame(message2, cachedMessages.get(0));

        assertFields(requestCaptor.getValue());
    }

    @Test
    public void testReceiveMessageShouldReturnNullWhenCacheIsEmptyAndNoMessageIsAvailableInSqs() {
        // given
        List<Message> cachedMessages = new LinkedList<>();
        doReturn(cachedMessages).when(underTest).getCachedMessages();

        doReturn(QUEUE_URL).when(underTest).getQueueUrl();
        doReturn(VISIBILITY_TIMEOUT_SECONDS).when(underTest)
                .getVisibilityTimeoutSeconds();
        doReturn(WAIT_TIME_SECONDS).when(underTest).getWaitTimeSeconds();
        doReturn(sourceQueue).when(underTest).getSourceQueue();

        ArgumentCaptor<ReceiveMessageRequest> requestCaptor = ArgumentCaptor
                .forClass(ReceiveMessageRequest.class);
        ReceiveMessageResult expectedResult = new ReceiveMessageResult();
        when(sourceQueue.receiveMessage(requestCaptor.capture()))
                .thenReturn(expectedResult);

        // when
        Message actual = underTest.receiveMessage();

        // then
        assertNull(actual);
        assertTrue(cachedMessages.isEmpty());

        assertFields(requestCaptor.getValue());
    }

    private void assertFields(Header expected, Header actual) {
        assertNotNull(actual);
        assertEquals(expected.getNumber(), actual.getNumber());
        assertEquals(expected.getSource(), actual.getSource());
        assertEquals(expected.getCreationDate(), actual.getCreationDate());
    }

    private void assertFields(ReceiveMessageRequest actual) {
        assertNotNull(actual);
        assertEquals(QUEUE_URL, actual.getQueueUrl());
        assertEquals(MAX_NUMBER_OF_MESSAGES, actual.getMaxNumberOfMessages().intValue());
        assertEquals(VISIBILITY_TIMEOUT_SECONDS,
                actual.getVisibilityTimeout().intValue());
        assertEquals(WAIT_TIME_SECONDS, actual.getWaitTimeSeconds().intValue());
    }
}
