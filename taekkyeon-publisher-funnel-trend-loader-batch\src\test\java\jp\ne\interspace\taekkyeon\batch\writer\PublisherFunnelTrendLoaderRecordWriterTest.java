/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.writer;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.easybatch.core.record.Batch;
import org.easybatch.core.record.GenericRecord;
import org.easybatch.core.record.Header;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.ApprovedRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.ConversionTrackingFunnelDetails;
import jp.ne.interspace.taekkyeon.model.CreativeTrackingFunnelDetails;
import jp.ne.interspace.taekkyeon.model.FirstApprovedAffiliationDetails;
import jp.ne.interspace.taekkyeon.model.OccurredRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.Pair;
import jp.ne.interspace.taekkyeon.model.PublisherAccountFunnelDetails;
import jp.ne.interspace.taekkyeon.model.PublisherAccountType;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelAccumulatedRewardResult;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelDetails;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelDetailsByTime;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelDetailsPayload;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelTrendLoaderRecordPayload;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelTrendLoaderRequest;
import jp.ne.interspace.taekkyeon.model.Site;
import jp.ne.interspace.taekkyeon.module.Country;
import jp.ne.interspace.taekkyeon.module.Environment;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionLogMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CreativeAccessLogSummaryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PublisherAccountFunnelMapper;
import jp.ne.interspace.taekkyeon.persist.aws.redshift.mapper.PublisherFunnelTrendMapper;
import jp.ne.interspace.taekkyeon.persist.aws.s3.SimpleStorageServiceClient;
import jp.ne.interspace.taekkyeon.persist.aws.sqs.PublisherFunnelTrendQueue;
import jp.ne.interspace.taekkyeon.service.PublisherFunnelTrendAccumulatedRewardService;

import static com.google.common.collect.Sets.newHashSet;
import static java.math.BigDecimal.ZERO;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static jp.ne.interspace.taekkyeon.model.PublisherAccountType.INDIVIDUAL;
import static jp.ne.interspace.taekkyeon.model.PublisherAccountType.LOCAL_COMPANY;
import static jp.ne.interspace.taekkyeon.model.PublisherAccountType.OVERSEAS_COMPANY;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link PublisherFunnelTrendLoaderRecordWriter}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class PublisherFunnelTrendLoaderRecordWriterTest {

    private static final Country COUNTRY = Country.getCurrentCountry();
    private static final Environment ENVIRONMENT = Environment.getCurrentEnvironment();
    private static final String SUCCESS = "success";
    private static final String ERROR = "error";
    private static final String FILE_NAME = "test.csv";
    private static final String BUCKET_NAME = "bucket.name";
    private static final String RECEIPT_HANDLE = "receiptHandle";
    private static final String TEMPORARY_TABLE_KEY = "temporaryTableKey";
    private static final Date CURRENT_DATE = Date.from(Instant.ofEpochSecond(102398182));

    private static final String UTM_SOURCE_1 = "FB";
    private static final String UTM_SOURCE_2 = "GG";
    private static final String UTM_SOURCE_3 = "IS";

    private static final String UTM_MEDIUM_1 = "medium1";
    private static final String UTM_MEDIUM_2 = "medium2";
    private static final String UTM_MEDIUM_3 = "medium3";

    private static final String UTM_CONTENT_1 = "content1";
    private static final String UTM_CONTENT_2 = "content2";
    private static final String UTM_CONTENT_3 = "content3";

    private static final String UTM_CAMPAIGN_1 = "campaign1";
    private static final String UTM_CAMPAIGN_2 = "campaign2";
    private static final String UTM_CAMPAIGN_3 = "campaign3";

    private static final String UTM_TERM_1 = "term1";
    private static final String UTM_TERM_2 = "term2";
    private static final String UTM_TERM_3 = "term3";

    private static final Long REFERRER_ID_1 = 1L;
    private static final Long REFERRER_ID_2 = 2L;
    private static final Long REFERRER_ID_3 = 3L;

    private static final String COUNTRY_VN = "VN";
    private static final String COUNTRY_ID = "ID";
    private static final String COUNTRY_MY = "MY";

    private static final String REGISTRATION_REFERRAL_URL_1 = "http://facebook.com/1";
    private static final String REGISTRATION_REFERRAL_URL_2 = "http://facebook.com/2";
    private static final String REGISTRATION_REFERRAL_URL_3 = "http://facebook.com/3";

    private static final String REGISTRATION_REFERRAL_DOMAIN_1 = "http://facebook1.com";
    private static final String REGISTRATION_REFERRAL_DOMAIN_2 = "http://facebook2.com";
    private static final String REGISTRATION_REFERRAL_DOMAIN_3 = "http://facebook3.com";

    private static final Long PUBLISHER_ID_1 = 1L;
    private static final Long PUBLISHER_ID_2 = 2L;
    private static final Long PUBLISHER_ID_3 = 3L;
    private static final Long SITE_ID_1 = 11L;
    private static final Long SITE_ID_2 = 22L;
    private static final Long SITE_ID_3 = 33L;
    private static final Long SITE_ID_4 = 44L;
    private static final Long SITE_ID_5 = 55L;
    private static final Long SITE_ID_6 = 66L;

    private static final LocalDate REGISTERED_DATE_1 = LocalDate.of(2019, 5, 6);
    private static final LocalDate REGISTERED_DATE_2 = LocalDate.of(2019, 5, 7);
    private static final LocalDate REGISTERED_DATE_3 = LocalDate.of(2019, 5, 8);

    private static final LocalDate ACTIVATED_DATE_2 = LocalDate.of(2019, 6, 8);
    private static final LocalDate ACTIVATED_DATE_3 = LocalDate.of(2019, 7, 8);

    private static final LocalDate FIRST_APPROVED_SITE_DATE_1 = LocalDate.of(2016, 5, 8);
    private static final LocalDate FIRST_APPROVED_SITE_DATE_3 = LocalDate.of(2018, 5, 8);

    private static final LocalDate FIRST_AFFILIATION_DATE_1 = LocalDate.of(2019, 3, 8);
    private static final LocalDate FIRST_AFFILIATION_DATE_2 = LocalDate.of(2019, 3, 9);

    private static final LocalDate FIRST_PAYMENT_DATE_1 = LocalDate.of(2019, 2, 1);
    private static final LocalDate FIRST_PAYMENT_DATE_3 = LocalDate.of(2019, 2, 3);

    private static final LocalDate FIRST_APPROVED_AFFILIATION_DATE_1 = LocalDate.of(2019, 3, 3);
    private static final LocalDate FIRST_APPROVED_AFFILIATION_DATE_2 = LocalDate.of(2019, 2, 3);

    private static final LocalDate FIRST_CONVERSION_DATE_1 = LocalDate.of(2019, 6, 10);
    private static final LocalDate FIRST_CONVERSION_DATE_2 = LocalDate.of(2019, 6, 11);
    private static final LocalDate FIRST_CONVERSION_DATE_5 = LocalDate.of(2019, 6, 14);
    private static final LocalDate FIRST_CONVERSION_DATE_6 = LocalDate.of(2019, 6, 15);
    private static final LocalDateTime LATEST_TIME1 = LocalDateTime.of(2023, 1, 1, 10, 0);

    private static final LocalDate FIRST_APPROVED_CONVERSION_DATE_3 = LocalDate.of(2019, 7, 17);
    private static final LocalDate FIRST_APPROVED_CONVERSION_DATE_4 = LocalDate.of(2019, 7, 18);
    private static final LocalDate FIRST_APPROVED_CONVERSION_DATE_5 = LocalDate.of(2019, 7, 19);
    private static final LocalDate FIRST_APPROVED_CONVERSION_DATE_6 = LocalDate.of(2019, 7, 20);

    private static final LocalDate FIRST_IMPRESSION_OR_CLICK_DATE_1 = LocalDate.of(2019, 7, 20);
    private static final LocalDate FIRST_IMPRESSION_OR_CLICK_DATE_2 = LocalDate.of(2019, 7, 21);
    private static final LocalDate FIRST_IMPRESSION_OR_CLICK_DATE_3 = LocalDate.of(2019, 7, 22);

    private static final BigDecimal SALES_REWARD_1 = new BigDecimal("1.1");
    private static final BigDecimal SALES_REWARD_2 = new BigDecimal("2.2");
    private static final BigDecimal SALES_REWARD_3 = new BigDecimal("3.3");

    private static final BigDecimal TRANSACTION_AMOUNT_REWARD_1 = new BigDecimal("1.1");
    private static final BigDecimal TRANSACTION_AMOUNT_REWARD_2 = new BigDecimal("2.2");
    private static final BigDecimal TRANSACTION_AMOUNT_REWARD_3 = new BigDecimal("3.3");

    private static final BigDecimal AT_COMMISSION_1 = new BigDecimal("1.1");
    private static final BigDecimal AT_COMMISSION_2 = new BigDecimal("2.2");
    private static final BigDecimal AT_COMMISSION_3 = new BigDecimal("3.3");

    private static final BigDecimal MERCHANT_AGENT_COMMISSION_1 = new BigDecimal("1.1");
    private static final BigDecimal MERCHANT_AGENT_COMMISSION_2 = new BigDecimal("2.2");
    private static final BigDecimal MERCHANT_AGENT_COMMISSION_3 = new BigDecimal("3.3");

    private static final BigDecimal PUBLISHER_AGENT_COMMISSION_1 = new BigDecimal("1.1");
    private static final BigDecimal PUBLISHER_AGENT_COMMISSION_2 = new BigDecimal("2.2");
    private static final BigDecimal PUBLISHER_AGENT_COMMISSION_3 = new BigDecimal("3.3");

    private static final String EMAIL_1 = "<EMAIL>";
    private static final String EMAIL_2 = "<EMAIL>";
    private static final String EMAIL_3 = "<EMAIL>";
    private static final Header HEADER = new Header(1L, "2020-03-02T00:00:00.000",
            CURRENT_DATE);
    private static final String CSV_CONTENT = "csvContents";

    @InjectMocks @Spy
    private PublisherFunnelTrendLoaderRecordWriter underTest;

    @Mock
    private PublisherFunnelTrendMapper publisherFunnelTrendMapper;

    @Mock
    private SimpleStorageServiceClient s3Client;

    @Mock
    private PublisherFunnelTrendQueue publisherFunnelTrendQueue;

    @Mock
    private PublisherAccountFunnelMapper publisherAccountFunnelMapper;

    @Mock
    private ConversionLogMapper conversionsMapper;

    @Mock
    private CreativeAccessLogSummaryMapper creativeAccessLogSummariesMapper;

    @Mock
    private PublisherFunnelTrendAccumulatedRewardService accumulatedRewardService;

    @Before
    public void setup() {
        publisherFunnelTrendQueue.setUpForTest();
        publisherFunnelTrendQueue.waitUntilAvailable();
    }

    @After
    public void tearDown() {
        publisherFunnelTrendQueue.tearDownForTestWithoutTestSeedReset();
    }

    @Test
    public void testWriteRecordsShouldCallDeleteAndUpsertWhenPublisherFunnelTrendLoaderRequestsIsNotEmpty()
            throws Exception {
        // given
        String fileName = "publisher-funnel-data-1973-03-31T03:56:22.csv";
        List<PublisherFunnelTrendLoaderRequest> requests =
                createPublisherFunnelTrendLoaderRequests();
        doReturn(TEMPORARY_TABLE_KEY).when(underTest).createTemporaryTableKey();
        PublisherFunnelTrendLoaderRecordPayload payload =
                new PublisherFunnelTrendLoaderRecordPayload(RECEIPT_HANDLE, requests);
        GenericRecord<PublisherFunnelTrendLoaderRecordPayload> records =
                new GenericRecord<>(HEADER, payload);
        Batch batch = new Batch(records);

        // when
        underTest.writeRecords(batch);

        // then
        verify(underTest).safelyUpsertData(COUNTRY, ENVIRONMENT, payload, fileName,
                TEMPORARY_TABLE_KEY);
    }

    @Test
    public void testWriteRecordsShouldDoNothingWhenPublisherFunnelTrendLoaderInputDetailsIsEmpty() throws Exception {
        // given
        List<PublisherFunnelTrendLoaderRequest> requests = emptyList();
        doReturn(TEMPORARY_TABLE_KEY).when(underTest).createTemporaryTableKey();
        PublisherFunnelTrendLoaderRecordPayload payload =
                new PublisherFunnelTrendLoaderRecordPayload(RECEIPT_HANDLE, requests);
        GenericRecord<PublisherFunnelTrendLoaderRecordPayload> records =
                new GenericRecord<>(HEADER, payload);
        Batch batch = new Batch(records);

        // when
        underTest.writeRecords(batch);

        // then
        verify(underTest, never())
                .safelyUpsertData(any(Country.class), any(Environment.class),
                        any(PublisherFunnelTrendLoaderRecordPayload.class), anyString(),
                        anyString());
        verifyZeroInteractions(s3Client);
    }

    @Test
    public void testSafelyUpsertDataShouldProcessSuccessfullyAndCallCorrectMethodsWhenNotOccurredException()
            throws IOException {
        // given
        List<PublisherFunnelTrendLoaderRequest> requests =
                createPublisherFunnelTrendLoaderRequests();
        PublisherFunnelTrendLoaderRecordPayload payload =
                new PublisherFunnelTrendLoaderRecordPayload(RECEIPT_HANDLE, requests);
        PublisherFunnelDetailsPayload publisherFunnelDetailsPayload = mock(
                PublisherFunnelDetailsPayload.class);
        Pair<PublisherFunnelDetailsPayload, List<PublisherFunnelDetailsByTime>> mockResult =
                Pair.of(publisherFunnelDetailsPayload, emptyList());
        doReturn(mockResult).when(underTest)
                .findPublisherFunnelDetails(requests);
        List<PublisherFunnelDetails> publisherFunnelDetails =
                asList(mock(PublisherFunnelDetails.class));
        doReturn(publisherFunnelDetails).when(underTest)
                .createPublisherFunnelDetails(publisherFunnelDetailsPayload, mockResult.getRight());
        doReturn(CSV_CONTENT).when(underTest).convertToCsvContentsFrom(
                publisherFunnelDetails);
        doReturn(BUCKET_NAME).when(underTest).getBucketName();

        // when
        underTest.safelyUpsertData(COUNTRY, ENVIRONMENT, payload, FILE_NAME,
                TEMPORARY_TABLE_KEY);

        // then
        verify(s3Client).putObject(BUCKET_NAME, FILE_NAME, CSV_CONTENT);
        verify(underTest)
                .upsertPublisherFunnelData(COUNTRY, ENVIRONMENT, TEMPORARY_TABLE_KEY,
                        FILE_NAME);
        verify(underTest).move(FILE_NAME, SUCCESS);
        assertFalse(underTest.isError());
    }

    @Test
    public void testSafelyUpsertDataShouldMoveFileToErrorPathWhenOccurredException()
            throws IOException {
        // given
        List<PublisherFunnelTrendLoaderRequest> requests =
                createPublisherFunnelTrendLoaderRequests();
        PublisherFunnelTrendLoaderRecordPayload payload =
                new PublisherFunnelTrendLoaderRecordPayload(RECEIPT_HANDLE, requests);
        PublisherFunnelDetailsPayload publisherFunnelDetailsPayload = mock(
                PublisherFunnelDetailsPayload.class);
        Pair<PublisherFunnelDetailsPayload, List<PublisherFunnelDetailsByTime>> mockResult =
                Pair.of(publisherFunnelDetailsPayload, emptyList());
        doReturn(mockResult).when(underTest)
                .findPublisherFunnelDetails(requests);
        List<PublisherFunnelDetails> publisherFunnelDetails =
                asList(mock(PublisherFunnelDetails.class));
        doReturn(publisherFunnelDetails).when(underTest)
                .createPublisherFunnelDetails(publisherFunnelDetailsPayload, mockResult.getRight());
        doReturn(CSV_CONTENT).when(underTest).convertToCsvContentsFrom(
                publisherFunnelDetails);
        RuntimeException exception = new RuntimeException();
        doThrow(exception).when(s3Client).putObject(BUCKET_NAME, FILE_NAME, CSV_CONTENT);
        doReturn(BUCKET_NAME).when(underTest).getBucketName();

        // when
        try {
            underTest.safelyUpsertData(COUNTRY, ENVIRONMENT, payload, FILE_NAME,
                    TEMPORARY_TABLE_KEY);
            fail();
        } catch (Exception e) {

            // then
            verify(s3Client).putObject(BUCKET_NAME, FILE_NAME, CSV_CONTENT);
            verify(underTest, never()).upsertPublisherFunnelData(any(Country.class),
                    any(Environment.class), anyString(), anyString());
            verify(underTest, never()).move(anyString(), eq(SUCCESS));
            verify(underTest).move(FILE_NAME, ERROR);
            assertTrue(underTest.isError());
        }
    }

    @Test
    public void testUpsertPublisherFunnelDataShouldCallCorrectMethodsWhenCalled() {
        // given
        String redshiftCredentials = "redshiftCredentials";
        doReturn(redshiftCredentials).when(underTest).getRedshiftCredentials();
        doReturn(BUCKET_NAME).when(underTest).getBucketName();

        // when
        underTest.upsertPublisherFunnelData(COUNTRY, ENVIRONMENT, TEMPORARY_TABLE_KEY,
                FILE_NAME);

        // then
        verify(publisherFunnelTrendMapper)
                .createTemporarySyncTable(COUNTRY, ENVIRONMENT, TEMPORARY_TABLE_KEY);
        verify(publisherFunnelTrendMapper)
                .copy(COUNTRY, ENVIRONMENT, "s3://bucket.name/test.csv",
                        redshiftCredentials, TEMPORARY_TABLE_KEY);
        verify(publisherFunnelTrendMapper)
                .updateDataByTemporaryTable(COUNTRY, ENVIRONMENT, TEMPORARY_TABLE_KEY);
        verify(publisherFunnelTrendMapper)
                .insertDataByTemporaryTable(COUNTRY, ENVIRONMENT, TEMPORARY_TABLE_KEY);
        verify(publisherFunnelTrendMapper)
                .dropTemporaryTable(COUNTRY, ENVIRONMENT, TEMPORARY_TABLE_KEY);
    }

    @Test
    public void testConvertToCsvContentsFromShouldReturnCorrectCsv() throws Exception {
        // given
        PublisherFunnelDetails publisherFunnelDetails1 = new PublisherFunnelDetails(1L,
                "VN", REFERRER_ID_1, UTM_SOURCE_1, INDIVIDUAL,
                LocalDate.of(2019, 11, 12), LocalDate.of(2019, 1, 12),
                LocalDate.of(2019, 2, 12), LocalDate.of(2019, 2, 12),
                LocalDate.of(2019, 3, 12), LocalDate.of(2019, 3, 12),
                LocalDate.of(2019, 4, 12), LocalDate.of(2019, 4, 12),
                LocalDate.of(2019, 5, 12), new BigDecimal(10), new BigDecimal(20),
                new BigDecimal(30), new BigDecimal(40), new BigDecimal(50),
                new BigDecimal(60), new BigDecimal(70), new BigDecimal(80),
                new BigDecimal(00), new BigDecimal(100), REGISTRATION_REFERRAL_URL_1,
                REGISTRATION_REFERRAL_DOMAIN_1, UTM_MEDIUM_1, UTM_CONTENT_1,
                UTM_CAMPAIGN_1, UTM_TERM_1, EMAIL_1);
        PublisherFunnelDetails publisherFunnelDetails2 = new PublisherFunnelDetails(2L,
                "ID", REFERRER_ID_2, UTM_SOURCE_2, PublisherAccountType.LOCAL_COMPANY,
                LocalDate.of(2019, 12, 15), LocalDate.of(2019, 1, 15),
                LocalDate.of(2019, 2, 15), LocalDate.of(2019, 2, 15),
                LocalDate.of(2019, 3, 15), LocalDate.of(2019, 3, 15),
                LocalDate.of(2019, 4, 15), LocalDate.of(2019, 4, 15),
                LocalDate.of(2019, 5, 15), new BigDecimal(10), new BigDecimal(20),
                new BigDecimal(30), new BigDecimal(40), new BigDecimal(50),
                new BigDecimal(60), new BigDecimal(70), new BigDecimal(80),
                new BigDecimal(00), new BigDecimal(100), REGISTRATION_REFERRAL_URL_2,
                REGISTRATION_REFERRAL_DOMAIN_2, UTM_MEDIUM_2, UTM_CONTENT_2,
                UTM_CAMPAIGN_2, UTM_TERM_2, EMAIL_2);

        // when
        String actual = underTest.convertToCsvContentsFrom(
                Arrays.asList(publisherFunnelDetails1, publisherFunnelDetails2));

        // then
        assertEquals("1,VN,INDIVIDUAL,2019-11-12,2019-01-12,2019-02-12,2019-02-12,"
                        + "2019-03-12,2019-03-12,2019-04-12,2019-04-12,2019-05-12,1,FB,"
                        + "http://facebook.com/1,http://facebook1.com,"
                        + "10,20,30,40,50,60,70,80,0,100,"
                        + "medium1,content1,campaign1,term1,<EMAIL>"
                        + "\r\n"
                        + "2,ID,LOCAL_COMPANY,2019-12-15,2019-01-15,2019-02-15,2019-02-15,"
                        + "2019-03-15,2019-03-15,2019-04-15,2019-04-15,2019-05-15,2,GG,"
                        + "http://facebook.com/2,http://facebook2.com,"
                        + "10,20,30,40,50,60,70,80,0,100,"
                        + "medium2,content2,campaign2,term2,<EMAIL>"
                        + "\r\n",
                actual);
    }

    @Test
    public void testCreatePublisherFunnelDetailsShouldReturnCorrectDataWhenCalled() {
        // given
        Map<Long, List<Long>> publisherIdsAndActivatedSiteIds = new HashMap<>();
        List<Long> siteIds1 = asList(SITE_ID_1, SITE_ID_2);
        List<Long> siteIds2 = asList(SITE_ID_3, SITE_ID_4);
        List<Long> siteIds3 = asList(SITE_ID_5, SITE_ID_6);
        publisherIdsAndActivatedSiteIds.put(PUBLISHER_ID_1, siteIds1);
        publisherIdsAndActivatedSiteIds.put(PUBLISHER_ID_2, siteIds2);
        publisherIdsAndActivatedSiteIds.put(PUBLISHER_ID_3, siteIds3);
        Map<Long, PublisherAccountFunnelDetails> publisherAccountFunnelDetails = new HashMap<>();
        PublisherAccountFunnelDetails publisherAccountFunnelDetails1 =
                new PublisherAccountFunnelDetails(
                        COUNTRY_VN, PUBLISHER_ID_1, REFERRER_ID_1, UTM_SOURCE_1, UTM_MEDIUM_1,
                        UTM_CONTENT_1, UTM_CAMPAIGN_1, UTM_TERM_1, INDIVIDUAL, REGISTERED_DATE_1,
                        null, FIRST_APPROVED_SITE_DATE_1, FIRST_AFFILIATION_DATE_1,
                        FIRST_PAYMENT_DATE_1, REGISTRATION_REFERRAL_URL_1, EMAIL_1);
        publisherAccountFunnelDetails.put(PUBLISHER_ID_1, publisherAccountFunnelDetails1);
        PublisherAccountFunnelDetails publisherAccountFunnelDetails2 =
                new PublisherAccountFunnelDetails(
                        COUNTRY_ID, PUBLISHER_ID_2, REFERRER_ID_2, UTM_SOURCE_2, UTM_MEDIUM_2,
                        UTM_CONTENT_2, UTM_CAMPAIGN_2, UTM_TERM_2, LOCAL_COMPANY,
                        REGISTERED_DATE_2, ACTIVATED_DATE_2, null, FIRST_AFFILIATION_DATE_2, null,
                        REGISTRATION_REFERRAL_URL_2, EMAIL_2);
        publisherAccountFunnelDetails.put(PUBLISHER_ID_2, publisherAccountFunnelDetails2);
        PublisherAccountFunnelDetails publisherAccountFunnelDetails3 =
                new PublisherAccountFunnelDetails(
                        COUNTRY_MY, PUBLISHER_ID_3, REFERRER_ID_3, UTM_SOURCE_3, UTM_MEDIUM_3,
                        UTM_CONTENT_3, UTM_CAMPAIGN_3, UTM_TERM_3, OVERSEAS_COMPANY,
                        REGISTERED_DATE_3, ACTIVATED_DATE_3, FIRST_APPROVED_SITE_DATE_3, null,
                        FIRST_PAYMENT_DATE_3, REGISTRATION_REFERRAL_URL_3, EMAIL_3);
        publisherAccountFunnelDetails.put(PUBLISHER_ID_3, publisherAccountFunnelDetails3);

        Map<Long, FirstApprovedAffiliationDetails> firstApprovedAffiliationDetails =
                new HashMap<>();
        FirstApprovedAffiliationDetails firstApprovedAffiliationDetails1 =
                new FirstApprovedAffiliationDetails(PUBLISHER_ID_1,
                        FIRST_APPROVED_AFFILIATION_DATE_1);
        firstApprovedAffiliationDetails.put(PUBLISHER_ID_1, firstApprovedAffiliationDetails1);
        FirstApprovedAffiliationDetails firstApprovedAffiliationDetails2 =
                new FirstApprovedAffiliationDetails(PUBLISHER_ID_2,
                        FIRST_APPROVED_AFFILIATION_DATE_2);
        firstApprovedAffiliationDetails.put(PUBLISHER_ID_2, firstApprovedAffiliationDetails2);

        Map<Long, ConversionTrackingFunnelDetails> conversionTrackingFunnelDetailsBySiteId =
                new HashMap<>();
        ConversionTrackingFunnelDetails conversionTrackingFunnelDetails1 =
                new ConversionTrackingFunnelDetails(SITE_ID_1, FIRST_CONVERSION_DATE_1,
                        null);
        conversionTrackingFunnelDetailsBySiteId.put(SITE_ID_1, conversionTrackingFunnelDetails1);
        ConversionTrackingFunnelDetails conversionTrackingFunnelDetails2 =
                new ConversionTrackingFunnelDetails(SITE_ID_2, FIRST_CONVERSION_DATE_2,
                        null);
        conversionTrackingFunnelDetailsBySiteId.put(SITE_ID_2, conversionTrackingFunnelDetails2);
        ConversionTrackingFunnelDetails conversionTrackingFunnelDetails3 =
                new ConversionTrackingFunnelDetails(SITE_ID_3, null,
                        FIRST_APPROVED_CONVERSION_DATE_3);
        conversionTrackingFunnelDetailsBySiteId.put(SITE_ID_3, conversionTrackingFunnelDetails3);
        ConversionTrackingFunnelDetails conversionTrackingFunnelDetails4 =
                new ConversionTrackingFunnelDetails(SITE_ID_4, null,
                        FIRST_APPROVED_CONVERSION_DATE_4);
        conversionTrackingFunnelDetailsBySiteId.put(SITE_ID_4, conversionTrackingFunnelDetails4);
        ConversionTrackingFunnelDetails conversionTrackingFunnelDetails5 =
                new ConversionTrackingFunnelDetails(SITE_ID_5, FIRST_CONVERSION_DATE_5,
                        FIRST_APPROVED_CONVERSION_DATE_5);
        conversionTrackingFunnelDetailsBySiteId.put(SITE_ID_5, conversionTrackingFunnelDetails5);
        ConversionTrackingFunnelDetails conversionTrackingFunnelDetails6 =
                new ConversionTrackingFunnelDetails(SITE_ID_6, FIRST_CONVERSION_DATE_6,
                        FIRST_APPROVED_CONVERSION_DATE_6);
        conversionTrackingFunnelDetailsBySiteId.put(SITE_ID_6, conversionTrackingFunnelDetails6);

        Map<Long, CreativeTrackingFunnelDetails> creativeTrackingFunnelDetailsBySiteId =
                new HashMap<>();
        CreativeTrackingFunnelDetails creativeTrackingFunnelDetails1 =
                new CreativeTrackingFunnelDetails(SITE_ID_1,
                        FIRST_IMPRESSION_OR_CLICK_DATE_1);
        creativeTrackingFunnelDetailsBySiteId.put(SITE_ID_1, creativeTrackingFunnelDetails1);
        CreativeTrackingFunnelDetails creativeTrackingFunnelDetails2 =
                new CreativeTrackingFunnelDetails(SITE_ID_2,
                        FIRST_IMPRESSION_OR_CLICK_DATE_2);
        creativeTrackingFunnelDetailsBySiteId.put(SITE_ID_2, creativeTrackingFunnelDetails2);
        CreativeTrackingFunnelDetails creativeTrackingFunnelDetails3 =
                new CreativeTrackingFunnelDetails(SITE_ID_3,
                        FIRST_IMPRESSION_OR_CLICK_DATE_3);
        creativeTrackingFunnelDetailsBySiteId.put(SITE_ID_3, creativeTrackingFunnelDetails3);

        Map<Long, ApprovedRewardFunnelDetails> approvedRewardFunnelDetails = new HashMap<>();
        ApprovedRewardFunnelDetails approvedRewardFunnelDetails1 = new ApprovedRewardFunnelDetails(
                SITE_ID_1, SALES_REWARD_1, TRANSACTION_AMOUNT_REWARD_1, AT_COMMISSION_1,
                MERCHANT_AGENT_COMMISSION_1, PUBLISHER_AGENT_COMMISSION_1);
        approvedRewardFunnelDetails.put(SITE_ID_1, approvedRewardFunnelDetails1);
        ApprovedRewardFunnelDetails approvedRewardFunnelDetails3 = new ApprovedRewardFunnelDetails(
                SITE_ID_3, SALES_REWARD_3, TRANSACTION_AMOUNT_REWARD_3, AT_COMMISSION_3,
                MERCHANT_AGENT_COMMISSION_3, PUBLISHER_AGENT_COMMISSION_3);
        approvedRewardFunnelDetails.put(SITE_ID_3, approvedRewardFunnelDetails3);

        Map<Long, OccurredRewardFunnelDetails> occurredRewardFunnelDetails = new HashMap<>();
        OccurredRewardFunnelDetails occurredRewardFunnelDetails1 = new OccurredRewardFunnelDetails(
                SITE_ID_1, SALES_REWARD_1, TRANSACTION_AMOUNT_REWARD_1, AT_COMMISSION_1,
                MERCHANT_AGENT_COMMISSION_1, PUBLISHER_AGENT_COMMISSION_1);
        occurredRewardFunnelDetails.put(SITE_ID_1, occurredRewardFunnelDetails1);
        OccurredRewardFunnelDetails occurredRewardFunnelDetails2 = new OccurredRewardFunnelDetails(
                SITE_ID_2, SALES_REWARD_2, TRANSACTION_AMOUNT_REWARD_2, AT_COMMISSION_2,
                MERCHANT_AGENT_COMMISSION_2, PUBLISHER_AGENT_COMMISSION_2);
        occurredRewardFunnelDetails.put(SITE_ID_2, occurredRewardFunnelDetails2);
        OccurredRewardFunnelDetails occurredRewardFunnelDetails3 = new OccurredRewardFunnelDetails(
                SITE_ID_3, SALES_REWARD_3, TRANSACTION_AMOUNT_REWARD_3, AT_COMMISSION_3,
                MERCHANT_AGENT_COMMISSION_3, PUBLISHER_AGENT_COMMISSION_3);
        occurredRewardFunnelDetails.put(SITE_ID_3, occurredRewardFunnelDetails3);

        PublisherFunnelDetailsPayload publisherFunnelDetailsPayload =
                new PublisherFunnelDetailsPayload(publisherIdsAndActivatedSiteIds,
                        publisherAccountFunnelDetails, firstApprovedAffiliationDetails,
                        conversionTrackingFunnelDetailsBySiteId,
                        creativeTrackingFunnelDetailsBySiteId,
                        approvedRewardFunnelDetails, occurredRewardFunnelDetails);
        doReturn(REGISTRATION_REFERRAL_DOMAIN_1).when(underTest)
                .getDomainFrom(REGISTRATION_REFERRAL_URL_1);
        doReturn(REGISTRATION_REFERRAL_DOMAIN_2).when(underTest)
                .getDomainFrom(REGISTRATION_REFERRAL_URL_2);
        doReturn(REGISTRATION_REFERRAL_DOMAIN_3).when(underTest)
                .getDomainFrom(REGISTRATION_REFERRAL_URL_3);

        // when
        List<PublisherFunnelDetails> actual = underTest
                .createPublisherFunnelDetails(publisherFunnelDetailsPayload, emptyList());

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        assertFields(actual.get(0), PUBLISHER_ID_1, COUNTRY_VN, INDIVIDUAL,
                REGISTERED_DATE_1, null, FIRST_APPROVED_SITE_DATE_1,
                FIRST_AFFILIATION_DATE_1, FIRST_APPROVED_AFFILIATION_DATE_1,
                FIRST_IMPRESSION_OR_CLICK_DATE_1, FIRST_CONVERSION_DATE_1, null,
                FIRST_PAYMENT_DATE_1, SALES_REWARD_3, TRANSACTION_AMOUNT_REWARD_3,
                AT_COMMISSION_3, MERCHANT_AGENT_COMMISSION_3,
                PUBLISHER_AGENT_COMMISSION_3, SALES_REWARD_1, TRANSACTION_AMOUNT_REWARD_1,
                AT_COMMISSION_1, MERCHANT_AGENT_COMMISSION_1,
                PUBLISHER_AGENT_COMMISSION_1, REGISTRATION_REFERRAL_URL_1,
                REGISTRATION_REFERRAL_DOMAIN_1, EMAIL_1);
        assertFields(actual.get(1), PUBLISHER_ID_2, COUNTRY_ID, LOCAL_COMPANY,
                REGISTERED_DATE_2, ACTIVATED_DATE_2, null, FIRST_AFFILIATION_DATE_2,
                FIRST_APPROVED_AFFILIATION_DATE_2, FIRST_IMPRESSION_OR_CLICK_DATE_3, null,
                FIRST_APPROVED_CONVERSION_DATE_3, null, SALES_REWARD_3,
                TRANSACTION_AMOUNT_REWARD_3, AT_COMMISSION_3, MERCHANT_AGENT_COMMISSION_3,
                PUBLISHER_AGENT_COMMISSION_3, SALES_REWARD_3, TRANSACTION_AMOUNT_REWARD_3,
                AT_COMMISSION_3, MERCHANT_AGENT_COMMISSION_3,
                PUBLISHER_AGENT_COMMISSION_3, REGISTRATION_REFERRAL_URL_2,
                REGISTRATION_REFERRAL_DOMAIN_2, EMAIL_2);
        assertFields(actual.get(2), PUBLISHER_ID_3, COUNTRY_MY, OVERSEAS_COMPANY,
                REGISTERED_DATE_3, ACTIVATED_DATE_3, FIRST_APPROVED_SITE_DATE_3, null,
                null, null, FIRST_CONVERSION_DATE_5, FIRST_APPROVED_CONVERSION_DATE_5,
                FIRST_PAYMENT_DATE_3, ZERO, ZERO, ZERO, ZERO, ZERO, ZERO, ZERO, ZERO,
                ZERO, ZERO, REGISTRATION_REFERRAL_URL_3, REGISTRATION_REFERRAL_DOMAIN_3,
                EMAIL_3);
    }

    @Test
    public void testFindPublisherFunnelDetailsShouldReturnPublisherFunnelDetailsWhenPublisherFunnelTrendLoaderRequestsIsNotEmpty() {
        // given
        List<PublisherFunnelTrendLoaderRequest> requests =
                createPublisherFunnelTrendLoaderRequests();
        List<Long> publisherIds = asList(PUBLISHER_ID_1, PUBLISHER_ID_2);
        List<Long> activatedSiteIds = asList(SITE_ID_1, SITE_ID_2, SITE_ID_3,
                SITE_ID_4);
        Map<Long, List<Long>> publisherIdsAndActivatedSiteIds =
                new HashMap<>();
        publisherIdsAndActivatedSiteIds.put(PUBLISHER_ID_1,
                asList(SITE_ID_1, SITE_ID_2));
        publisherIdsAndActivatedSiteIds.put(PUBLISHER_ID_2,
                asList(SITE_ID_3, SITE_ID_4));

        // when
        Pair<PublisherFunnelDetailsPayload, List<PublisherFunnelDetailsByTime>> result =
                underTest.findPublisherFunnelDetails(requests);
        PublisherFunnelDetailsPayload actual = result.getLeft();

        //then
        assertNotNull(actual);
        assertEquals(publisherIdsAndActivatedSiteIds,
                actual.getPublisherIdsAndActivatedSiteIds());
        verify(conversionsMapper).findConversionTrackingFunnelDetails(activatedSiteIds);
        verify(creativeAccessLogSummariesMapper).findCreativeTrackingFunnelDetails(
                activatedSiteIds);
        verify(conversionsMapper).findApprovedRewardFunnelDetails(activatedSiteIds);
        verify(conversionsMapper).findOccurredRewardFunnelDetails(activatedSiteIds);
        verify(publisherAccountFunnelMapper).findPublisherAccountFunnelDetailsBy(
                publisherIds);
        verify(publisherAccountFunnelMapper).findFirstApprovedAffiliationDetailsBy(
                publisherIds);
    }

    @Test
    public void testFindPublisherFunnelDetailsShouldReturnEmptyPublisherFunnelDetailsWhenPublisherFunnelTrendLoaderRequestsIsEmpty() {
        // given
        List<PublisherFunnelTrendLoaderRequest> requests = emptyList();

        // when
        Pair<PublisherFunnelDetailsPayload, List<PublisherFunnelDetailsByTime>> result =
                underTest.findPublisherFunnelDetails(requests);
        PublisherFunnelDetailsPayload actual = result.getLeft();

        //then
        assertNotNull(actual);
        assertTrue(actual.getPublisherIdsAndActivatedSiteIds().isEmpty());
        assertTrue(actual.getPublisherAccountFunnelDetails().isEmpty());
        assertTrue(actual.getFirstApprovedAffiliationDetails().isEmpty());
        assertTrue(actual.getOccurredRewardFunnelDetails().isEmpty());
        assertTrue(actual.getApprovedRewardFunnelDetails().isEmpty());
        assertTrue(actual.getCreativesTrackingFunnelDetails().isEmpty());
        assertTrue(actual.getConversionsTrackingFunnelDetails().isEmpty());
    }

    @Test
    public void testGetActivatedSiteIdsByShouldReturnActivatedSiteIdsWhenCalled() {
        // given
        List<Long> publisherIds = asList(PUBLISHER_ID_1, PUBLISHER_ID_3);
        List<Site> activatedSiteIds = asList(new Site(PUBLISHER_ID_1, SITE_ID_1),
                new Site(PUBLISHER_ID_3, SITE_ID_3));
        when(publisherAccountFunnelMapper.findActivatedSiteIdsBy(publisherIds))
                .thenReturn(activatedSiteIds);

        // when
        Map<Long, Set<Long>> actual = underTest.getActivatedSiteIdsBy(publisherIds);

        //then
        assertNotNull(actual);
        assertEquals(newHashSet(SITE_ID_1), actual.get(PUBLISHER_ID_1));
        assertEquals(newHashSet(SITE_ID_3), actual.get(PUBLISHER_ID_3));
    }

    @Test
    public void testCreatePublisherFunnelDetailsShouldReturnCorrectResultWhenValidPayloadProvided() {
        // given
        PublisherFunnelDetailsPayload payload = createMockPublisherFunnelDetailsPayload();
        List<PublisherFunnelDetailsByTime> publisherFunnelDetailsByTimes =
                createMockPublisherFunnelDetailsByTimes();

        // when
        List<PublisherFunnelDetails> result = underTest.createPublisherFunnelDetails(
                payload, publisherFunnelDetailsByTimes);

        // then
        assertNotNull(result);
        assertEquals(2, result.size());
        PublisherFunnelDetails firstDetails = result.get(0);
        assertEquals(PUBLISHER_ID_1.longValue(), firstDetails.getAccountId());
        assertEquals("JP", firstDetails.getCountryCode());
        assertEquals(PublisherAccountType.INDIVIDUAL, firstDetails.getAccountType());
        PublisherFunnelDetails secondDetails = result.get(1);
        assertEquals(PUBLISHER_ID_2.longValue(), secondDetails.getAccountId());
        assertEquals("JP", secondDetails.getCountryCode());
        assertEquals(PublisherAccountType.INDIVIDUAL, secondDetails.getAccountType());
    }

    @Test
    public void testCreatePublisherFunnelDetailsShouldReturnEmptyListWhenEmptyPayloadProvided() {
        // given
        PublisherFunnelDetailsPayload emptyPayload = createEmptyPublisherFunnelDetailsPayload();
        List<PublisherFunnelDetailsByTime> publisherFunnelDetailsByTimes =
                createMockPublisherFunnelDetailsByTimes();

        // when
        List<PublisherFunnelDetails> result = underTest.createPublisherFunnelDetails(
                emptyPayload, publisherFunnelDetailsByTimes);

        // then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testCreatePublisherFunnelDetailsShouldHandleNullSiteIds() {
        // given
        PublisherFunnelDetailsPayload payload =
                createMockPublisherFunnelDetailsPayloadWithNullSiteIds();
        List<PublisherFunnelDetailsByTime> publisherFunnelDetailsByTimes =
                createMockPublisherFunnelDetailsByTimes();

        // when
        List<PublisherFunnelDetails> result = underTest.createPublisherFunnelDetails(
                payload, publisherFunnelDetailsByTimes);

        // then
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    @Test
    public void testFindPublisherFunnelDetailsShouldCallCorrectMappersWhenNoExistingFunnelTrendData() {
        // given
        List<PublisherFunnelTrendLoaderRequest> requests = asList(
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_1, asList(SITE_ID_1, SITE_ID_2)),
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_2, asList(SITE_ID_3, SITE_ID_4))
        );
        List<Long> publisherIds = asList(PUBLISHER_ID_1, PUBLISHER_ID_2);
        List<Long> activatedSiteIds = asList(SITE_ID_1, SITE_ID_2, SITE_ID_3, SITE_ID_4);

        when(publisherAccountFunnelMapper.findPublisherFunnelDetailsByTime(publisherIds))
                .thenReturn(emptyList());
        when(publisherAccountFunnelMapper.findPublisherAccountFunnelDetailsBy(publisherIds))
                .thenReturn(emptyMap());
        when(publisherAccountFunnelMapper.findFirstApprovedAffiliationDetailsBy(publisherIds))
                .thenReturn(emptyMap());
        when(conversionsMapper.findConversionTrackingFunnelDetails(activatedSiteIds))
                .thenReturn(emptyMap());
        when(creativeAccessLogSummariesMapper.findCreativeTrackingFunnelDetails(activatedSiteIds))
                .thenReturn(emptyMap());
        when(conversionsMapper.findApprovedRewardFunnelDetails(activatedSiteIds))
                .thenReturn(emptyMap());
        when(conversionsMapper.findOccurredRewardFunnelDetails(activatedSiteIds))
                .thenReturn(emptyMap());

        // when
        Pair<PublisherFunnelDetailsPayload, List<PublisherFunnelDetailsByTime>> result =
                underTest.findPublisherFunnelDetails(requests);
        PublisherFunnelDetailsPayload actual = result.getLeft();

        // then
        assertNotNull(actual);
        assertTrue(result.getRight().isEmpty());
        verify(publisherAccountFunnelMapper).findPublisherFunnelDetailsByTime(publisherIds);
        verify(publisherAccountFunnelMapper).findPublisherAccountFunnelDetailsBy(publisherIds);
        verify(publisherAccountFunnelMapper).findFirstApprovedAffiliationDetailsBy(publisherIds);
        verify(conversionsMapper).findConversionTrackingFunnelDetails(activatedSiteIds);
        verify(creativeAccessLogSummariesMapper)
                .findCreativeTrackingFunnelDetails(activatedSiteIds);
        verify(conversionsMapper).findApprovedRewardFunnelDetails(activatedSiteIds);
        verify(conversionsMapper).findOccurredRewardFunnelDetails(activatedSiteIds);
        verify(conversionsMapper, never()).findApprovedRewardFunnelDetailsWithTimeRange(
                anyList(), any(LocalDateTime.class), any(LocalDateTime.class));
        verify(conversionsMapper, never()).findOccurredRewardFunnelDetailsWithTimeRange(
                anyList(), any(LocalDateTime.class), any(LocalDateTime.class));
    }

    @Test
    public void testFindPublisherFunnelDetailsShouldHandleMixedScenarioWithExistingAndNewPublishers() {
        // given
        List<PublisherFunnelTrendLoaderRequest> requests = asList(
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_1, asList(SITE_ID_1, SITE_ID_2)),
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_2, asList(SITE_ID_3, SITE_ID_4)),
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_3, asList(SITE_ID_5, SITE_ID_6))
        );
        List<Long> allPublisherIds = asList(PUBLISHER_ID_1, PUBLISHER_ID_2, PUBLISHER_ID_3);
        List<Long> newPublisherIds = asList(PUBLISHER_ID_3);
        List<Long> newActivatedSiteIds = asList(SITE_ID_5, SITE_ID_6);

        List<PublisherFunnelDetailsByTime> existingDetailsList = asList(
                createMockPublisherFunnelDetailsByTime(PUBLISHER_ID_1),
                createMockPublisherFunnelDetailsByTime(PUBLISHER_ID_2)
        );

        when(publisherAccountFunnelMapper.findPublisherFunnelDetailsByTime(allPublisherIds))
                .thenReturn(existingDetailsList);
        when(publisherAccountFunnelMapper.findPublisherAccountFunnelDetailsBy(newPublisherIds))
                .thenReturn(emptyMap());
        when(publisherAccountFunnelMapper.findFirstApprovedAffiliationDetailsBy(newPublisherIds))
                .thenReturn(emptyMap());
        when(conversionsMapper.findConversionTrackingFunnelDetails(newActivatedSiteIds))
                .thenReturn(emptyMap());
        when(creativeAccessLogSummariesMapper
                .findCreativeTrackingFunnelDetails(newActivatedSiteIds))
                .thenReturn(emptyMap());
        when(conversionsMapper.findApprovedRewardFunnelDetails(newActivatedSiteIds))
                .thenReturn(emptyMap());
        when(conversionsMapper.findOccurredRewardFunnelDetails(newActivatedSiteIds))
                .thenReturn(emptyMap());

        // Mock accumulatedRewardService
        PublisherFunnelAccumulatedRewardResult mockResult =
                new PublisherFunnelAccumulatedRewardResult(emptyMap(), emptyMap());
        when(accumulatedRewardService.collectDataForAccountIdsWithTimeRange(
                eq(existingDetailsList), any(), any(), any())).thenReturn(mockResult);

        // when
        Pair<PublisherFunnelDetailsPayload, List<PublisherFunnelDetailsByTime>> result =
                underTest.findPublisherFunnelDetails(requests);
        PublisherFunnelDetailsPayload actual = result.getLeft();

        // then
        assertNotNull(actual);
        assertEquals(existingDetailsList, result.getRight());
        verify(publisherAccountFunnelMapper).findPublisherAccountFunnelDetailsBy(newPublisherIds);
        verify(publisherAccountFunnelMapper).findFirstApprovedAffiliationDetailsBy(newPublisherIds);
        verify(conversionsMapper).findConversionTrackingFunnelDetails(newActivatedSiteIds);
        verify(creativeAccessLogSummariesMapper)
                .findCreativeTrackingFunnelDetails(newActivatedSiteIds);
        verify(conversionsMapper).findApprovedRewardFunnelDetails(newActivatedSiteIds);
        verify(conversionsMapper).findOccurredRewardFunnelDetails(newActivatedSiteIds);
        verify(accumulatedRewardService).collectDataForAccountIdsWithTimeRange(
                eq(existingDetailsList), any(), any(), any());
    }

    @Test
    public void testFindPublisherFunnelDetailsShouldNotCallConversionMappersWhenActivatedSiteIdsIsEmpty() {
        // given
        List<PublisherFunnelTrendLoaderRequest> requests = asList(
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_1, emptyList()),
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_2, emptyList())
        );
        List<Long> publisherIds = asList(PUBLISHER_ID_1, PUBLISHER_ID_2);

        when(publisherAccountFunnelMapper.findPublisherFunnelDetailsByTime(publisherIds))
                .thenReturn(emptyList());
        when(publisherAccountFunnelMapper.findPublisherAccountFunnelDetailsBy(publisherIds))
                .thenReturn(emptyMap());
        when(publisherAccountFunnelMapper.findFirstApprovedAffiliationDetailsBy(publisherIds))
                .thenReturn(emptyMap());

        // when
        Pair<PublisherFunnelDetailsPayload, List<PublisherFunnelDetailsByTime>> result =
                underTest.findPublisherFunnelDetails(requests);
        PublisherFunnelDetailsPayload actual = result.getLeft();

        // then
        assertNotNull(actual);
        assertTrue(result.getRight().isEmpty());
        verify(publisherAccountFunnelMapper).findPublisherFunnelDetailsByTime(publisherIds);
        verify(publisherAccountFunnelMapper).findPublisherAccountFunnelDetailsBy(publisherIds);
        verify(publisherAccountFunnelMapper).findFirstApprovedAffiliationDetailsBy(publisherIds);
        verify(conversionsMapper, never()).findConversionTrackingFunnelDetails(anyList());
        verify(creativeAccessLogSummariesMapper, never())
                .findCreativeTrackingFunnelDetails(anyList());
        verify(conversionsMapper, never()).findApprovedRewardFunnelDetails(anyList());
        verify(conversionsMapper, never()).findOccurredRewardFunnelDetails(anyList());
    }

    @Test
    public void testFindPublisherFunnelDetailsShouldHandleDuplicatePublisherIds() {
        // given
        List<PublisherFunnelTrendLoaderRequest> requests = asList(
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_1, asList(SITE_ID_1, SITE_ID_2)),
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_1, asList(SITE_ID_3, SITE_ID_4)),
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_2, asList(SITE_ID_5, SITE_ID_6))
        );
        List<Long> expectedPublisherIds = asList(PUBLISHER_ID_1, PUBLISHER_ID_1, PUBLISHER_ID_2);
        List<Long> expectedActivatedSiteIds = asList(SITE_ID_1, SITE_ID_2,
                SITE_ID_3, SITE_ID_4, SITE_ID_5, SITE_ID_6);

        when(publisherAccountFunnelMapper.findPublisherFunnelDetailsByTime(expectedPublisherIds))
                .thenReturn(emptyList());
        when(publisherAccountFunnelMapper
                .findPublisherAccountFunnelDetailsBy(expectedPublisherIds))
                .thenReturn(emptyMap());
        when(publisherAccountFunnelMapper
                .findFirstApprovedAffiliationDetailsBy(expectedPublisherIds))
                .thenReturn(emptyMap());
        when(conversionsMapper.findConversionTrackingFunnelDetails(expectedActivatedSiteIds))
                .thenReturn(emptyMap());
        when(creativeAccessLogSummariesMapper
                .findCreativeTrackingFunnelDetails(expectedActivatedSiteIds))
                .thenReturn(emptyMap());
        when(conversionsMapper.findApprovedRewardFunnelDetails(expectedActivatedSiteIds))
                .thenReturn(emptyMap());
        when(conversionsMapper.findOccurredRewardFunnelDetails(expectedActivatedSiteIds))
                .thenReturn(emptyMap());

        // when
        Pair<PublisherFunnelDetailsPayload, List<PublisherFunnelDetailsByTime>> result =
                underTest.findPublisherFunnelDetails(requests);
        PublisherFunnelDetailsPayload actual = result.getLeft();

        // then
        assertNotNull(actual);
        assertTrue(result.getRight().isEmpty());
        assertEquals(2, actual.getPublisherIdsAndActivatedSiteIds().size());
        assertEquals(asList(SITE_ID_3, SITE_ID_4), actual
                .getPublisherIdsAndActivatedSiteIds().get(PUBLISHER_ID_1));
        assertEquals(asList(SITE_ID_5, SITE_ID_6), actual
                .getPublisherIdsAndActivatedSiteIds().get(PUBLISHER_ID_2));
    }

    @Test
    public void testFindPublisherFunnelDetailsShouldHandleEmptySiteIdsInRequests() {
        // given
        List<PublisherFunnelTrendLoaderRequest> requests = asList(
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_1, emptyList()),
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_2, asList(SITE_ID_3, SITE_ID_4))
        );
        List<Long> publisherIds = asList(PUBLISHER_ID_1, PUBLISHER_ID_2);
        List<Long> activatedSiteIds = asList(SITE_ID_3, SITE_ID_4);

        when(publisherAccountFunnelMapper.findPublisherFunnelDetailsByTime(publisherIds))
                .thenReturn(emptyList());
        when(publisherAccountFunnelMapper.findPublisherAccountFunnelDetailsBy(publisherIds))
                .thenReturn(emptyMap());
        when(publisherAccountFunnelMapper.findFirstApprovedAffiliationDetailsBy(publisherIds))
                .thenReturn(emptyMap());
        when(conversionsMapper.findConversionTrackingFunnelDetails(activatedSiteIds))
                .thenReturn(emptyMap());
        when(creativeAccessLogSummariesMapper.findCreativeTrackingFunnelDetails(activatedSiteIds))
                .thenReturn(emptyMap());
        when(conversionsMapper.findApprovedRewardFunnelDetails(activatedSiteIds))
                .thenReturn(emptyMap());
        when(conversionsMapper.findOccurredRewardFunnelDetails(activatedSiteIds))
                .thenReturn(emptyMap());

        // when
        Pair<PublisherFunnelDetailsPayload, List<PublisherFunnelDetailsByTime>> result =
                underTest.findPublisherFunnelDetails(requests);
        PublisherFunnelDetailsPayload actual = result.getLeft();

        // then
        assertNotNull(actual);
        assertTrue(result.getRight().isEmpty());
        assertEquals(emptyList(), actual.getPublisherIdsAndActivatedSiteIds().get(PUBLISHER_ID_1));
        assertEquals(asList(SITE_ID_3, SITE_ID_4), actual
                .getPublisherIdsAndActivatedSiteIds().get(PUBLISHER_ID_2));
        verify(conversionsMapper).findConversionTrackingFunnelDetails(activatedSiteIds);
    }

    @Test
    public void testFindPublisherFunnelDetailsShouldPopulatePayloadCorrectly() {
        // given
        List<PublisherFunnelTrendLoaderRequest> requests = asList(
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_1, asList(SITE_ID_1, SITE_ID_2))
        );
        List<Long> publisherIds = asList(PUBLISHER_ID_1);
        List<Long> activatedSiteIds = asList(SITE_ID_1, SITE_ID_2);

        Map<Long, PublisherAccountFunnelDetails> mockAccountDetails = new HashMap<>();
        mockAccountDetails.put(PUBLISHER_ID_1, createMockPublisherAccountFunnelDetails1());

        Map<Long, FirstApprovedAffiliationDetails> mockAffiliationDetails = new HashMap<>();
        mockAffiliationDetails.put(PUBLISHER_ID_1, new FirstApprovedAffiliationDetails(
                PUBLISHER_ID_1, FIRST_APPROVED_AFFILIATION_DATE_1));

        Map<Long, ConversionTrackingFunnelDetails> mockConversionDetails = new HashMap<>();
        mockConversionDetails.put(SITE_ID_1, new ConversionTrackingFunnelDetails(
                SITE_ID_1, FIRST_CONVERSION_DATE_1, null));

        Map<Long, CreativeTrackingFunnelDetails> mockCreativeDetails = new HashMap<>();
        mockCreativeDetails.put(SITE_ID_1, new CreativeTrackingFunnelDetails(
                SITE_ID_1, FIRST_IMPRESSION_OR_CLICK_DATE_1));

        Map<Long, ApprovedRewardFunnelDetails> mockApprovedRewardDetails = new HashMap<>();
        mockApprovedRewardDetails.put(SITE_ID_1, new ApprovedRewardFunnelDetails(
                SITE_ID_1, SALES_REWARD_1, TRANSACTION_AMOUNT_REWARD_1, AT_COMMISSION_1,
                MERCHANT_AGENT_COMMISSION_1, PUBLISHER_AGENT_COMMISSION_1));

        Map<Long, OccurredRewardFunnelDetails> mockOccurredRewardDetails = new HashMap<>();
        mockOccurredRewardDetails.put(SITE_ID_1, new OccurredRewardFunnelDetails(
                SITE_ID_1, SALES_REWARD_1, TRANSACTION_AMOUNT_REWARD_1, AT_COMMISSION_1,
                MERCHANT_AGENT_COMMISSION_1, PUBLISHER_AGENT_COMMISSION_1));

        when(publisherAccountFunnelMapper.findPublisherFunnelDetailsByTime(publisherIds))
                .thenReturn(emptyList());
        when(publisherAccountFunnelMapper.findPublisherAccountFunnelDetailsBy(publisherIds))
                .thenReturn(mockAccountDetails);
        when(publisherAccountFunnelMapper.findFirstApprovedAffiliationDetailsBy(publisherIds))
                .thenReturn(mockAffiliationDetails);
        when(conversionsMapper.findConversionTrackingFunnelDetails(activatedSiteIds))
                .thenReturn(mockConversionDetails);
        when(creativeAccessLogSummariesMapper.findCreativeTrackingFunnelDetails(activatedSiteIds))
                .thenReturn(mockCreativeDetails);
        when(conversionsMapper.findApprovedRewardFunnelDetails(activatedSiteIds))
                .thenReturn(mockApprovedRewardDetails);
        when(conversionsMapper.findOccurredRewardFunnelDetails(activatedSiteIds))
                .thenReturn(mockOccurredRewardDetails);

        // when
        Pair<PublisherFunnelDetailsPayload, List<PublisherFunnelDetailsByTime>> result =
                underTest.findPublisherFunnelDetails(requests);
        PublisherFunnelDetailsPayload actual = result.getLeft();

        // then
        assertNotNull(actual);
        assertTrue(result.getRight().isEmpty());
        assertEquals(asList(SITE_ID_1, SITE_ID_2), actual
                .getPublisherIdsAndActivatedSiteIds().get(PUBLISHER_ID_1));
        assertEquals(mockAccountDetails, actual.getPublisherAccountFunnelDetails());
        assertEquals(mockAffiliationDetails, actual.getFirstApprovedAffiliationDetails());
        assertEquals(mockConversionDetails, actual.getConversionsTrackingFunnelDetails());
        assertEquals(mockCreativeDetails, actual.getCreativesTrackingFunnelDetails());
        assertEquals(mockApprovedRewardDetails, actual.getApprovedRewardFunnelDetails());
        assertEquals(mockOccurredRewardDetails, actual.getOccurredRewardFunnelDetails());
    }

    @Test
    public void testUpsertOraclePublisherFunnelTrendShouldCallMapperWhenValidDataProvided() {
        // given
        List<PublisherFunnelDetails> publisherFunnelDetails =
                createMockPublisherFunnelDetailsList();
        LocalDateTime targetTimeEnd = LocalDateTime.of(2025, 1, 15, 12, 0);
        doReturn(targetTimeEnd).when(underTest).getTargetTimeEnd();

        // when
        underTest.upsertOraclePublisherFunnelTrend(publisherFunnelDetails);

        // then
        verify(publisherAccountFunnelMapper).upsertPublisherFunnelTrendSummary(
                publisherFunnelDetails, targetTimeEnd);
    }

    @Test
    public void testUpsertOraclePublisherFunnelTrendShouldDoNothingWhenEmptyListProvided() {
        // given
        List<PublisherFunnelDetails> emptyList = emptyList();

        // when
        underTest.upsertOraclePublisherFunnelTrend(emptyList);

        // then
        verify(publisherAccountFunnelMapper, never()).upsertPublisherFunnelTrendSummary(
                any(), any());
    }

    @Test
    public void testUpsertOraclePublisherFunnelTrendShouldHandleLargeDataSet() {
        // given
        List<PublisherFunnelDetails> largeDataSet = createLargePublisherFunnelDetailsList();

        // when
        underTest.upsertOraclePublisherFunnelTrend(largeDataSet);

        // then
        verify(publisherAccountFunnelMapper).upsertPublisherFunnelTrendSummary(
                largeDataSet, underTest.getTargetTimeEnd());
    }

    private PublisherFunnelDetailsPayload createMockPublisherFunnelDetailsPayload() {
        Map<Long, List<Long>> publisherIdsAndActivatedSiteIds = new HashMap<>();
        publisherIdsAndActivatedSiteIds.put(PUBLISHER_ID_1, asList(SITE_ID_1, SITE_ID_2));
        publisherIdsAndActivatedSiteIds.put(PUBLISHER_ID_2, asList(SITE_ID_3, SITE_ID_4));

        Map<Long, PublisherAccountFunnelDetails> publisherAccountFunnelDetails = new HashMap<>();
        publisherAccountFunnelDetails.put(PUBLISHER_ID_1,
                createMockPublisherAccountFunnelDetails1());
        publisherAccountFunnelDetails.put(PUBLISHER_ID_2,
                createMockPublisherAccountFunnelDetails2());

        Map<Long, FirstApprovedAffiliationDetails> firstApprovedAffiliationDetails =
                new HashMap<>();
        firstApprovedAffiliationDetails.put(PUBLISHER_ID_1,
                new FirstApprovedAffiliationDetails(PUBLISHER_ID_1,
                        FIRST_APPROVED_AFFILIATION_DATE_1));
        firstApprovedAffiliationDetails.put(PUBLISHER_ID_2,
                new FirstApprovedAffiliationDetails(PUBLISHER_ID_2,
                        FIRST_APPROVED_AFFILIATION_DATE_2));

        Map<Long, ConversionTrackingFunnelDetails> conversionsTrackingFunnelDetails =
                new HashMap<>();
        conversionsTrackingFunnelDetails.put(SITE_ID_1,
                new ConversionTrackingFunnelDetails(SITE_ID_1,
                        FIRST_CONVERSION_DATE_1, null));
        conversionsTrackingFunnelDetails.put(SITE_ID_3,
                new ConversionTrackingFunnelDetails(SITE_ID_3, null,
                        FIRST_APPROVED_CONVERSION_DATE_3));

        Map<Long, CreativeTrackingFunnelDetails> creativesTrackingFunnelDetails = new HashMap<>();
        creativesTrackingFunnelDetails.put(SITE_ID_1,
                new CreativeTrackingFunnelDetails(SITE_ID_1, FIRST_IMPRESSION_OR_CLICK_DATE_1));
        creativesTrackingFunnelDetails.put(SITE_ID_3,
                new CreativeTrackingFunnelDetails(SITE_ID_3, FIRST_IMPRESSION_OR_CLICK_DATE_3));

        Map<Long, ApprovedRewardFunnelDetails> approvedRewardFunnelDetails = new HashMap<>();
        approvedRewardFunnelDetails.put(SITE_ID_1,
                new ApprovedRewardFunnelDetails(SITE_ID_1, SALES_REWARD_1,
                        TRANSACTION_AMOUNT_REWARD_1, AT_COMMISSION_1,
                        MERCHANT_AGENT_COMMISSION_1, PUBLISHER_AGENT_COMMISSION_1));

        Map<Long, OccurredRewardFunnelDetails> occurredRewardFunnelDetails = new HashMap<>();
        occurredRewardFunnelDetails.put(SITE_ID_1,
                new OccurredRewardFunnelDetails(SITE_ID_1, SALES_REWARD_1,
                        TRANSACTION_AMOUNT_REWARD_1, AT_COMMISSION_1,
                        MERCHANT_AGENT_COMMISSION_1, PUBLISHER_AGENT_COMMISSION_1));

        return new PublisherFunnelDetailsPayload(publisherIdsAndActivatedSiteIds,
                publisherAccountFunnelDetails, firstApprovedAffiliationDetails,
                conversionsTrackingFunnelDetails, creativesTrackingFunnelDetails,
                approvedRewardFunnelDetails, occurredRewardFunnelDetails);
    }

    private PublisherAccountFunnelDetails createMockPublisherAccountFunnelDetails1() {
        return new PublisherAccountFunnelDetails(
                "JP", PUBLISHER_ID_1, REFERRER_ID_1, UTM_SOURCE_1, UTM_MEDIUM_1,
                UTM_CONTENT_1, UTM_CAMPAIGN_1, UTM_TERM_1, INDIVIDUAL, REGISTERED_DATE_1,
                null, FIRST_APPROVED_SITE_DATE_1, FIRST_AFFILIATION_DATE_1,
                FIRST_PAYMENT_DATE_1, REGISTRATION_REFERRAL_URL_1, EMAIL_1);
    }

    private PublisherAccountFunnelDetails createMockPublisherAccountFunnelDetails2() {
        return new PublisherAccountFunnelDetails(
                "JP", PUBLISHER_ID_2, REFERRER_ID_2, UTM_SOURCE_2, UTM_MEDIUM_2,
                UTM_CONTENT_2, UTM_CAMPAIGN_2, UTM_TERM_2, INDIVIDUAL, REGISTERED_DATE_2,
                ACTIVATED_DATE_2, null, FIRST_AFFILIATION_DATE_2, null,
                REGISTRATION_REFERRAL_URL_2, EMAIL_2);
    }

    private List<PublisherFunnelDetailsByTime> createMockPublisherFunnelDetailsByTimes() {
        return emptyList();
    }

    private PublisherFunnelDetailsPayload createEmptyPublisherFunnelDetailsPayload() {
        return new PublisherFunnelDetailsPayload(emptyMap(), emptyMap(), emptyMap(),
                emptyMap(), emptyMap(), emptyMap(), emptyMap());
    }

    private PublisherFunnelDetailsPayload createMockPublisherFunnelDetailsPayloadWithNullSiteIds() {
        Map<Long, List<Long>> publisherIdsAndActivatedSiteIds = new HashMap<>();
        publisherIdsAndActivatedSiteIds.put(PUBLISHER_ID_1, emptyList());
        publisherIdsAndActivatedSiteIds.put(PUBLISHER_ID_2, emptyList());

        Map<Long, PublisherAccountFunnelDetails> publisherAccountFunnelDetails = new HashMap<>();
        publisherAccountFunnelDetails.put(PUBLISHER_ID_1,
                createMockPublisherAccountFunnelDetails1());
        publisherAccountFunnelDetails.put(PUBLISHER_ID_2,
                createMockPublisherAccountFunnelDetails2());

        return new PublisherFunnelDetailsPayload(publisherIdsAndActivatedSiteIds,
                publisherAccountFunnelDetails, emptyMap(), emptyMap(), emptyMap(),
                emptyMap(), emptyMap());
    }

    private List<PublisherFunnelDetails> createMockPublisherFunnelDetailsList() {
        PublisherFunnelDetails details1 = new PublisherFunnelDetails(PUBLISHER_ID_1,
                "JP", REFERRER_ID_1, UTM_SOURCE_1, INDIVIDUAL,
                REGISTERED_DATE_1, null, FIRST_APPROVED_SITE_DATE_1, FIRST_AFFILIATION_DATE_1,
                FIRST_APPROVED_AFFILIATION_DATE_1, FIRST_IMPRESSION_OR_CLICK_DATE_1,
                FIRST_CONVERSION_DATE_1, null, FIRST_PAYMENT_DATE_1,
                SALES_REWARD_1, TRANSACTION_AMOUNT_REWARD_1, AT_COMMISSION_1,
                MERCHANT_AGENT_COMMISSION_1, PUBLISHER_AGENT_COMMISSION_1,
                SALES_REWARD_1, TRANSACTION_AMOUNT_REWARD_1, AT_COMMISSION_1,
                MERCHANT_AGENT_COMMISSION_1, PUBLISHER_AGENT_COMMISSION_1,
                REGISTRATION_REFERRAL_URL_1, REGISTRATION_REFERRAL_DOMAIN_1,
                UTM_MEDIUM_1, UTM_CONTENT_1, UTM_CAMPAIGN_1, UTM_TERM_1, EMAIL_1);

        PublisherFunnelDetails details2 = new PublisherFunnelDetails(PUBLISHER_ID_2,
                "JP", REFERRER_ID_2, UTM_SOURCE_2, INDIVIDUAL,
                REGISTERED_DATE_2, ACTIVATED_DATE_2, null, FIRST_AFFILIATION_DATE_2,
                FIRST_APPROVED_AFFILIATION_DATE_2, FIRST_IMPRESSION_OR_CLICK_DATE_2,
                FIRST_CONVERSION_DATE_2, null, null,
                SALES_REWARD_2, TRANSACTION_AMOUNT_REWARD_2, AT_COMMISSION_2,
                MERCHANT_AGENT_COMMISSION_2, PUBLISHER_AGENT_COMMISSION_2,
                SALES_REWARD_2, TRANSACTION_AMOUNT_REWARD_2, AT_COMMISSION_2,
                MERCHANT_AGENT_COMMISSION_2, PUBLISHER_AGENT_COMMISSION_2,
                REGISTRATION_REFERRAL_URL_2, REGISTRATION_REFERRAL_DOMAIN_2,
                UTM_MEDIUM_2, UTM_CONTENT_2, UTM_CAMPAIGN_2, UTM_TERM_2, EMAIL_2);

        return asList(details1, details2);
    }

    private List<PublisherFunnelDetails> createLargePublisherFunnelDetailsList() {
        List<PublisherFunnelDetails> largeList = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            PublisherFunnelDetails details = new PublisherFunnelDetails((long) i,
                    "JP", REFERRER_ID_1, UTM_SOURCE_1, INDIVIDUAL,
                    REGISTERED_DATE_1, null, FIRST_APPROVED_SITE_DATE_1, FIRST_AFFILIATION_DATE_1,
                    FIRST_APPROVED_AFFILIATION_DATE_1, FIRST_IMPRESSION_OR_CLICK_DATE_1,
                    FIRST_CONVERSION_DATE_1, null, FIRST_PAYMENT_DATE_1,
                    SALES_REWARD_1, TRANSACTION_AMOUNT_REWARD_1, AT_COMMISSION_1,
                    MERCHANT_AGENT_COMMISSION_1, PUBLISHER_AGENT_COMMISSION_1,
                    SALES_REWARD_1, TRANSACTION_AMOUNT_REWARD_1, AT_COMMISSION_1,
                    MERCHANT_AGENT_COMMISSION_1, PUBLISHER_AGENT_COMMISSION_1,
                    REGISTRATION_REFERRAL_URL_1, REGISTRATION_REFERRAL_DOMAIN_1,
                    UTM_MEDIUM_1, UTM_CONTENT_1, UTM_CAMPAIGN_1, UTM_TERM_1, EMAIL_1);
            largeList.add(details);
        }
        return largeList;
    }

    private List<PublisherFunnelTrendLoaderRequest> createPublisherFunnelTrendLoaderRequests() {
        PublisherFunnelTrendLoaderRequest request1 =
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_1,
                        asList(SITE_ID_1, SITE_ID_2));
        PublisherFunnelTrendLoaderRequest request2 =
                new PublisherFunnelTrendLoaderRequest(PUBLISHER_ID_2,
                        asList(SITE_ID_3, SITE_ID_4));
        return asList(request1, request2);
    }

    private void assertFields(PublisherFunnelDetails actual,
            long expectedAccountId, String expectedCountryCode,
            PublisherAccountType expectedAccountType, LocalDate expectedRegisteredDate,
            LocalDate expectedActivatedDate, LocalDate expectedFirstApprovedSiteDate,
            LocalDate expectedFirstAffiliationDate,
            LocalDate expectedFirstApproveAffiliationDate,
            LocalDate expectedFirstImpressionOrClickDate,
            LocalDate expectedFirstConversionDate,
            LocalDate expectedFirstApprovedConversionDate,
            LocalDate expectedFirstPaymentDate, BigDecimal expectedOccurredSalesReward,
            BigDecimal expectedOccurredTransactionAmountReward,
            BigDecimal expectedOccurredAtCommission,
            BigDecimal expectedOccurredMerchantAgentCommission,
            BigDecimal expectedOccurredPublisherAgentCommission,
            BigDecimal expectedApprovedSalesReward,
            BigDecimal expectedApprovedTransactionAmountReward,
            BigDecimal expectedApprovedAtCommission,
            BigDecimal expectedApprovedMerchantAgentCommission,
            BigDecimal expectedApprovedPublisherAgentCommission,
            String expectedRegistrationReferralUrl,
            String expectedRegistrationReferralDomain,
            String expectedEmail) {
        assertEquals(expectedAccountId, actual.getAccountId());
        assertEquals(expectedCountryCode, actual.getCountryCode());
        assertEquals(expectedAccountType, actual.getAccountType());
        assertEquals(expectedRegisteredDate, actual.getRegisteredDate());
        assertEquals(expectedActivatedDate, actual.getActivatedDate());
        assertEquals(expectedFirstApprovedSiteDate, actual.getFirstApprovedSiteDate());
        assertEquals(expectedFirstAffiliationDate, actual.getFirstAffiliationDate());
        assertEquals(expectedFirstApproveAffiliationDate,
                actual.getFirstApproveAffiliationDate());
        assertEquals(expectedFirstImpressionOrClickDate,
                actual.getFirstImpressionOrClickDate());
        assertEquals(expectedFirstConversionDate, actual.getFirstConversionDate());
        assertEquals(expectedFirstApprovedConversionDate,
                actual.getFirstApprovedConversionDate());
        assertEquals(expectedFirstPaymentDate, actual.getFirstPaymentDate());
        assertEquals(0,
                expectedOccurredSalesReward.compareTo(actual.getOccurredSalesReward()));
        assertEquals(0, expectedOccurredTransactionAmountReward
                .compareTo(actual.getOccurredTransactionAmountReward()));
        assertEquals(0,
                expectedOccurredAtCommission.compareTo(actual.getOccurredAtCommission()));
        assertEquals(0, expectedOccurredMerchantAgentCommission
                .compareTo(actual.getOccurredMerchantAgentCommission()));
        assertEquals(0, expectedOccurredPublisherAgentCommission
                .compareTo(actual.getOccurredPublisherAgentCommission()));
        assertEquals(0,
                expectedApprovedSalesReward.compareTo(actual.getApprovedSalesReward()));
        assertEquals(0, expectedApprovedTransactionAmountReward
                .compareTo(actual.getApprovedTransactionAmountReward()));
        assertEquals(0,
                expectedApprovedAtCommission.compareTo(actual.getApprovedAtCommission()));
        assertEquals(0, expectedApprovedMerchantAgentCommission
                .compareTo(actual.getApprovedMerchantAgentCommission()));
        assertEquals(0, expectedApprovedPublisherAgentCommission
                .compareTo(actual.getApprovedPublisherAgentCommission()));
        assertEquals(expectedRegistrationReferralUrl, actual.getRegistrationReferralUrl());
        assertEquals(expectedRegistrationReferralDomain, actual.getRegistrationReferralDomain());
        assertEquals(expectedEmail, actual.getEmail());
    }

    private PublisherFunnelDetailsByTime createMockPublisherFunnelDetailsByTime(Long publisherId) {
        return new PublisherFunnelDetailsByTime(
                publisherId, "JP", REFERRER_ID_1, UTM_SOURCE_1, INDIVIDUAL,
                REGISTERED_DATE_1, null, FIRST_APPROVED_SITE_DATE_1, FIRST_AFFILIATION_DATE_1,
                FIRST_APPROVED_AFFILIATION_DATE_1, FIRST_IMPRESSION_OR_CLICK_DATE_1,
                FIRST_CONVERSION_DATE_1, null, FIRST_PAYMENT_DATE_1,
                SALES_REWARD_1, TRANSACTION_AMOUNT_REWARD_1, AT_COMMISSION_1,
                MERCHANT_AGENT_COMMISSION_1, PUBLISHER_AGENT_COMMISSION_1,
                SALES_REWARD_1, TRANSACTION_AMOUNT_REWARD_1, AT_COMMISSION_1,
                MERCHANT_AGENT_COMMISSION_1, PUBLISHER_AGENT_COMMISSION_1,
                REGISTRATION_REFERRAL_URL_1, REGISTRATION_REFERRAL_DOMAIN_1,
                UTM_MEDIUM_1, UTM_CONTENT_1, UTM_CAMPAIGN_1, UTM_TERM_1, EMAIL_1, LATEST_TIME1);
    }
}
