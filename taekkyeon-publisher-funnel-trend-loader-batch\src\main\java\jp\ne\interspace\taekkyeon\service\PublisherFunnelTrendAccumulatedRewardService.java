/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.model.ApprovedRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.OccurredRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelAccumulatedRewardResult;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelDetailsByTime;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionLogMapper;

/**
 * Service for gathering reward funnel details with time range conditions.
 *
 * <AUTHOR> Tran
 */
public class PublisherFunnelTrendAccumulatedRewardService {

    @Inject
    private ConversionLogMapper conversionsMapper;

    /**
    * Collects data for publisher funnel details by account IDs and time range.
    *
    * @param publisherFunnelDetailsByTimes List of publisher funnel details by time.
    * @param siteIdsByLatestTime Map of latest time to list of site IDs.
    * @param accountIdsAndActivatedSiteIds Map of account IDs to list of activated site IDs.
    * @param targetTimeEnd Target time end.
    * @return PublisherFunnelAccumulatedRewardResult containing approved and occurred rewards.
    */
    public PublisherFunnelAccumulatedRewardResult collectDataForAccountIdsWithTimeRange(
            List<PublisherFunnelDetailsByTime> publisherFunnelDetailsByTimes,
            Map<LocalDateTime, List<Long>> siteIdsByLatestTime,
            Map<Long, List<Long>> accountIdsAndActivatedSiteIds,
            LocalDateTime targetTimeEnd) {

        Map<Long, ApprovedRewardFunnelDetails> allApprovedRewards = new HashMap<>();
        Map<Long, OccurredRewardFunnelDetails> allOccurredRewards = new HashMap<>();

        for (Map.Entry<LocalDateTime, List<Long>> entry : siteIdsByLatestTime.entrySet()) {
            LocalDateTime targetTimeFrom = entry.getKey();

            Map<Long, ApprovedRewardFunnelDetails> timeRangeApprovedRewards =
                    conversionsMapper.findApprovedRewardFunnelDetailsWithTimeRange(
                            entry.getValue(), targetTimeFrom, targetTimeEnd);
            Map<Long, OccurredRewardFunnelDetails> timeRangeOccurredRewards =
                    conversionsMapper.findOccurredRewardFunnelDetailsWithTimeRange(
                            entry.getValue(), targetTimeFrom, targetTimeEnd);

            allApprovedRewards.putAll(timeRangeApprovedRewards);
            allOccurredRewards.putAll(timeRangeOccurredRewards);
        }
        rewardsAccumulate(publisherFunnelDetailsByTimes,
                accountIdsAndActivatedSiteIds, allApprovedRewards, allOccurredRewards);


        return new PublisherFunnelAccumulatedRewardResult(allApprovedRewards, allOccurredRewards);
    }

    private void rewardsAccumulate(
            List<PublisherFunnelDetailsByTime> publisherFunnelDetailsByTimes,
            Map<Long, List<Long>> accountIdsAndActivatedSiteIds,
            Map<Long, ApprovedRewardFunnelDetails> allApprovedRewards,
            Map<Long, OccurredRewardFunnelDetails> allOccurredRewards) {

        allApprovedRewards.forEach((siteId, approvedReward) -> findDetailsForSite(
                siteId, accountIdsAndActivatedSiteIds, publisherFunnelDetailsByTimes)
                .ifPresent(details -> updateApprovedReward(approvedReward, details)));

        allOccurredRewards.forEach((siteId, occurredReward) -> findDetailsForSite(
                siteId, accountIdsAndActivatedSiteIds, publisherFunnelDetailsByTimes)
                .ifPresent(details -> updateOccurredReward(occurredReward, details)));
    }

    private Optional<PublisherFunnelDetailsByTime> findDetailsForSite(
            Long siteId,
            Map<Long, List<Long>> accountIdsAndActivatedSiteIds,
            List<PublisherFunnelDetailsByTime> publisherFunnelDetailsByTimes) {

        Long accountId = findAccountIdForSiteId(siteId, accountIdsAndActivatedSiteIds);
        if (accountId == null) {
            return Optional.empty();
        }

        return publisherFunnelDetailsByTimes.stream()
                .filter(details -> details.getAccountId() == accountId)
                .findFirst();
    }

    private void updateApprovedReward(ApprovedRewardFunnelDetails reward,
            PublisherFunnelDetailsByTime details) {
        reward.setApprovedSalesReward(
                reward.getApprovedSalesReward()
                        .add(details.getApprovedSalesReward()));
        reward.setApprovedTransactionAmountReward(
                reward.getApprovedTransactionAmountReward()
                        .add(details.getApprovedTransactionAmountReward()));
        reward.setApprovedAtCommission(
                reward.getApprovedAtCommission()
                        .add(details.getApprovedAtCommission()));
        reward.setApprovedMerchantAgentCommission(
                reward.getApprovedMerchantAgentCommission()
                        .add(details.getApprovedMerchantAgentCommission()));
        reward.setApprovedPublisherAgentCommission(
                reward.getApprovedPublisherAgentCommission()
                        .add(details.getApprovedPublisherAgentCommission()));
    }

    private void updateOccurredReward(OccurredRewardFunnelDetails reward,
            PublisherFunnelDetailsByTime details) {
        reward.setOccurredSalesReward(
                reward.getOccurredSalesReward()
                        .add(details.getOccurredSalesReward()));
        reward.setOccurredTransactionAmountReward(
                reward.getOccurredTransactionAmountReward()
                        .add(details.getOccurredTransactionAmountReward()));
        reward.setOccurredAtCommission(
                reward.getOccurredAtCommission()
                        .add(details.getOccurredAtCommission()));
        reward.setOccurredMerchantAgentCommission(
                reward.getOccurredMerchantAgentCommission()
                        .add(details.getOccurredMerchantAgentCommission()));
        reward.setOccurredPublisherAgentCommission(
                reward.getOccurredPublisherAgentCommission()
                        .add(details.getOccurredPublisherAgentCommission()));
    }

    private Long findAccountIdForSiteId(Long siteId,
            Map<Long, List<Long>> publisherIdsAndActivatedSiteIds) {
        return publisherIdsAndActivatedSiteIds.entrySet().stream()
                .filter(entry -> entry.getValue().contains(siteId))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);
    }
}
