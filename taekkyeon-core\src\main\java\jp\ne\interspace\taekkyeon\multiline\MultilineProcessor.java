/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.multiline;

import java.util.Set;

import javax.annotation.processing.AbstractProcessor;
import javax.annotation.processing.ProcessingEnvironment;
import javax.annotation.processing.RoundEnvironment;
import javax.annotation.processing.SupportedAnnotationTypes;
import javax.annotation.processing.SupportedSourceVersion;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.TypeElement;

import com.sun.tools.javac.model.JavacElements;
import com.sun.tools.javac.processing.JavacProcessingEnvironment;
import com.sun.tools.javac.tree.JCTree;
import com.sun.tools.javac.tree.TreeMaker;

/**
 * Annotation processor for multiline string variable declaration.
 *
 * <AUTHOR> OBS DEV Team
 */
@SupportedAnnotationTypes({ "jp.ne.interspace.taekkyeon.multiline.Multiline" })
@SupportedSourceVersion(SourceVersion.RELEASE_8)
public final class MultilineProcessor extends AbstractProcessor {

    private JavacElements elementUtils;
    private TreeMaker maker;

    @Override
    public void init(final ProcessingEnvironment procEnv) {
        super.init(procEnv);

        JavacProcessingEnvironment javacProcessingEnv =
                (JavacProcessingEnvironment) procEnv;
        this.elementUtils = javacProcessingEnv.getElementUtils();
        this.maker = TreeMaker.instance(javacProcessingEnv.getContext());
    }

    @Override
    public boolean process(final Set<? extends TypeElement> annotations,
            final RoundEnvironment roundEnv) {

        roundEnv.getElementsAnnotatedWith(Multiline.class).forEach(field -> {
            String docComment = elementUtils.getDocComment(field);
            if (null != docComment) {
                JCTree.JCVariableDecl fieldNode =
                        (JCTree.JCVariableDecl) elementUtils.getTree(field);
                fieldNode.init = maker.Literal(docComment);
            }
        });
        return true;
    }
}
