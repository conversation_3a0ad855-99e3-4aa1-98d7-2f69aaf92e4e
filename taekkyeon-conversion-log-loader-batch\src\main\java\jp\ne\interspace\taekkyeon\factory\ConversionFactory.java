/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.factory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.DeviceOs;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.LogItem;
import jp.ne.interspace.taekkyeon.service.AdvancedLogItemParserService;
import jp.ne.interspace.taekkyeon.service.ConversionRequestParserService;
import jp.ne.interspace.taekkyeon.service.ConversionService;
import jp.ne.interspace.taekkyeon.validator.ConversionValidator;

import static java.math.BigDecimal.ZERO;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.COMMA;

/**
 * {@link LogItemFactory} implementation for {@link Conversion}s.
 *
 * <AUTHOR> Shin
 */
@Singleton
public class ConversionFactory implements LogItemFactory {

    private static final int INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID = 3;
    private static final int INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID = 30;

    @Inject
    private AdvancedLogItemParserService parser;

    @Inject
    private ConversionRequestParserService requestParser;

    @Inject
    private ConversionValidator conversionValidator;

    @Inject
    private ConversionService conversionService;

    @Override
    public LogItem createFrom(String rawLogItem) {
        Map<String, String> parsedLogItem = parser.parse(rawLogItem);
        String request = parser.getRequestFrom(parsedLogItem);
        Map<String, String> parsedRequest = requestParser.parseRequest(request);
        long campaignId = parser.getCampaignIdFrom(parsedLogItem);
        int resultId = requestParser.getResultIdFrom(parsedRequest);
        conversionValidator.validateResultId(resultId, campaignId);
        BigDecimal price = getPriceFrom(parser.getTransactionAmountFrom(parsedLogItem),
                requestParser.getPriceFrom(parsedRequest));
        LocalDateTime logDate = parser.getLogDateFrom(parsedLogItem);
        long creativeId = parser.getCreativeIdFrom(parsedLogItem);
        long siteId = parser.getSiteIdFrom(parsedLogItem);
        DeviceType deviceType = parser.getDeviceTypeFrom(parsedLogItem);
        String ipAddress = parser.getIpAddressFrom(parsedLogItem);
        String referer = parser.getRefererFrom(parsedLogItem);
        String userAgent = parser.getUserAgentFrom(parsedLogItem);
        LocalDateTime clickDate = parser.getClickDateFrom(parsedLogItem);
        LocalDateTime conversionDate = parser.getConversionDateFrom(parsedLogItem);
        String identifier = parser.getIdentifierFrom(parsedLogItem);
        String sessionId = parser.getSessionIdFrom(parsedLogItem);
        String uuid = parser.getUuidFrom(parsedLogItem);
        long quantity = parser.getQuantityFrom(parsedLogItem, resultId);
        String productId = parser.getProductIdFrom(parsedLogItem);
        String categoryId = parser.getCategoryIdFrom(parsedLogItem, resultId);
        DeviceOs deviceOs = parser.getDeviceOsFrom(userAgent);
        BigDecimal discount = parser.getDiscountFrom(parsedLogItem);
        BigDecimal unitPrice = parser.getUnitPriceFrom(parsedLogItem, resultId);
        BigDecimal originalTotalPrice = getOriginalTotalPrice(
                parser.getOriginalTotalPriceFrom(parsedLogItem), price, unitPrice,
                resultId);
        String pointbackId = parser.getPointbackIdFrom(parsedLogItem);
        String clickReferer = parser.getClickRefererFrom(parsedLogItem);
        String clickUrl = parser.getClickUrlFrom(parsedLogItem);
        String clickUserAgent = parser.getClickUserAgentFrom(parsedLogItem);
        String customerType = parser.getCustomerTypeFrom(parsedLogItem);
        String language = parser.getLanguageFrom(parsedLogItem);
        String clickIpAddress = getClientIpFrom(
                parser.getClickIpAddressFrom(parsedLogItem));
        String currency = parser.getCurrencyFrom(parsedLogItem);
        String atDuplicatedProduct = parser.getAtDuplicatedProduct(parsedLogItem);

        Map<String, String> conversionParameters = parser
                .getConversionParametersFrom(parsedLogItem);
        Map<String, String> clickParameters = parser
                .getClickParametersFrom(parsedLogItem);
        List<String> transactionIds = conversionService
                .generateTransactionIdsFrom(quantity, conversionDate, identifier,
                        productId, resultId, campaignId, customerType,
                        atDuplicatedProduct);

        return new Conversion(logDate, creativeId, siteId, campaignId, deviceType,
                deviceOs, ipAddress, referer, userAgent, clickDate, conversionDate,
                sessionId, uuid, identifier, resultId, quantity, price, unitPrice,
                originalTotalPrice, discount, productId, categoryId, pointbackId,
                clickReferer, clickUrl, clickUserAgent, conversionParameters,
                clickParameters, transactionIds, customerType, language, clickIpAddress,
                currency);
    }

    @VisibleForTesting
    boolean isMultipleProductConversion(int resultId, long quantity) {
        return isProductOrCategoryReward(resultId) && quantity > 1;
    }

    @VisibleForTesting
    BigDecimal getPriceFrom(BigDecimal transactionAmount, BigDecimal value) {
        BigDecimal price = transactionAmount != null ? transactionAmount : value;
        return price.compareTo(ZERO) > 0 ? price : ZERO;
    }

    @VisibleForTesting
    BigDecimal getOriginalTotalPrice(BigDecimal originalTotalPrice, BigDecimal price,
            BigDecimal unitPrice, int resultId) {
        if (unitPrice.compareTo(ZERO) == 0 && isProductOrCategoryReward(resultId)) {
            return ZERO;
        }
        return originalTotalPrice.compareTo(ZERO) > 0 ? originalTotalPrice : price;
    }

    @VisibleForTesting
    String getClientIpFrom(String ipAddress) {
        if (ipAddress == null) {
            return null;
        }
        List<String> ipAddresses = Arrays.asList(ipAddress.split(COMMA));
        return ipAddresses.get(0).trim();
    }

    private boolean isProductOrCategoryReward(int resultId) {
        return resultId == INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID
                || resultId == INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID;
    }
}
