/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CampaignSettingDuplicationCutDetails;
import jp.ne.interspace.taekkyeon.validator.LogValidator;

import static jp.ne.interspace.taekkyeon.model.DuplicationCutTarget.DAILY;
import static jp.ne.interspace.taekkyeon.model.DuplicationCutTarget.PERMANENT;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ConversionService}.
 *
 * <AUTHOR> Ferreras
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class ConversionServiceTest {

    private static final String TRANSACTION_ID = "TRANSACTION_ID";

    private static final long QUANTITY_VALUE_0 = 0L;
    private static final long QUANTITY_VALUE_1 = 1L;
    private static final long QUANTITY_VALUE_2 = 2L;
    private static final LocalDateTime CONVERSION_DATE_VALUE = LocalDateTime.of(2018, 6,
            15, 11, 22, 33);
    private static final String IDENTIFIER_VALUE = "identifier";
    private static final String PRODUCT_ID_VALUE = "productId";

    private static final int INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID = 3;
    private static final int INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID = 30;

    private static final int TRANSACTION_ID_MAX_BYTE_LENGTH = 512;

    private static final String UNIQUE_KEY_FOR_TRANSACTION_ID = "unique";
    private static final long CAMPAIGN_ID_1 = 1L;
    private static final long CAMPAIGN_ID_3 = 3L;
    private static final String CUSTOMER_TYPE = "customerType";

    private static final String IDENTIFIER = "identifier";
    private static final int RESULT_ID = 3;
    private static final String AT_DUPLICATED_PRODUCT = "1011-2";

    @InjectMocks @Spy
    private ConversionService underTest;

    @Mock
    private CampaignSettingService campaignSettingService;

    @Mock
    private LogValidator logValidator;

    @Test
    public void testCreateInternalTransactionIdByShouldReturnCorrectInternalTransactionIdStartsWithConversionDateWhenDuplicationCutFlagIsEnabledAndTargetIsDaily() {
        // given
        long campaignId = 1L;
        LocalDate conversionDate = LocalDate.of(2017, 10, 15);
        String identifier = "identifier";
        int resultId = 30;
        String permanentTargetInternalTransactionId = "1$identifier$30$cusomterType";
        String expected = "2017-10-15$1$identifier$30$cusomterType";
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                new CampaignSettingDuplicationCutDetails(true, DAILY);
        when(campaignSettingService.findDuplicationCutDetailsBy(campaignId))
                .thenReturn(duplicationCutDetails);
        doReturn(permanentTargetInternalTransactionId).when(underTest)
                .createPermanentTargetInternalTransactionId(campaignId, identifier,
                        resultId, CUSTOMER_TYPE);

        // when
        String actual = underTest.createInternalTransactionIdBy(campaignId,
                conversionDate, identifier, resultId, CUSTOMER_TYPE);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateInternalTransactionIdByShouldReturnCorrectInternalTransactionIdStartsWithoutConversionDateWhenDuplicationCutFlagIsEnabledAndTargetIsPermanent() {
        // given
        long campaignId = 2L;
        LocalDate conversionDate = LocalDate.of(2017, 10, 16);
        String identifier = "permenentIdentifier";
        int resultId = 30;
        String permanentTargetInternalTransactionId = "2$permenentIdentifier$30";
        String expected = "2$permenentIdentifier$30";
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                new CampaignSettingDuplicationCutDetails(true, PERMANENT);
        when(campaignSettingService.findDuplicationCutDetailsBy(campaignId))
                .thenReturn(duplicationCutDetails);
        doReturn(permanentTargetInternalTransactionId).when(underTest)
                .createPermanentTargetInternalTransactionId(campaignId, identifier,
                        resultId, CUSTOMER_TYPE);

        // when
        String actual = underTest.createInternalTransactionIdBy(campaignId,
                conversionDate, identifier, resultId, CUSTOMER_TYPE);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateInternalTransactionIdByShouldReturnCorrectInternalTransactionIdWithRandomStringWhenDuplicationCutFlagIsDisabled() {
        // given
        long campaignId = 1L;
        LocalDate conversionDate = LocalDate.of(2017, 10, 15);
        String identifier = "identifier";
        int resultId = 30;
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                new CampaignSettingDuplicationCutDetails(false, DAILY);
        when(campaignSettingService.findDuplicationCutDetailsBy(campaignId))
                .thenReturn(duplicationCutDetails);

        // when
        String actual = underTest.createInternalTransactionIdBy(campaignId,
                conversionDate, identifier, resultId, CUSTOMER_TYPE);

        // then
        assertEquals(36, actual.length());
        verify(underTest, never()).createPermanentTargetInternalTransactionId(anyLong(),
                anyString(), anyInt(), anyString());
    }

    @Test
    public void testGenerateTransactionIdsFromShouldReturnTransactionIdContainingConversionDateAndIdentifierAndResultIdAndCusomterTypeWhenGivenProductIdIsNullAndTheCreatedTransactionIdIsLessThanTheTransactionIdMaxByteLength() {
        // given
        String productId = null;
        String expectedTransactionId = "2018-06-15 11:22:33-identifier-3-customerType";
        CampaignSettingDuplicationCutDetails duplicationCutDetails = mock(
                CampaignSettingDuplicationCutDetails.class);
        when(duplicationCutDetails.isEnabled()).thenReturn(true);
        when(campaignSettingService.findDuplicationCutDetailsBy(CAMPAIGN_ID_1))
                .thenReturn(duplicationCutDetails);
        doReturn(expectedTransactionId).when(underTest).createTransactionIdPrefix(
                CONVERSION_DATE_VALUE, IDENTIFIER_VALUE,
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CUSTOMER_TYPE);

        // when
        List<String> actual = underTest.generateTransactionIdsFrom(QUANTITY_VALUE_1,
                CONVERSION_DATE_VALUE, IDENTIFIER_VALUE, productId,
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CAMPAIGN_ID_1, CUSTOMER_TYPE);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertEquals(expectedTransactionId, actual.get(0));

        verify(logValidator).validateMaxByteCountOf(expectedTransactionId,
                TRANSACTION_ID_MAX_BYTE_LENGTH, TRANSACTION_ID);
    }

    @Test
    public void testGenerateTransactionIdsFromShouldReturnCorrectValueWhenGivenProductIdIsEmptyAndTheCreatedTransactionIdIsLessThanTheTransactionIdMaxByteLength() {
        // given
        String productId = "";
        String expectedTransactionId = "expectedTransactionId";
        CampaignSettingDuplicationCutDetails duplicationCutDetails = mock(
                CampaignSettingDuplicationCutDetails.class);
        when(duplicationCutDetails.isEnabled()).thenReturn(true);
        when(campaignSettingService.findDuplicationCutDetailsBy(CAMPAIGN_ID_1))
                .thenReturn(duplicationCutDetails);
        doReturn(expectedTransactionId).when(underTest).createTransactionIdPrefix(
                CONVERSION_DATE_VALUE, IDENTIFIER_VALUE,
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CUSTOMER_TYPE);

        // when
        List<String> actual = underTest.generateTransactionIdsFrom(QUANTITY_VALUE_1,
                CONVERSION_DATE_VALUE, IDENTIFIER_VALUE, productId,
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CAMPAIGN_ID_1, CUSTOMER_TYPE);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertEquals(expectedTransactionId, actual.get(0));

        verify(logValidator).validateMaxByteCountOf(expectedTransactionId,
                TRANSACTION_ID_MAX_BYTE_LENGTH, TRANSACTION_ID);
    }

    @Test
    public void testGenerateTransactionIdsFromShouldReturnCorrectValueWhenGivenProductIdIsNotEmptyAndConversionIsNotMultipleProductAndTheCreatedTransactionIdIsLessThanTheTransactionIdMaxByteLength() {
        // given
        String transactionIdPrefix = "transactionIdPrefix";
        String expectedTransactionId = "transactionIdPrefix-productId-unique";

        when(underTest.isMultipleProductConversion(INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID,
                QUANTITY_VALUE_1)).thenReturn(false);
        doReturn(UNIQUE_KEY_FOR_TRANSACTION_ID).when(underTest)
                .getUniqueKeyForTransactionId();
        CampaignSettingDuplicationCutDetails duplicationCutDetails = mock(
                CampaignSettingDuplicationCutDetails.class);
        when(duplicationCutDetails.isEnabled()).thenReturn(false);
        when(campaignSettingService.findDuplicationCutDetailsBy(CAMPAIGN_ID_1))
                .thenReturn(duplicationCutDetails);
        doReturn(transactionIdPrefix).when(underTest).createTransactionIdPrefix(
                CONVERSION_DATE_VALUE, IDENTIFIER_VALUE,
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CUSTOMER_TYPE);

        // when
        List<String> actual = underTest.generateTransactionIdsFrom(QUANTITY_VALUE_1,
                CONVERSION_DATE_VALUE, IDENTIFIER_VALUE, PRODUCT_ID_VALUE,
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CAMPAIGN_ID_1, CUSTOMER_TYPE);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertEquals(expectedTransactionId, actual.get(0));

        verify(logValidator).validateMaxByteCountOf(expectedTransactionId,
                TRANSACTION_ID_MAX_BYTE_LENGTH, TRANSACTION_ID);
    }

    @Test
    public void testGenerateTransactionIdsFromShouldReturnCorrectValueWhenGivenProductIdIsNotEmptyAndConversionIsNotMultipleProductAndTheCreatedTransactionIdIsLessThanTheTransactionIdMaxByteLengthAndCampaignDuplicationCutValueIsTrue() {
        // given
        String transactionIdPrefix = "transactionIdPrefix";
        String expectedTransactionId = "transactionIdPrefix-productId";

        when(underTest.isMultipleProductConversion(INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID,
                QUANTITY_VALUE_1)).thenReturn(false);
        CampaignSettingDuplicationCutDetails duplicationCutDetails = mock(
                CampaignSettingDuplicationCutDetails.class);
        when(duplicationCutDetails.isEnabled()).thenReturn(true);
        when(campaignSettingService.findDuplicationCutDetailsBy(CAMPAIGN_ID_1))
                .thenReturn(duplicationCutDetails);
        doReturn(transactionIdPrefix).when(underTest).createTransactionIdPrefix(
                CONVERSION_DATE_VALUE, IDENTIFIER_VALUE,
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CUSTOMER_TYPE);

        // when
        List<String> actual = underTest.generateTransactionIdsFrom(QUANTITY_VALUE_1,
                CONVERSION_DATE_VALUE, IDENTIFIER_VALUE, PRODUCT_ID_VALUE,
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CAMPAIGN_ID_1, CUSTOMER_TYPE);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertEquals(expectedTransactionId, actual.get(0));

        verify(logValidator).validateMaxByteCountOf(expectedTransactionId,
                TRANSACTION_ID_MAX_BYTE_LENGTH, TRANSACTION_ID);
    }

    @Test
    public void testGenerateTransactionIdsFromShouldReturnCorrectValueWhenGivenProductIdIsNotEmptyAndConversionIsMultipleProductAndSomeCreatedTransactionIdsAreLessThanTransactionIdMaxByteLength() {
        // given
        String transactionIdPrefix = "transactionIdPrefix";
        String expectedTransactionId1 = "transactionIdPrefix-productId-0-unique";
        String expectedTransactionId2 = "transactionIdPrefix-productId-1-unique";

        when(underTest.isMultipleProductConversion(INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID,
                QUANTITY_VALUE_2)).thenReturn(true);
        doReturn(UNIQUE_KEY_FOR_TRANSACTION_ID).when(underTest)
                .getUniqueKeyForTransactionId();
        CampaignSettingDuplicationCutDetails duplicationCutDetails = mock(
                CampaignSettingDuplicationCutDetails.class);
        when(duplicationCutDetails.isEnabled()).thenReturn(false);
        when(campaignSettingService.findDuplicationCutDetailsBy(CAMPAIGN_ID_1))
                .thenReturn(duplicationCutDetails);
        doReturn(transactionIdPrefix).when(underTest).createTransactionIdPrefix(
                CONVERSION_DATE_VALUE, IDENTIFIER_VALUE,
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CUSTOMER_TYPE);

        // when
        List<String> actual = underTest.generateTransactionIdsFrom(QUANTITY_VALUE_2,
                CONVERSION_DATE_VALUE, IDENTIFIER_VALUE, PRODUCT_ID_VALUE,
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CAMPAIGN_ID_1, CUSTOMER_TYPE);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertEquals(expectedTransactionId1, actual.get(0));
        assertEquals(expectedTransactionId2, actual.get(1));

        verify(logValidator).validateMaxByteCountOf(expectedTransactionId1,
                TRANSACTION_ID_MAX_BYTE_LENGTH, TRANSACTION_ID);
        verify(logValidator).validateMaxByteCountOf(expectedTransactionId2,
                TRANSACTION_ID_MAX_BYTE_LENGTH, TRANSACTION_ID);
    }

    @Test
    public void testCreatePermanentTargetInternalTransactionIdShouldReturnCorrectValueWhenCustomerTypeIsNotEmpty() {
        // given
        String expected = "3$identifier$3$customerType";

        // when
        String actual = underTest.createPermanentTargetInternalTransactionId(CAMPAIGN_ID_3,
                IDENTIFIER, RESULT_ID, CUSTOMER_TYPE);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreatePermanentTargetInternalTransactionIdShouldReturnCorrectValueWhenCustomerTypeIsEmpty() {
        // given
        String customerType = "";
        String expected = "3$identifier$3";

        // when
        String actual = underTest.createPermanentTargetInternalTransactionId(CAMPAIGN_ID_3,
                IDENTIFIER, RESULT_ID, customerType);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreatePermanentTargetInternalTransactionIdShouldReturnCorrectValueWhenCustomerTypeIsNull() {
        // given
        String customerType = null;
        String expected = "3$identifier$3";

        // when
        String actual = underTest.createPermanentTargetInternalTransactionId(CAMPAIGN_ID_3,
                IDENTIFIER, RESULT_ID, customerType);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateTransactionIdPrefixShouldReturnCorrectValueWhenCustomerTypeIsNotEmpty() {
        // given
        String expected = "2018-06-15 00:00:00-identifier-3-customerType";

        // when
        String actual = underTest.createTransactionIdPrefix(CONVERSION_DATE_VALUE,
                IDENTIFIER_VALUE, INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CUSTOMER_TYPE);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateTransactionIdPrefixShouldReturnCorrectValueWhenCustomerTypeIsEmpty() {
        // given
        String customerType = "";
        String expected = "2018-06-15 00:00:00-identifier-3";

        // when
        String actual = underTest.createTransactionIdPrefix(CONVERSION_DATE_VALUE,
                IDENTIFIER_VALUE, INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, customerType);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateTransactionIdPrefixShouldReturnCorrectValueWhenCustomerTypeIsNull() {
        // given
        String customerType = null;
        String expected = "2018-06-15 00:00:00-identifier-3";

        // when
        String actual = underTest.createTransactionIdPrefix(CONVERSION_DATE_VALUE,
                IDENTIFIER_VALUE, INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, customerType);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenAResultIdThatIsNotCategoryResultIdAndProcudtResultIdAndAQuantityLessThanOne() {
        // given
        int resultId = 4;

        // when
        boolean actual = underTest.isMultipleProductConversion(resultId,
                QUANTITY_VALUE_0);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenAResultIdThatIsNotCategoryResultIdAndProcudtResultIdAndAQuantityEqualToOne() {
        // given
        int resultId = 31;

        // when
        boolean actual = underTest.isMultipleProductConversion(resultId,
                QUANTITY_VALUE_1);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenAResultIdThatIsNotCategoryResultIdAndProcudtResultIdAndAQuantityGreaterThanOne() {
        // given
        int resultId = 2;

        // when
        boolean actual = underTest.isMultipleProductConversion(resultId,
                QUANTITY_VALUE_2);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenAProductResultIdAndAQuantityLessThanOne() {
        // when
        boolean actual = underTest.isMultipleProductConversion(
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, QUANTITY_VALUE_0);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenACategoryResultIdAndAQuantityLessThanOne() {
        // when
        boolean actual = underTest.isMultipleProductConversion(
                INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID, QUANTITY_VALUE_0);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenAProductResultIdAndAQuantityEqualToOne() {
        // when
        boolean actual = underTest.isMultipleProductConversion(
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, QUANTITY_VALUE_1);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenACategoryResultIdAndAQuantityEqualToOne() {
        // when
        boolean actual = underTest.isMultipleProductConversion(
                INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID, QUANTITY_VALUE_1);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnTrueWhenGivenAProductResultIdAndAQuantityGreaterThanOne() {
        // when
        boolean actual = underTest.isMultipleProductConversion(
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, QUANTITY_VALUE_2);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnTrueWhenGivenACategoryResultIdAndAQuantityGreaterThanOne() {
        // when
        boolean actual = underTest.isMultipleProductConversion(
                INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID, QUANTITY_VALUE_2);

        // then
        assertTrue(actual);
    }

    @Test
    public void testGenerateTransactionIdsFromShouldAppendDuplicatedProductValueWhenATDuplicatedProductPresent() {
        // given
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                new CampaignSettingDuplicationCutDetails(false, PERMANENT);
        when(campaignSettingService.findDuplicationCutDetailsBy(CAMPAIGN_ID_1))
                .thenReturn(duplicationCutDetails);
        String expectedPrefix = "2018-06-15 00:00:00-identifier-3-customerType-productId";
        String expectedTransactionId = expectedPrefix + "-1011-2";

        // when
        List<String> actual = underTest.generateTransactionIdsFrom(QUANTITY_VALUE_1,
                CONVERSION_DATE_VALUE, IDENTIFIER_VALUE, PRODUCT_ID_VALUE,
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CAMPAIGN_ID_1, CUSTOMER_TYPE,
                AT_DUPLICATED_PRODUCT);

        // then
        assertEquals(1, actual.size());
        assertEquals(expectedTransactionId, actual.get(0));
        verify(logValidator).validateMaxByteCountOf(expectedTransactionId,
                TRANSACTION_ID_MAX_BYTE_LENGTH, TRANSACTION_ID);
    }

    @Test
    public void testGenerateTransactionIdsFromShouldWorkNormallyWhenATDuplicatedProductNotPresent() {
        // given
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                new CampaignSettingDuplicationCutDetails(false, PERMANENT);
        when(campaignSettingService.findDuplicationCutDetailsBy(CAMPAIGN_ID_1))
                .thenReturn(duplicationCutDetails);
        doReturn(UNIQUE_KEY_FOR_TRANSACTION_ID).when(underTest)
                .getUniqueKeyForTransactionId();
        String expectedPrefix = "2018-06-15 00:00:00-identifier-3-customerType-productId";
        String expectedTransactionId = expectedPrefix + "-" + UNIQUE_KEY_FOR_TRANSACTION_ID;

        // when
        List<String> actual = underTest.generateTransactionIdsFrom(QUANTITY_VALUE_1,
                CONVERSION_DATE_VALUE, IDENTIFIER_VALUE, PRODUCT_ID_VALUE,
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, CAMPAIGN_ID_1, CUSTOMER_TYPE,
                null);

        // then
        assertEquals(1, actual.size());
        assertEquals(expectedTransactionId, actual.get(0));
        verify(logValidator).validateMaxByteCountOf(expectedTransactionId,
                TRANSACTION_ID_MAX_BYTE_LENGTH, TRANSACTION_ID);
    }
}
