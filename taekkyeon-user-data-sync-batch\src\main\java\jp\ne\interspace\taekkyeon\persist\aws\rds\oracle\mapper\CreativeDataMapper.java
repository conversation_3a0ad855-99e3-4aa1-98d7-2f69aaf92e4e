/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.Creative;
import jp.ne.interspace.taekkyeon.model.CreativeDetails;
import jp.ne.interspace.taekkyeon.model.CreativeType;
import jp.ne.interspace.taekkyeon.model.CustomCreativeTrackingUrlRule;
import jp.ne.interspace.taekkyeon.model.SubId;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DEFAULT_ORACLE_FETCH_SIZE;

/**
 * <PERSON><PERSON><PERSON> mapper for handling creative.
 *
 * <AUTHOR>
 */
public interface CreativeDataMapper {

    /**
        SELECT
            banner_id AS id,
            banner_name AS name,
            banner_type_id AS creativeType,
            linked_url AS creativeLandingPageUrl,
            shorten_link_code AS shortenLinkCode
        FROM
            banner
        WHERE
            NVL(updated_on, created_on) BETWEEN #{startDateTime} AND #{endDateTime}
        ORDER BY
            banner_id
     */
    @Multiline String SELECT_CREATIVE = "";

    /**
        SELECT
            shorten_link_code
        FROM
            banner
        WHERE
            banner_id = #{creativeId}
     */
    @Multiline String SELECT_SHORTEN_LINK_CODE_BY = "";

    /**
        SELECT
            a.partner_site_no
        FROM
            banner b
        INNER JOIN
            affiliation a
        ON
            b.merchant_campaign_no = a.merchant_campaign_no
        WHERE
            b.banner_id = #{creativeId}
        FETCH FIRST 1 ROW ONLY
     */
    @Multiline String SELECT_SITE_ID_BY = "";

    /**
        SELECT
            connection.external_site_id
        FROM
            ad_platform_pub_connection connection
        INNER JOIN
            merchant_campaign campaign
        ON
            connection.ad_platform_id = campaign.ad_platform_id
        INNER JOIN
            banner creative
        ON
            campaign.campaign_no = creative.merchant_campaign_no
        WHERE
            connection.site_id = #{siteId}
        AND
            creative.banner_id = #{creativeId}
     */
    @Multiline String SELECT_EXTERNAL_SITE_ID_BY_SITE_ID_AND_CREATIVE_ID = "";

    /**
        SELECT
        (
            SELECT
                url_prefix
            FROM
                production_link_rules
            WHERE
                merchant_campaign_no = #{campaignId}
            AND
                rule_type = 3
        ) prefix,
        NVL((
            SELECT
                url_encode_times
            FROM
                production_link_rules
            WHERE
                merchant_campaign_no = #{campaignId}
            AND
                rule_type = 3
        ), 0) urlEncodeTimes,
        (
            SELECT
                url_parameter
            FROM
                production_link_rules
            WHERE
                merchant_campaign_no = #{campaignId}
            AND
                rule_type = 4
        ) parameters
        FROM
            DUAL
     */
    @Multiline String SELECT_TRACKING_URL_RULE = "";

    /**
        SELECT
            linked_url
        FROM
            banner
        WHERE
            banner_id = #{creativeId}
     */
    @Multiline String SELECT_LANDING_URL = "";

    /**
        SELECT
            b.merchant_campaign_no AS campaignId,
            b.linked_url AS landingUrl,
            bhm.partner_site_no AS siteId
        FROM
            banner b
        LEFT JOIN
            banner_hidden_mapping bhm
        ON
            b.banner_id = bhm.banner_id
        WHERE
            b.banner_id = #{creativeId}
        FETCH FIRST 1 ROW ONLY
     */
    @Multiline String SELECT_CREATIVE_DETAILS = "";

    /**
        SELECT
            parameter_name name,
            parameter_value value
        FROM
            linkcode_add_param_setting
        WHERE
            partner_site_no = #{siteId}
        AND
            banner_id = #{creativeId}
     */
    @Multiline String SELECT_SUB_IDS_BY_SITE_ID_AND_CREATIVE_ID = "";

    /**
     * Returns creatives whose created_on or update_on is period from
     * {@code startDateTime} to {@code endDateTime}.
     *
     * @param startDateTime
     *          {@link LocalDateTime} start date
     * @param endDateTime
     *          {@link LocalDateTime} end date
     * @return creatives whose created_on or update_on is period from
     *          {@code startDateTime} to {@code endDateTime}
     * @see #SELECT_CREATIVE
     */
    @Select(SELECT_CREATIVE)
    @Options(fetchSize = DEFAULT_ORACLE_FETCH_SIZE)
    @ConstructorArgs({ @Arg(column = "id", javaType = Long.class),
            @Arg(column = "name", javaType = String.class),
            @Arg(column = "creativeType", javaType = CreativeType.class),
            @Arg(column = "creativeLandingPageUrl", javaType = String.class),
            @Arg(column = "shortenLinkCode", javaType = String.class)})
    List<Creative> findCreatives(@Param("startDateTime") LocalDateTime startDateTime,
            @Param("endDateTime") LocalDateTime endDateTime);

    /**
     * Finds site id by creative id.
     *
     * @param creativeId
     *            the given creative id
     * @return site id by creativeId id
     */
    @Select(SELECT_SITE_ID_BY)
    Long findSiteIdBy(Long creativeId);

    /**
     * Assigns an available external site ID of the given ad platform to the given site.
     *
     * @param creativeId the given creative id
     * @param siteId the given creative id
     * @return the number of updated rows
     */
    @Select(SELECT_EXTERNAL_SITE_ID_BY_SITE_ID_AND_CREATIVE_ID)
    Long findExternalSiteIdBy(@Param("creativeId") long creativeId,
            @Param("siteId") long siteId);

    /**
     * Returns the rule for the tracking URL for the given campaign.
     *
     * @param campaignId
     *            ID of the given campaign
     * @return the rule for the tracking URL for the given campaign
     */
    @Select(SELECT_TRACKING_URL_RULE)
    @ConstructorArgs({ @Arg(column = "prefix", javaType = String.class),
            @Arg(column = "parameters", javaType = String.class),
            @Arg(column = "urlEncodeTimes", javaType = Integer.class) })
    CustomCreativeTrackingUrlRule findTrackingUrlRuleBy(long campaignId);

    /**
     *  Returns the {@link CreativeDetails} given by creative id.
     *
     * @param creativeId the given creative id
     * @return the {@link CreativeDetails} given by creative id
     */
    @Select(SELECT_CREATIVE_DETAILS)
    @ConstructorArgs({ @Arg(column = "campaignId", javaType = long.class),
            @Arg(column = "landingUrl", javaType = String.class),
            @Arg(column = "siteId", javaType = long.class)})
    CreativeDetails findCreativeDetailsBy(long creativeId);

    /**
     * Returns the {@link SubId}s of the given publisher site and creative.
     *
     * @param siteId
     *            the ID of the given publisher site
     * @param creativeId
     *            the ID of the given creative
     * @return the {@link SubId}s of the given publisher site
     * @see #SELECT_SUB_IDS_BY_SITE_ID_AND_CREATIVE_ID
     */
    @Select(SELECT_SUB_IDS_BY_SITE_ID_AND_CREATIVE_ID)
    @ConstructorArgs({ @Arg(column = "name", javaType = String.class),
            @Arg(column = "value", javaType = String.class) })
    List<SubId> findSubIdBy(@Param("siteId") long siteId,
            @Param("creativeId") long creativeId);
}
