/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.BatchWriteItemResult;
import com.amazonaws.services.dynamodbv2.model.PutRequest;
import com.amazonaws.services.dynamodbv2.model.WriteRequest;
import com.google.common.collect.ImmutableMap;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.persist.aws.dynamodb.DynamoDbTable;

import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.DynamoDbTable.stringOf;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link DynamoDbSyncService}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class DynamoDbSyncServiceTest {

    private static final String UPDATE_MESSAGE = "Update DynamoDb table name : {}, contents : {}";
    private static final String ERROR_MESSAGE = "Failed to update DynamoDB data.";

    private static final String KEY_1 = "key1";
    private static final String KEY_2 = "key2";

    private static final String TABLE_NAME = "tableName";
    private static final String KEY_NAME = "keyName";

    private static final Map<String, AttributeValue> ATTRIBUTE_VALUE_1 = Collections
            .emptyMap();

    @InjectMocks @Spy
    private DynamoDbSyncService underTest;

    @Mock
    private DynamoDbTable dynamoDbTable;

    @Mock
    private SyncStatusUpdateService syncStatusUpdateService;

    @Mock
    private Logger logger;

    @Test
    public void testWaitUntilAvailableShouldCallWaitUntilAvailableMethodOnDynamoDbTableWhenCalled() {
        // when
        underTest.waitUntilAvailable();

        // when
        verify(dynamoDbTable).waitUntilAvailable();
    }

    @Test
    public void testSyncStatusShouldUpdateDataInDynamoDbSuccessfullyWhenDataWereSyncedSuccessfully() {
        // given
        Map<String, AttributeValue> attributeValue2 = Collections.emptyMap();
        Map<String, Map<String, AttributeValue>> attributeValues = new HashMap<>();
        attributeValues.put(KEY_1, ATTRIBUTE_VALUE_1);
        attributeValues.put(KEY_2, attributeValue2);

        BatchWriteItemResult result = new BatchWriteItemResult().addUnprocessedItemsEntry(
                TABLE_NAME, Arrays.asList(createUnprocessedRequestFrom(KEY_2)));
        when(dynamoDbTable.batchUpdateBy(attributeValues)).thenReturn(Optional.of(result));
        when(dynamoDbTable.getKeyName()).thenReturn(KEY_NAME);
        when(dynamoDbTable.getTableName()).thenReturn(TABLE_NAME);
        doReturn(logger).when(underTest).getLogger();

        Map<String, Map<String, AttributeValue>> expectedSyncStatusUpdateRecords =
                ImmutableMap.of(KEY_1, ATTRIBUTE_VALUE_1);

        // when
        underTest.syncStatus(attributeValues);

        // then
        verify(dynamoDbTable).batchUpdateBy(attributeValues);
        verify(syncStatusUpdateService).updateSyncStatusOf(
                expectedSyncStatusUpdateRecords);
        verify(logger).info(UPDATE_MESSAGE, TABLE_NAME, ImmutableMap
                .of(KEY_1, new HashMap<>(), KEY_2, new HashMap<>()).toString());
    }

    @Test
    public void testSyncStatusShouldNotUpdateDataInDynamoDbWhenNoDataWasSynced() {
        // given
        Map<String, Map<String, AttributeValue>> attributeValues = new HashMap<>();
        attributeValues.put(KEY_1, ATTRIBUTE_VALUE_1);

        BatchWriteItemResult result = new BatchWriteItemResult().addUnprocessedItemsEntry(
                TABLE_NAME, Arrays.asList(createUnprocessedRequestFrom(KEY_1)));
        when(dynamoDbTable.batchUpdateBy(attributeValues))
                .thenReturn(Optional.of(result));
        when(dynamoDbTable.getKeyName()).thenReturn(KEY_NAME);
        when(dynamoDbTable.getTableName()).thenReturn(TABLE_NAME);
        doReturn(logger).when(underTest).getLogger();

        // when
        underTest.syncStatus(attributeValues);

        // then
        verify(dynamoDbTable).batchUpdateBy(attributeValues);
        verifyZeroInteractions(syncStatusUpdateService);
        verify(logger).info(UPDATE_MESSAGE, TABLE_NAME,
                ImmutableMap.of(KEY_1, ATTRIBUTE_VALUE_1).toString());
    }

    @Test
    public void testSyncStatusShouldShouldThrowExceptionWhenOccurredException() {
        // given
        Map<String, Map<String, AttributeValue>> attributeValues = new HashMap<>();
        attributeValues.put(KEY_1, ATTRIBUTE_VALUE_1);

        when(dynamoDbTable.getTableName()).thenReturn(TABLE_NAME);
        RuntimeException exception = new RuntimeException("exception");
        when(dynamoDbTable.batchUpdateBy(attributeValues)).thenThrow(exception);
        doReturn(logger).when(underTest).getLogger();

        // when
        try {
            underTest.syncStatus(attributeValues);
            fail();

            // then
        } catch (Exception e) {
            assertEquals(exception, e);
            verify(dynamoDbTable).batchUpdateBy(attributeValues);
            verifyZeroInteractions(syncStatusUpdateService);
            verify(logger).info(UPDATE_MESSAGE, TABLE_NAME,
                    ImmutableMap.of(KEY_1, ATTRIBUTE_VALUE_1).toString());
            verify(logger).error(ERROR_MESSAGE, exception);
        }
    }

    private WriteRequest createUnprocessedRequestFrom(String key) {
        Map<String, AttributeValue> item = new HashMap<>();
        item.put(KEY_NAME, stringOf(key));
        PutRequest putRequest = new PutRequest(item);
        return new WriteRequest(putRequest);
    }
}
