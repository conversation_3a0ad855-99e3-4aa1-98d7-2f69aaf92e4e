<?xml version="1.0"?>
<!DOCTYPE suppressions PUBLIC "-//Puppy Crawl//DTD Suppressions 1.1//EN" "http://www.puppycrawl.com/dtds/suppressions_1_1.dtd">
<suppressions>
    <suppress checks="LineLength" files="AffiliationRankHistoryMapper" />
	<suppress checks="LineLength" files="CategoryRewardSettingMapper" />
	<suppress checks="LineLength" files="ProductRewardSettingMapper" />
    <suppress checks="LineLength" files="ConversionMapper.java" />
    <suppress checks="LineLength" files="ConversionLogMapper.java" />
    <suppress checks="LineLength" files="CreativeAccessLogSummaryMapper.java" />
    <suppress checks="LineLength" files="CurrencyMapper" />
    <suppress checks="LineLength" files="CurrencyExchangeRateMapper.java" />
    <suppress checks="LineLength" files="MonthlyTaxCalculationMapper.java" />
    <suppress checks="LineLength" files="PaymentTaxCalculationMapper.java" />
    <suppress checks="LineLength" files="PublisherRewardTaxMapper.java" />
    <suppress checks="LineLength" files="PublisherPaymentHistoryMapper.java" />
    <suppress checks="LineLength" files="SiteMapper.java" />
    <suppress checks="LineLength" files="TaekkyeonScraperModule.java" />
    <suppress checks="LineLength" files="GoogleAdsScraperProviderModule.java" />
    <suppress checks="LineLength" files="GoogleAdsScraperProviderPropertiesJunitModule.java" />
    <suppress checks="LineLength" files="GoogleAdsScraperProviderIntegrationTest.java" />
    <suppress checks="LineLength" files="TableIndexMapper.java" />
    <suppress checks="LineLength" files="CommonMapper.java" />
    <suppress checks="LineLength" files="CampaignClosureMapper.java" />
    <suppress checks="LineLength" files="ConversionMapper.java" />
    <suppress checks="LineLength" files="CreativeAccessLogSummaryMapper.java" />
    <suppress checks="LineLength" files="IntegrationTestMapper.java" />
    <suppress checks="LineLength" files="BonusMapper.java" />
    <suppress checks="LineLength" files="FixedBonusDetailsMapper.java" />
    <suppress checks="LineLength" files="PublisherAccountPaymentHistoryMapper.java" />
    <suppress checks="LineLength" files="PublisherTaxCalculationMapper.java" />
    <suppress checks="LineLength" files="PublisherAccountCampaignRewardHistoryMapper.java" />
    <suppress checks="LineLength" files="SalesLogMapper.java" />
    <suppress checks="LineLength" files="SynchronizationDataMapper.java" />
    <suppress checks="LineLength" files="TrackingDataMapper.java" />
    <suppress checks="LineLength" files="jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PublisherAccountFunnelMapper.java" />
    <suppress checks=".*" files="CloudwatchAppender.java" />
    <suppress checks=".*" files="jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionMapper.java" />
    <suppress checks=".*" files="PublisherSiteReportMapper.java" />
    <suppress checks=".*" files="ConversionMapper.java" />
    <suppress checks=".*" files="CreativeAccessLogSummaryMapper.java" />
</suppressions>
