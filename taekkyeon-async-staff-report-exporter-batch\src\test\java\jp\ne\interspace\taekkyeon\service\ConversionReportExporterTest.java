/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.ConversionReport;
import jp.ne.interspace.taekkyeon.model.ConversionReportExportRequest;
import jp.ne.interspace.taekkyeon.model.ConversionReportRowCount;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.PublisherType;
import jp.ne.interspace.taekkyeon.model.ReportExportPagedDetails;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ClickParameterMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionMapper;

import static java.util.Arrays.asList;
import static jp.ne.interspace.taekkyeon.model.PublisherType.UNSPECIFIED;
import static jp.ne.interspace.taekkyeon.model.ReportQueryPeriodBase.CONVERSION_DATE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ConversionReportExporter}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class ConversionReportExporterTest {

    private static final Set<Long> CONVERSION_IDS = new HashSet<>(
            asList(1050L));
    private static final List<ConversionStatus> CONVERSION_STATUSES = asList(ConversionStatus.APPROVED);
    private static final Set<Long> CAMPAIGN_IDS = new HashSet<>(asList(277L));
    private static final Set<Long> SITE_IDS = new HashSet<>(asList(7893L));
    private static final Set<String> VERIFICATION_IDS = new HashSet<>(
            asList("verificationId"));
    private static final Set<String> PRODUCT_IDS = new HashSet<>(
            asList("productId1"));
    private static final Set<Long> CREATIVE_IDS = new HashSet<>(asList(14851L));
    private static final Set<Long> RESULT_IDS = new HashSet<>(asList(30L));
    private static final Set<Long> RANKS = new HashSet<>(asList(5L));
    private static final List<DeviceType> DEVICE_TYPES = asList(DeviceType.ANDROID_TAB);
    private static final String COUNTRY_CODE = "SG";
    private static final Set<Long> MERCHANT_IDS = Sets.newHashSet(5L);
    private static final Set<Long> PUBLISHER_IDS = new HashSet<>(asList(1L));
    private static final PublisherType PUBLISHER_TYPE = UNSPECIFIED;
    private static final long PUBLISHER_AGENCY_ID = 1;
    private static final ZonedDateTime CONVERSION_REPORT_FROM_DATE = ZonedDateTime
            .parse("2018-08-02T00:00:00.000+07:00");

    private static final ZonedDateTime CONVERSION_REPORT_TO_DATE = ZonedDateTime
            .parse("2018-08-04T00:00:00.000+07:00");
    private static final long DEFAULT_LATEST_MAX_CONVERSION_ID = 0;
    private static final Long NEXT_LATEST_MAX_CONVERSION_ID = 1L;

    @Spy @InjectMocks
    private ConversionReportExporter underTest;

    @Mock
    private ConversionMapper conversionMapper;

    @Mock
    private ClickParameterMapper clickParamterMapper;

    @Test
    public void testFindReportShouldCallFindConversionReportDataWhenCalled() {
        // given
        ConversionReportExportRequest request = createConversionReportRequest(
                PUBLISHER_IDS, SITE_IDS);
        List<ConversionReport> reportItems = asList(mock(ConversionReport.class));
        ReportExportPagedDetails<ConversionReport, Long> expected =
                new ReportExportPagedDetails<>(reportItems, NEXT_LATEST_MAX_CONVERSION_ID);
        doReturn(expected).when(underTest).findConversionReportDataPaging(
                DEFAULT_LATEST_MAX_CONVERSION_ID);

        // when
        ReportExportPagedDetails<ConversionReport, Long> actual =
                underTest.findReportData(request, DEFAULT_LATEST_MAX_CONVERSION_ID);

        // then
        assertSame(expected, actual);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testFindCustomFieldsShouldReturnClickParameterNamesWhenDataIsInsertedSuccessfully() {
        // given
        ConversionReportExportRequest request = mock(ConversionReportExportRequest.class);
        when(request.getFromDate()).thenReturn(ZonedDateTime.now().minusDays(1));
        when(request.getToDate()).thenReturn(ZonedDateTime.now());

        int insertedConversionTempCount = 10;
        when(conversionMapper.insertConversionTempTable(
                eq(request),
                any(LocalDate.class),
                any(LocalDate.class)))
                .thenReturn(insertedConversionTempCount);

        int insertedClickParameterTempCount = 5;
        when(clickParamterMapper.insertClickParameterTempTable())
                .thenReturn(insertedClickParameterTempCount);

        Set<String> expectedClickParameterNames = ImmutableSet.of("param1", "param2");
        when(clickParamterMapper.getClickParameterNamesFromTempTable())
                .thenReturn(expectedClickParameterNames);

        // when
        List<String> actualClickParameterNames = underTest.findCustomFields(request);

        // then
        assertNotNull(actualClickParameterNames);
        assertEquals(new LinkedList<>(expectedClickParameterNames),
                actualClickParameterNames);
        verify(conversionMapper).insertConversionTempTable(
                eq(request),
                any(LocalDate.class),
                any(LocalDate.class));
        verify(clickParamterMapper).insertClickParameterTempTable();
        verify(clickParamterMapper).getClickParameterNamesFromTempTable();
    }

    @Test
    public void testFindCustomFieldsShouldReturnEmptyListWhenConversionTempCountIsZero() {
        // given
        ConversionReportExportRequest request = mock(ConversionReportExportRequest.class);
        when(request.getFromDate()).thenReturn(ZonedDateTime.now().minusDays(1));
        when(request.getToDate()).thenReturn(ZonedDateTime.now());

        int insertedConversionTempCount = 0;
        when(conversionMapper.insertConversionTempTable(
                eq(request),
                any(LocalDate.class),
                any(LocalDate.class)))
                .thenReturn(insertedConversionTempCount);

        // when
        List<String> actualClickParameterNames = underTest.findCustomFields(request);

        // then
        assertNotNull(actualClickParameterNames);
        assertTrue(actualClickParameterNames.isEmpty());
        verify(conversionMapper).insertConversionTempTable(
                eq(request),
                any(LocalDate.class),
                any(LocalDate.class));
        verifyNoMoreInteractions(clickParamterMapper);
    }

    @Test
    public void testFindCustomFieldsShouldReturnEmptyListWhenClickParameterTempCountIsZero() {
        // given
        ConversionReportExportRequest request = mock(ConversionReportExportRequest.class);
        when(request.getFromDate()).thenReturn(ZonedDateTime.now().minusDays(1));
        when(request.getToDate()).thenReturn(ZonedDateTime.now());

        int insertedConversionTempCount = 10;
        when(conversionMapper.insertConversionTempTable(
                eq(request),
                any(LocalDate.class),
                any(LocalDate.class)))
                .thenReturn(insertedConversionTempCount);

        int insertedClickParameterTempCount = 0;
        when(clickParamterMapper.insertClickParameterTempTable())
                .thenReturn(insertedClickParameterTempCount);

        // when
        List<String> actualClickParameterNames = underTest.findCustomFields(request);

        // then
        assertNotNull(actualClickParameterNames);
        assertTrue(actualClickParameterNames.isEmpty());
        verify(conversionMapper).insertConversionTempTable(
                eq(request),
                any(LocalDate.class),
                any(LocalDate.class));
        verify(clickParamterMapper).insertClickParameterTempTable();
        verifyNoMoreInteractions(clickParamterMapper);
    }

    @Test
    public void testFindConversionReportDataPagingShouldReturnCorrectData() {
        // given
        long page = 1L;
        int partitionLimitation = 10;
        int rowCount = 20;
        ConversionReportRowCount.setRowCount(rowCount);
        when(underTest.getPartitionLimitation()).thenReturn(partitionLimitation);
        when(underTest.isCheckReportRowsLimit()).thenReturn(false);

        long offset = (page - 1) * partitionLimitation + 1;
        long limit = page * partitionLimitation;

        List<ConversionReport> mockConversions = asList(
                mock(ConversionReport.class),
                mock(ConversionReport.class)
        );

        when(conversionMapper.findFullConversionReportFromTempTableBy(offset, limit, false))
                .thenReturn(mockConversions);

        // when
        ReportExportPagedDetails<ConversionReport, Long> result = underTest
                .findConversionReportDataPaging(page);

        // then
        assertNotNull(result);
        assertEquals(mockConversions, result.getReportItems());
        assertEquals(2L, result.getPaginationData().longValue());

        verify(conversionMapper).findFullConversionReportFromTempTableBy(offset, limit, false);
        verifyNoMoreInteractions(conversionMapper);
    }

    private ConversionReportExportRequest createConversionReportRequest(
            Set<Long> publisherIds, Set<Long> siteIds) {
        return new ConversionReportExportRequest(COUNTRY_CODE,
                CONVERSION_REPORT_FROM_DATE, CONVERSION_REPORT_TO_DATE, siteIds,
                CAMPAIGN_IDS, CONVERSION_DATE, CONVERSION_STATUSES, VERIFICATION_IDS,
                PRODUCT_IDS, CREATIVE_IDS, RESULT_IDS, RANKS, DEVICE_TYPES,
                CONVERSION_IDS, MERCHANT_IDS, publisherIds, PUBLISHER_AGENCY_ID,
                PUBLISHER_TYPE, true);
    }
}
