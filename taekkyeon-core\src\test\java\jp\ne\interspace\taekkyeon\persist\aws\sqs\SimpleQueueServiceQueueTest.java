/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.sqs;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import com.amazonaws.services.sqs.model.BatchResultErrorEntry;
import com.amazonaws.services.sqs.model.DeleteMessageBatchRequestEntry;
import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageResult;
import com.amazonaws.services.sqs.model.SendMessageBatchRequestEntry;
import com.amazonaws.services.sqs.model.SendMessageBatchResult;
import com.amazonaws.services.sqs.model.SendMessageBatchResultEntry;
import com.google.common.collect.Lists;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.Pair;
import jp.ne.interspace.taekkyeon.module.Country;
import jp.ne.interspace.taekkyeon.service.SlackService;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link SimpleQueueServiceQueue}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class SimpleQueueServiceQueueTest {

    private static final String RETRY_FORMAT = "Retry ({}) times to send the SQS message {}";
    private static final String QUEUE_NAME = "queueName";
    private static final String QUEUE_URL = "queueUrl";

    private static final String ID = "id";
    private static final String FAILED_MESSAGE_ID_1 = "failedMessageId1";
    private static final String FAILED_MESSAGE_ID_2 = "failedMessageId2";

    private static final String FAILED_MESSAGE_CONTENT_1 = "failedMessageContent1";
    private static final String FAILED_MESSAGE_CONTENT_2 = "failedMessageContent2";

    private static final String MESSAGE_CONTENTS = "messageContents";
    private static final String MESSAGE_CONTENTS_1 = "messageContents1";
    private static final String MESSAGE_CONTENTS_2 = "messageContents2";
    private static final String MESSAGE_CONTENTS_3 = "messageContents3";

    private static final String RECEIPT_HANDLE_1 = "receiptHandle1";
    private static final String RECEIPT_HANDLE_2 = "receiptHandle2";
    private static final String RECEIPT_HANDLE_3 = "receiptHandle3";
    private static final String RECEIPT_HANDLE_4 = "receiptHandle4";
    private static final String RECEIPT_HANDLE_5 = "receiptHandle5";
    private static final String RECEIPT_HANDLE_6 = "receiptHandle6";
    private static final String RECEIPT_HANDLE_7 = "receiptHandle7";
    private static final String RECEIPT_HANDLE_8 = "receiptHandle8";
    private static final String RECEIPT_HANDLE_9 = "receiptHandle9";
    private static final String RECEIPT_HANDLE_10 = "receiptHandle10";
    private static final String RECEIPT_HANDLE_11 = "receiptHandle11";

    private static final String DELETE_REQUEST_ID_1 = "0";
    private static final String DELETE_REQUEST_ID_2 = "1";
    private static final String DELETE_REQUEST_ID_3 = "2";
    private static final String DELETE_REQUEST_ID_4 = "3";
    private static final String DELETE_REQUEST_ID_5 = "4";
    private static final String DELETE_REQUEST_ID_6 = "5";
    private static final String DELETE_REQUEST_ID_7 = "6";
    private static final String DELETE_REQUEST_ID_8 = "7";
    private static final String DELETE_REQUEST_ID_9 = "8";
    private static final String DELETE_REQUEST_ID_10 = "9";
    private static final String DELETE_REQUEST_ID_11 = "10";

    private static final int SQS_MESSAGE_BATCH_MAX_SIZE = 10;

    private static final Pair<String, String> LOG_FORMAT = Pair.of("Success {}",
            "Fail %s %s");

    @InjectMocks @Spy
    private TestQueue underTest;

    @Mock
    private SimpleQueueServiceClient sqsClient;

    @Mock
    private SlackService slackService;

    @Mock
    private Logger logger;

    @Test
    public void testSetUpForTestShouldCallCreateQueueWhenCalled() {
        // given
        when(underTest.getQueueName()).thenReturn(QUEUE_NAME);

        // when
        underTest.setUpForTest();

        // then
        verify(sqsClient).createQueue(QUEUE_NAME);
    }

    @Test
    public void testTearDownForTestShouldCallTearDownForTestWithoutTestSeedResetAndResetTestSeedWhenCalled() {
        // given
        String testSeed = System.getProperty("test_seed");
        doNothing().when(underTest).tearDownForTestWithoutTestSeedReset();

        // when
        underTest.tearDownForTest();

        // then
        verify(underTest).tearDownForTestWithoutTestSeedReset();
        assertNotEquals(testSeed, System.getProperty("test_seed"));
    }

    @Test
    public void testTearDownForTestWithoutTestSeedResetShouldCallDeleteQueueAndNotResetTestSeedWhenCalled() {
        // given
        String testSeed = System.getProperty("test_seed");
        when(underTest.getQueueUrl()).thenReturn(QUEUE_URL);

        // when
        underTest.tearDownForTestWithoutTestSeedReset();

        // then
        verify(sqsClient).deleteQueue(QUEUE_URL);
        assertEquals(testSeed, System.getProperty("test_seed"));
    }

    @Test
    public void testReceiveMessageShouldCallReceiveMessageWhenCalled() {
        // given
        ReceiveMessageRequest request = new ReceiveMessageRequest();
        ReceiveMessageResult expected = new ReceiveMessageResult();
        when(sqsClient.receiveMessage(request)).thenReturn(expected);

        // when
        ReceiveMessageResult actual = underTest.receiveMessage(request);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testGetQueueUrlShouldReturnCorrectQueueUrlWhenCalled() {
        // given
        when(sqsClient.getQueueUrl(QUEUE_NAME)).thenReturn(QUEUE_URL);
        when(underTest.getQueueName()).thenReturn(QUEUE_NAME);

        // when
        String actual = underTest.getQueueUrl();

        // then
        assertEquals(QUEUE_URL, actual);
    }

    @Test
    public void testSendShouldSendBatchesWithRetriesAndReturnFailedMessagesWhenCalled()
            throws Exception {
        // given
        List<SendMessageBatchRequestEntry> requestEntries = Arrays
                .asList(new SendMessageBatchRequestEntry());
        List<SendMessageBatchResult> results = Arrays
                .asList(new SendMessageBatchResult());
        List<SendMessageBatchResult> expected = Arrays
                .asList(new SendMessageBatchResult());
        doReturn(QUEUE_URL).when(underTest).getQueueUrl();
        doReturn(results).when(underTest).sendBatches(QUEUE_URL, requestEntries,
                LOG_FORMAT);
        doReturn(expected).when(underTest).resendFailedMessages(QUEUE_URL, results,
                LOG_FORMAT);

        // when
        List<SendMessageBatchResult> actual = underTest.send(requestEntries, LOG_FORMAT);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testSendShouldCallSendMethodWithNoLogFormatWhenCalled() throws Exception {
        // given
        List<SendMessageBatchRequestEntry> requestEntries = Arrays
                .asList(new SendMessageBatchRequestEntry());
        List<SendMessageBatchResult> expected = Arrays
                .asList(new SendMessageBatchResult());
        doReturn(expected).when(underTest).send(requestEntries, null);

        // when
        List<SendMessageBatchResult> actual = underTest.send(requestEntries);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testSendShouldSendMessageToSqsWhenCalled() {
        // given
        String message = "sqs message";
        doReturn(QUEUE_URL).when(underTest).getQueueUrl();

        // when
        underTest.send(message);

        // then
        verify(sqsClient).sendMessage(QUEUE_URL, message);
    }

    @Test
    public void testDeleteShouldDeleteMessagesInBatchesWhenCalled() {
        // given
        List<String> receiptHandles = createReceiptHandlesOverMaximumSize();

        doReturn(QUEUE_URL).when(underTest).getQueueUrl();

        List<DeleteMessageBatchRequestEntry> expectedRequests =
                createExpectedDeleteMessageRequests();
        List<List<DeleteMessageBatchRequestEntry>> expectedRequestPartitions = Lists
                .partition(expectedRequests, SQS_MESSAGE_BATCH_MAX_SIZE);

        // when
        underTest.delete(receiptHandles);

        // then
        verify(sqsClient).deleteMessageBatch(QUEUE_URL, expectedRequestPartitions.get(0));
        verify(sqsClient).deleteMessageBatch(QUEUE_URL, expectedRequestPartitions.get(1));
    }

    @Test
    public void testSendBatchesShouldSendMessagesInBatchesWhenLogFormatIsNull() {
        // given
        List<SendMessageBatchRequestEntry> requestEntries =
                createSendMessageBatchRequestEntriesOverMaximumSize();
        List<List<SendMessageBatchRequestEntry>> entryPartitions = Lists
                .partition(requestEntries, SQS_MESSAGE_BATCH_MAX_SIZE);
        SendMessageBatchResult result1 = new SendMessageBatchResult();
        SendMessageBatchResult result2 = new SendMessageBatchResult();

        when(sqsClient.sendMessageBatch(QUEUE_URL, entryPartitions.get(0)))
                .thenReturn(result1);
        when(sqsClient.sendMessageBatch(QUEUE_URL, entryPartitions.get(1)))
                .thenReturn(result2);

        // when
        List<SendMessageBatchResult> actual = underTest.sendBatches(QUEUE_URL,
                requestEntries, null);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertEquals(result1, actual.get(0));
        assertEquals(result2, actual.get(1));
    }

    @Test
    public void testSendBatchesShouldSendMessagesInBatchesAndLoggingWhenLogFormatIsNotNull() {
        // given
        List<SendMessageBatchRequestEntry> requestEntries =
                createSendMessageBatchRequestEntriesOverMaximumSize();
        List<List<SendMessageBatchRequestEntry>> entryPartitions = Lists
                .partition(requestEntries, SQS_MESSAGE_BATCH_MAX_SIZE);
        SendMessageBatchResult result1 = new SendMessageBatchResult().withSuccessful(
                new SendMessageBatchResultEntry().withId("id1"));
        SendMessageBatchResult result2 = new SendMessageBatchResult().withSuccessful(
                new SendMessageBatchResultEntry().withId("id2"));
        doReturn(logger).when(underTest).getLogger();

        when(sqsClient.sendMessageBatch(QUEUE_URL, entryPartitions.get(0)))
                .thenReturn(result1);
        when(sqsClient.sendMessageBatch(QUEUE_URL, entryPartitions.get(1)))
                .thenReturn(result2);

        // when
        List<SendMessageBatchResult> actual = underTest.sendBatches(QUEUE_URL,
                requestEntries, LOG_FORMAT);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertEquals(result1, actual.get(0));
        assertEquals(result2, actual.get(1));
        verify(logger).info(LOG_FORMAT.getLeft(), "messageContents1");
        verify(logger).info(LOG_FORMAT.getLeft(), "messageContents2");
    }

    @Test
    public void testResendFailedMessagesShouldResendFailedMessagesAndReturnEmptyListWhenSendingFailsThreeTimesInARowAndGivenLogFormatIsNull()
            throws InterruptedException {
        // given
        List<SendMessageBatchResult> originalResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(true).when(underTest).hasFailure(originalResults);
        List<SendMessageBatchRequestEntry> firstRetries = Arrays
                .asList(new SendMessageBatchRequestEntry());
        doReturn(firstRetries).when(underTest).createRequestsFrom(originalResults);

        List<SendMessageBatchResult> firstRetryResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(firstRetryResults).when(underTest).sendBatches(QUEUE_URL, firstRetries,
                null);
        doReturn(true).when(underTest).hasFailure(firstRetryResults);
        List<SendMessageBatchRequestEntry> secondRetries = Arrays
                .asList(new SendMessageBatchRequestEntry());
        doReturn(secondRetries).when(underTest).createRequestsFrom(firstRetryResults);

        List<SendMessageBatchResult> secondRetryResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(secondRetryResults).when(underTest).sendBatches(QUEUE_URL, secondRetries,
                null);
        doReturn(true).when(underTest).hasFailure(secondRetryResults);
        List<SendMessageBatchRequestEntry> thirdRetries = Arrays
                .asList(new SendMessageBatchRequestEntry());
        doReturn(thirdRetries).when(underTest).createRequestsFrom(secondRetryResults);

        List<SendMessageBatchResult> thirdRetryResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(thirdRetryResults).when(underTest).sendBatches(QUEUE_URL, thirdRetries,
                null);
        doReturn(false).when(underTest).hasFailure(thirdRetryResults);

        doReturn(MESSAGE_CONTENTS).when(underTest).getContentsFrom(anyList());
        doReturn(logger).when(underTest).getLogger();

        // when
        List<SendMessageBatchResult> actual = underTest.resendFailedMessages(QUEUE_URL,
                originalResults, null);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
        verifyZeroInteractions(logger);
    }

    @Test
    public void testResendFailedMessagesShouldResendFailedMessagesAndReturnEmptyListWhenSendingFailsThreeTimesInARowAndGivenLogFormatIsNotNull()
            throws InterruptedException {
        // given
        List<SendMessageBatchResult> originalResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(true).when(underTest).hasFailure(originalResults);
        List<SendMessageBatchRequestEntry> firstRetries = Arrays
                .asList(new SendMessageBatchRequestEntry()
                        .withMessageBody(MESSAGE_CONTENTS));
        doReturn(firstRetries).when(underTest).createRequestsFrom(originalResults);

        List<SendMessageBatchResult> firstRetryResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(firstRetryResults).when(underTest).sendBatches(QUEUE_URL, firstRetries,
                LOG_FORMAT);
        doReturn(true).when(underTest).hasFailure(firstRetryResults);
        List<SendMessageBatchRequestEntry> secondRetries = Arrays
                .asList(new SendMessageBatchRequestEntry());
        doReturn(secondRetries).when(underTest).createRequestsFrom(firstRetryResults);

        List<SendMessageBatchResult> secondRetryResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(secondRetryResults).when(underTest).sendBatches(QUEUE_URL, secondRetries,
                LOG_FORMAT);
        doReturn(true).when(underTest).hasFailure(secondRetryResults);
        List<SendMessageBatchRequestEntry> thirdRetries = Arrays
                .asList(new SendMessageBatchRequestEntry());
        doReturn(thirdRetries).when(underTest).createRequestsFrom(secondRetryResults);

        List<SendMessageBatchResult> thirdRetryResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(thirdRetryResults).when(underTest).sendBatches(QUEUE_URL, thirdRetries,
                LOG_FORMAT);
        doReturn(false).when(underTest).hasFailure(thirdRetryResults);

        doReturn(MESSAGE_CONTENTS).when(underTest).getContentsFrom(anyList());
        doReturn(logger).when(underTest).getLogger();

        // when
        List<SendMessageBatchResult> actual = underTest.resendFailedMessages(QUEUE_URL,
                originalResults, LOG_FORMAT);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
        verify(logger).warn(RETRY_FORMAT, 1, MESSAGE_CONTENTS);
    }

    @Test
    public void testResendFailedMessagesShouldResendFailedMessagesAndReturnFailedResultsWhenSendingFailsFourTimesInARow()
            throws InterruptedException {
        // given
        List<SendMessageBatchResult> originalResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(true).when(underTest).hasFailure(originalResults);
        List<SendMessageBatchRequestEntry> firstRetries = Arrays
                .asList(new SendMessageBatchRequestEntry()
                        .withMessageBody(MESSAGE_CONTENTS_1));
        doReturn(firstRetries).when(underTest).createRequestsFrom(originalResults);

        List<SendMessageBatchResult> firstRetryResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(firstRetryResults).when(underTest).sendBatches(QUEUE_URL, firstRetries,
                null);
        doReturn(true).when(underTest).hasFailure(firstRetryResults);
        List<SendMessageBatchRequestEntry> secondRetries = Arrays
                .asList(new SendMessageBatchRequestEntry()
                        .withMessageBody(MESSAGE_CONTENTS_2));
        doReturn(secondRetries).when(underTest).createRequestsFrom(firstRetryResults);

        List<SendMessageBatchResult> secondRetryResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(secondRetryResults).when(underTest).sendBatches(QUEUE_URL,
                secondRetries, null);
        doReturn(true).when(underTest).hasFailure(secondRetryResults);
        List<SendMessageBatchRequestEntry> thirdRetries = Arrays
                .asList(new SendMessageBatchRequestEntry()
                        .withMessageBody(MESSAGE_CONTENTS_3));
        doReturn(thirdRetries).when(underTest).createRequestsFrom(secondRetryResults);

        List<SendMessageBatchResult> thirdRetryResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(thirdRetryResults).when(underTest).sendBatches(QUEUE_URL, thirdRetries,
                null);
        doReturn(true).when(underTest).hasFailure(thirdRetryResults);

        doReturn(MESSAGE_CONTENTS).when(underTest).getContentsFrom(anyList());

        // when
        List<SendMessageBatchResult> actual = underTest.resendFailedMessages(QUEUE_URL,
                originalResults, null);

        // then
        assertSame(thirdRetryResults, actual);
    }

    @Test
    public void testResendFailedMessagesShouldResendFailedMessagesAndReturnFailedResultsAndLoggingWhenSendingFailsFourTimesInARowAndLogFormatIsNotNull()
            throws InterruptedException {
        // given
        List<SendMessageBatchResult> originalResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(true).when(underTest).hasFailure(originalResults);
        List<SendMessageBatchRequestEntry> firstRetries = Arrays
                .asList(new SendMessageBatchRequestEntry()
                        .withMessageBody(MESSAGE_CONTENTS_1));
        doReturn(firstRetries).when(underTest).createRequestsFrom(originalResults);

        List<SendMessageBatchResult> firstRetryResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(firstRetryResults).when(underTest).sendBatches(QUEUE_URL, firstRetries,
                LOG_FORMAT);
        doReturn(true).when(underTest).hasFailure(firstRetryResults);
        List<SendMessageBatchRequestEntry> secondRetries = Arrays
                .asList(new SendMessageBatchRequestEntry()
                        .withMessageBody(MESSAGE_CONTENTS_2));
        doReturn(secondRetries).when(underTest).createRequestsFrom(firstRetryResults);

        List<SendMessageBatchResult> secondRetryResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(secondRetryResults).when(underTest).sendBatches(QUEUE_URL, secondRetries,
                LOG_FORMAT);
        doReturn(true).when(underTest).hasFailure(secondRetryResults);
        List<SendMessageBatchRequestEntry> thirdRetries = Arrays
                .asList(new SendMessageBatchRequestEntry()
                        .withMessageBody(MESSAGE_CONTENTS_3));
        doReturn(thirdRetries).when(underTest).createRequestsFrom(secondRetryResults);

        List<SendMessageBatchResult> thirdRetryResults = Arrays
                .asList(mock(SendMessageBatchResult.class));
        doReturn(thirdRetryResults).when(underTest).sendBatches(QUEUE_URL, thirdRetries,
                LOG_FORMAT);
        doReturn(true).when(underTest).hasFailure(thirdRetryResults);
        doReturn(logger).when(underTest).getLogger();

        doReturn(MESSAGE_CONTENTS).when(underTest).getContentsFrom(anyList());

        // when
        List<SendMessageBatchResult> actual = underTest.resendFailedMessages(QUEUE_URL,
                originalResults, LOG_FORMAT);

        // then
        assertSame(thirdRetryResults, actual);
        verify(logger).warn(RETRY_FORMAT, 1, "messageContents1");
        verify(logger).warn(RETRY_FORMAT, 2, "messageContents2");
        verify(logger).warn(RETRY_FORMAT, 3, "messageContents3");
        verify(slackService).send("Fail %s %s", Country.getCurrentCountry().getCode(),
                MESSAGE_CONTENTS);
    }

    @Test
    public void testHasFailureShouldReturnFalseWhenGivenNoFailedData() {
        // given
        List<SendMessageBatchResult> results = createSuccessfulResults();

        // when
        boolean actual = underTest.hasFailure(results);

        // then
        assertFalse(actual);
    }

    @Test
    public void testHasFailureShouldReturnTrueWhenGivenFailedData() {
        // given
        List<SendMessageBatchResult> results = createSuccessfulResults();
        results.addAll(createFailedResults());

        // when
        boolean actual = underTest.hasFailure(results);

        // then
        assertTrue(actual);
    }

    @Test
    public void testGetContentsFromShouldReturnCorrectDataWhenCalled() {
        // given
        List<SendMessageBatchRequestEntry> requests = createSendMessageBatchRequestEntries();
        String expected = requests.get(0).getMessageBody() + System.lineSeparator()
                + requests.get(1).getMessageBody() + System.lineSeparator();

        // when
        String actual = underTest.getContentsFrom(requests);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateRequestsFromShouldReturnCorrectDataWhenCalled() {
        // given
        List<SendMessageBatchResult> results = createFailedResults();
        List<BatchResultErrorEntry> expected = results.get(0).getFailed();

        // when
        List<SendMessageBatchRequestEntry> actual = underTest.createRequestsFrom(results);

        // then
        assertNotNull(actual);
        assertFalse(actual.isEmpty());
        assertEquals(2, actual.size());
        assertFields(expected.get(0), actual.get(0));
        assertFields(expected.get(1), actual.get(1));
    }

    /**
     * {@link SimpleQueueServiceQueue} implementation to be used in the unit tests.
     *
     * <AUTHOR> Varga
     */
    private static class TestQueue extends SimpleQueueServiceQueue {

        @Override
        protected String getPartialQueueName() {
            return QUEUE_NAME;
        }
    }

    private List<SendMessageBatchRequestEntry>
                createSendMessageBatchRequestEntriesOverMaximumSize() {
        List<SendMessageBatchRequestEntry> requestEntry = new LinkedList<>();
        for (int i = 0; i < 15; i++) {
            requestEntry.add(new SendMessageBatchRequestEntry().withId(ID + i)
                    .withMessageBody(MESSAGE_CONTENTS + i));
        }

        return requestEntry;
    }

    private List<String> createReceiptHandlesOverMaximumSize() {
        return Arrays.asList(RECEIPT_HANDLE_1, RECEIPT_HANDLE_2, RECEIPT_HANDLE_3,
                RECEIPT_HANDLE_4, RECEIPT_HANDLE_5, RECEIPT_HANDLE_6, RECEIPT_HANDLE_7,
                RECEIPT_HANDLE_8, RECEIPT_HANDLE_9, RECEIPT_HANDLE_10, RECEIPT_HANDLE_11);
    }

    private List<DeleteMessageBatchRequestEntry>
                createExpectedDeleteMessageRequests() {
        return Arrays.asList(
                new DeleteMessageBatchRequestEntry(DELETE_REQUEST_ID_1, RECEIPT_HANDLE_1),
                new DeleteMessageBatchRequestEntry(DELETE_REQUEST_ID_2, RECEIPT_HANDLE_2),
                new DeleteMessageBatchRequestEntry(DELETE_REQUEST_ID_3, RECEIPT_HANDLE_3),
                new DeleteMessageBatchRequestEntry(DELETE_REQUEST_ID_4, RECEIPT_HANDLE_4),
                new DeleteMessageBatchRequestEntry(DELETE_REQUEST_ID_5, RECEIPT_HANDLE_5),
                new DeleteMessageBatchRequestEntry(DELETE_REQUEST_ID_6, RECEIPT_HANDLE_6),
                new DeleteMessageBatchRequestEntry(DELETE_REQUEST_ID_7, RECEIPT_HANDLE_7),
                new DeleteMessageBatchRequestEntry(DELETE_REQUEST_ID_8, RECEIPT_HANDLE_8),
                new DeleteMessageBatchRequestEntry(DELETE_REQUEST_ID_9, RECEIPT_HANDLE_9),
                new DeleteMessageBatchRequestEntry(DELETE_REQUEST_ID_10,
                        RECEIPT_HANDLE_10),
                new DeleteMessageBatchRequestEntry(DELETE_REQUEST_ID_11,
                        RECEIPT_HANDLE_11));
    }

    private List<SendMessageBatchResult> createSuccessfulResults() {
        List<SendMessageBatchResult> expectedSendMessageBatchResult = new LinkedList<>();
        SendMessageBatchResult result = new SendMessageBatchResult();
        List<SendMessageBatchResultEntry> successList = new LinkedList<>();
        successList.add(new SendMessageBatchResultEntry());
        result.setSuccessful(successList);
        expectedSendMessageBatchResult.add(result);

        return expectedSendMessageBatchResult;
    }

    private List<SendMessageBatchResult> createFailedResults() {
        List<SendMessageBatchResult> expectedSendMessageBatchResult = new LinkedList<>();
        SendMessageBatchResult result = new SendMessageBatchResult();
        List<BatchResultErrorEntry> failList = new LinkedList<>();
        BatchResultErrorEntry errorEntry1 = new BatchResultErrorEntry();
        errorEntry1.setId(FAILED_MESSAGE_ID_1);
        errorEntry1.setMessage(FAILED_MESSAGE_CONTENT_1);
        BatchResultErrorEntry errorEntry2 = new BatchResultErrorEntry();
        errorEntry2.setId(FAILED_MESSAGE_ID_2);
        errorEntry2.setMessage(FAILED_MESSAGE_CONTENT_2);
        failList.add(errorEntry1);
        failList.add(errorEntry2);
        result.setFailed(failList);
        expectedSendMessageBatchResult.add(result);

        return expectedSendMessageBatchResult;
    }

    private List<SendMessageBatchRequestEntry> createSendMessageBatchRequestEntries() {
        SendMessageBatchRequestEntry requestEntry1 = new SendMessageBatchRequestEntry(
                FAILED_MESSAGE_ID_1, FAILED_MESSAGE_CONTENT_1);
        SendMessageBatchRequestEntry requestEntry2 = new SendMessageBatchRequestEntry(
                FAILED_MESSAGE_ID_2, FAILED_MESSAGE_CONTENT_2);

        return Arrays.asList(requestEntry1, requestEntry2);
    }

    private void assertFields(BatchResultErrorEntry expected,
            SendMessageBatchRequestEntry actual) {
        assertNotNull(actual);
        assertEquals(expected.getId(), actual.getId());
        assertEquals(expected.getMessage(), actual.getMessageBody());
    }
}
