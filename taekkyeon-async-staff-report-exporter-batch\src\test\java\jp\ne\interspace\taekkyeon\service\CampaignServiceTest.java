/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.google.common.collect.Sets;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CampaignStatus;
import jp.ne.interspace.taekkyeon.model.CampaignType;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignMapper;

import static jp.ne.interspace.taekkyeon.model.CampaignStatus.OTHER;
import static jp.ne.interspace.taekkyeon.model.CampaignStatus.WONT_RUN;
import static jp.ne.interspace.taekkyeon.model.CampaignType.CPL;
import static jp.ne.interspace.taekkyeon.model.CampaignType.CPS;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link CampaignService}.
 *
 * <AUTHOR> Mayur
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class CampaignServiceTest {

    private static final String CAMPAIGNS_NOT_FOUND_ERROR_MESSAGE = "No campaigns were found for the given criteria.";

    private static final Set<Long> MERCHANT_IDS = Sets.newHashSet(1L);
    private static final Set<Long> CAMPAIGN_IDS = Sets.newHashSet(2L);
    private static final List<CampaignType> CAMPAIGN_TYPES = Arrays.asList(CPL, CPS);
    private static final List<Long> CAMPAIGN_CATEGORIES = Arrays.asList(5L, 6L, 7L);
    private static final List<CampaignStatus> CAMPAIGN_STATUSES = Arrays.asList(OTHER,
            WONT_RUN);
    private static final Set<Long> CAMPAIGN_IDS_BY_MERCHANTS_AND_CAMPAIGNS1 = Sets
            .newHashSet(1L, 2L);
    private static final Set<Long> CAMPAIGN_IDS_BY_TYPES_AND_CATEGORIES_AND_STATUSES1 = Sets
            .newHashSet(2L, 3L);
    private static final String COUNTRY_CODE = "MY";
    private static final int PARTITION_LIMIT = 10;

    @Spy @InjectMocks
    private CampaignService underTest;

    @Mock
    private CampaignMapper campaignMapper;

    @Test
    public void testFindCampaignIdsByShouldReturnCorrectCampaignIdsWhenCalled() {
        // given
        Set<Long> campaignIds = Sets.newHashSet(1L, 2L, 3L);
        doReturn(campaignIds).when(underTest).findCampaignIdsBy(MERCHANT_IDS,
                CAMPAIGN_IDS);
        Set<Long> expected = Sets.newHashSet(1L, 2L);
        doReturn(expected).when(underTest).findCampaignIdsBy(campaignIds, COUNTRY_CODE);

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(MERCHANT_IDS, CAMPAIGN_IDS,
                COUNTRY_CODE);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testFindCampaignIdsByShouldReturnCorrectCampaignIdsWhenCampaignIdsByGivenCampaignIdAndCountryCodeExists() {
        // given
        Set<Long> campaignIds = Sets.newHashSet(1L, 2L, 3L, 4L);
        Set<Long> expected = Sets.newHashSet(2L, 3L);
        doReturn(campaignIds).when(underTest).findCampaignIdsBy(MERCHANT_IDS,
                CAMPAIGN_IDS, CAMPAIGN_TYPES, CAMPAIGN_CATEGORIES, CAMPAIGN_STATUSES);
        doReturn(expected).when(underTest).findCampaignIdsBy(campaignIds, COUNTRY_CODE);

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(MERCHANT_IDS, CAMPAIGN_IDS,
                CAMPAIGN_TYPES, CAMPAIGN_CATEGORIES, CAMPAIGN_STATUSES, COUNTRY_CODE);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testFindCampaignIdsByShouldReturnCorrectCampaignIdsWhenCampaignIdsByTypesAndCategoriesAndStatusesIsEmpty() {
        // given
        doReturn(CAMPAIGN_IDS_BY_MERCHANTS_AND_CAMPAIGNS1).when(underTest)
                .findCampaignIdsBy(MERCHANT_IDS, CAMPAIGN_IDS);
        doReturn(Sets.newHashSet()).when(underTest)
                .findCampaignIdsByTypesAndCategoriesAndStatuses(CAMPAIGN_TYPES,
                        CAMPAIGN_CATEGORIES, CAMPAIGN_STATUSES);

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(MERCHANT_IDS, CAMPAIGN_IDS,
                CAMPAIGN_TYPES, CAMPAIGN_CATEGORIES, CAMPAIGN_STATUSES);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertTrue(actual.contains(1L));
        assertTrue(actual.contains(2L));
    }

    @Test
    public void testFindCampaignIdsByShouldReturnCorrectCampaignIdsWhenCampaignIdsByMerchantsAndCampaignsIsEmpty() {
        // given
        doReturn(Sets.newHashSet()).when(underTest).findCampaignIdsBy(MERCHANT_IDS,
                CAMPAIGN_IDS);
        doReturn(CAMPAIGN_IDS_BY_TYPES_AND_CATEGORIES_AND_STATUSES1).when(underTest)
                .findCampaignIdsByTypesAndCategoriesAndStatuses(CAMPAIGN_TYPES,
                        CAMPAIGN_CATEGORIES, CAMPAIGN_STATUSES);

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(MERCHANT_IDS, CAMPAIGN_IDS,
                CAMPAIGN_TYPES, CAMPAIGN_CATEGORIES, CAMPAIGN_STATUSES);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertTrue(actual.contains(2L));
        assertTrue(actual.contains(3L));
    }

    @Test
    public void testFindCampaignIdsByShouldReturnCorrectCampaignIdsWhenIntersectionExists() {
        // given
        doReturn(CAMPAIGN_IDS_BY_MERCHANTS_AND_CAMPAIGNS1).when(underTest)
                .findCampaignIdsBy(MERCHANT_IDS, CAMPAIGN_IDS);
        doReturn(CAMPAIGN_IDS_BY_TYPES_AND_CATEGORIES_AND_STATUSES1).when(underTest)
                .findCampaignIdsByTypesAndCategoriesAndStatuses(CAMPAIGN_TYPES,
                        CAMPAIGN_CATEGORIES, CAMPAIGN_STATUSES);
        Set<Long> expected = Sets.newHashSet(2L);
        doNothing().when(underTest).validateIds(expected);

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(MERCHANT_IDS, CAMPAIGN_IDS,
                CAMPAIGN_TYPES, CAMPAIGN_CATEGORIES, CAMPAIGN_STATUSES);

        // then
        assertEquals(expected, actual);
        verify(underTest).validateIds(expected);
    }

    @Test
    public void testFindCampaignIdsByShouldReturnCorrectCampaignIdsWhenBothMerchantIdsAndCampaignIdsAreNotEmptyAndCampaignIdsAreFound() {
        // given
        List<Long> partitionedCampaignIds = Arrays.asList(2L);
        Set<Long> merchantIds = Sets.newHashSet(1L);
        Set<Long> campaignIds = Sets.newHashSet(2L);
        Set<Long> expected = Sets.newHashSet(2L);

        when(campaignMapper.findCampaignIdsByMerchantIdsAndCampaignIds(merchantIds,
                partitionedCampaignIds)).thenReturn(expected);
        doNothing().when(underTest).validateIds(expected);
        doReturn(PARTITION_LIMIT).when(underTest).getOracleInLimit();

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(merchantIds, campaignIds);

        // then
        assertEquals(expected, actual);

        verify(campaignMapper).findCampaignIdsByMerchantIdsAndCampaignIds(merchantIds,
                partitionedCampaignIds);
        verify(underTest).validateIds(expected);
    }

    @Test
    public void testFindCampaignIdsByShouldReturnCorrectCampaignIdsWhenMerchantIdsIsNotEmptyAndCampaignIdsIsEmpty() {
        // given
        List<Long> partitionedMerchantIds1 = Arrays.asList(1L);
        List<Long> partitionedMerchantIds2 = Arrays.asList(2L);
        Set<Long> merchantIds = Sets.newHashSet(1L, 2L);
        Set<Long> campaignIds = Collections.emptySet();
        Set<Long> expected1 = Sets.newHashSet(1L);
        Set<Long> expected2 = Sets.newHashSet(2L);
        Set<Long> expected = Sets.newHashSet(1L, 2L);

        when(campaignMapper.findCampaignIdsBy(partitionedMerchantIds1))
                .thenReturn(expected1);
        when(campaignMapper.findCampaignIdsBy(partitionedMerchantIds2))
                .thenReturn(expected2);
        doNothing().when(underTest).validateIds(expected);
        doReturn(1).when(underTest).getOracleInLimit();

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(merchantIds, campaignIds);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertEquals(expected, actual);

        verify(campaignMapper).findCampaignIdsBy(partitionedMerchantIds1);
        verify(campaignMapper).findCampaignIdsBy(partitionedMerchantIds2);
        verify(underTest).validateIds(expected);
    }

    @Test
    public void testFindCampaignIdsByShouldReturnCorrectCampaignIdsWhenMerchantIdsIsNotEmptyAndCampaignIdsIsNull() {
        // given
        List<Long> partitionedMerchantIds = Arrays.asList(1L);
        Set<Long> merchantIds = Sets.newHashSet(1L);
        Set<Long> campaignIds = null;
        Set<Long> expected = Sets.newHashSet(2L);

        when(campaignMapper.findCampaignIdsBy(partitionedMerchantIds))
                .thenReturn(expected);
        doNothing().when(underTest).validateIds(expected);
        doReturn(PARTITION_LIMIT).when(underTest).getOracleInLimit();

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(merchantIds, campaignIds);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertEquals(expected, actual);

        verify(campaignMapper).findCampaignIdsBy(partitionedMerchantIds);
        verify(underTest).validateIds(expected);
    }

    @Test
    public void testFindCampaignIdsByShouldReturnCorrectCampaignIdsWhenMerchantIdsIsEmptyAndCampaignIdsIsNotEmpty() {
        // given
        Set<Long> merchantIds = Collections.emptySet();
        Set<Long> campaignIds = Sets.newHashSet(2L);

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(merchantIds, campaignIds);

        // then
        assertEquals(campaignIds, actual);

        verify(campaignMapper, never()).findCampaignIdsByMerchantIdsAndCampaignIds(any(),
                any());
        verify(campaignMapper, never()).findCampaignIdsBy(any());
    }

    @Test
    public void testFindCampaignIdsByShouldReturnCorrectCampaignIdsWhenMerchantIdsIsNullAndCampaignIdsIsNotEmpty() {
        // given
        Set<Long> merchantIds = null;
        Set<Long> campaignIds = Sets.newHashSet(2L);
        List<Long> partitionedCampaignIds = Arrays.asList(2L);

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(merchantIds, campaignIds);

        // then
        assertEquals(campaignIds, actual);

        verify(campaignMapper, never()).findCampaignIdsByMerchantIdsAndCampaignIds(
                merchantIds, partitionedCampaignIds);
        verify(campaignMapper, never()).findCampaignIdsBy(any());
    }

    @Test
    public void testFindCampaignIdsByShouldReturnEmptySetWhenBothMerchantIdsAndCampaignIdsAreEmpty() {
        // given
        Set<Long> merchantIds = Collections.emptySet();
        Set<Long> campaignIds = Collections.emptySet();

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(merchantIds, campaignIds);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());

        verify(campaignMapper, never()).findCampaignIdsByMerchantIdsAndCampaignIds(any(),
                any());
        verify(campaignMapper, never()).findCampaignIdsBy(any());
    }

    @Test
    public void testFindCampaignIdsByShouldReturnEmptySetWhenBothMerchantIdsAndCampaignIdsAreNull() {
        // given
        Set<Long> merchantIds = null;
        Set<Long> campaignIds = null;

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(merchantIds, campaignIds);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());

        verify(campaignMapper, never()).findCampaignIdsByMerchantIdsAndCampaignIds(any(),
                any());
        verify(campaignMapper, never()).findCampaignIdsBy(any());
    }

    @Test
    public void testFindCampaignIdsByShouldReturnCorrectCampaignIdsWhenCampaignIdsGivenByCampaignIdsAndCountryCodeExists() {
        // given
        Set<Long> campaignIds = Sets.newHashSet(1L, 2L, 3L);
        Set<Long> expected = Sets.newHashSet(2L, 3L);
        List<Long> partitionedCampaignIds = Arrays.asList(1L, 2L, 3L);
        when(campaignMapper.findCampaignIdsByCampaignIdsAndCountryCode(
                partitionedCampaignIds, COUNTRY_CODE)).thenReturn(expected);
        doReturn(PARTITION_LIMIT).when(underTest).getOracleInLimit();

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(campaignIds, COUNTRY_CODE);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testFindCampaignIdsByShouldReturnEmptyWhenGivenCampaignIdsIsEmpty() {
        // given
        Set<Long> campaignIds = Collections.emptySet();
        Set<Long> expected = Collections.emptySet();

        // when
        Set<Long> actual = underTest.findCampaignIdsBy(campaignIds, COUNTRY_CODE);

        // then
        assertSame(expected, actual);
        verifyZeroInteractions(campaignMapper);
        verify(underTest, never()).validateIds(any());
    }

    @Test(expected = Exception.class)
    public void testFindCampaignIdsByShouldThrowExceptionWhenCampaignIdsByGivenCampaignIdsAndCountryCodeDoesNotExists() {
        // given
        Set<Long> campaignIds = Sets.newHashSet(1L, 2L, 3L);
        List<Long> partitionedCampaignIds = Arrays.asList(1L, 2L, 3L);
        Set<Long> expected = Collections.emptySet();
        when(campaignMapper.findCampaignIdsByCampaignIdsAndCountryCode(
                partitionedCampaignIds, COUNTRY_CODE)).thenReturn(expected);
        doReturn(PARTITION_LIMIT).when(underTest).getOracleInLimit();

        // when
        underTest.findCampaignIdsBy(campaignIds, COUNTRY_CODE);
    }

    @Test
    public void testFindCampaignIdsByTypesAndCategoriesAndStatusesShouldReturnCorrectCampaignIdsWhenCategoriesAreNotNullAndNotEmptyAndDataExists() {
        // given
        List<CampaignType> campaignTypes = Collections.emptyList();
        List<Long> campaignCategories = Arrays.asList(5L, 6L, 7L);
        List<CampaignStatus> campaignStatuses = Collections.emptyList();
        Set<Long> expected = Sets.newHashSet(2L);

        when(campaignMapper.findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignCategories, campaignTypes, campaignStatuses))
                        .thenReturn(expected);

        // when
        Set<Long> actual = underTest.findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignTypes, campaignCategories, campaignStatuses);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertSame(expected, actual);

        verify(campaignMapper).findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignCategories, campaignTypes, campaignStatuses);
    }

    @Test
    public void testFindCampaignIdsByTypesAndCategoriesAndStatusesShouldThrowExceptionWhenCategoriesAreNotNullAndNotEmptyAndDataDoesNotExist() {
        // given
        List<CampaignType> campaignTypes = Collections.emptyList();
        List<Long> campaignCategories = Arrays.asList(5L, 6L, 7L);
        List<CampaignStatus> campaignStatuses = Collections.emptyList();

        when(campaignMapper.findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignCategories, campaignTypes, campaignStatuses))
                        .thenReturn(Sets.newHashSet());

        // when
        try {
            underTest.findCampaignIdsByTypesAndCategoriesAndStatuses(campaignTypes,
                    campaignCategories, campaignStatuses);

            // then
            fail();
        } catch (TaekkyeonException actual) {
            assertEquals(CAMPAIGNS_NOT_FOUND_ERROR_MESSAGE, actual.getMessage());
        }

        verify(campaignMapper).findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignCategories, campaignTypes, campaignStatuses);
    }

    @Test
    public void testFindCampaignIdsByTypesAndCategoriesAndStatusesShouldReturnCorrectCampaignIdsWhenTypesAndCategoriesAndStatusesAreNotNullAndNotEmptyAndDataExists() {
        // given
        List<CampaignType> campaignTypes = Arrays.asList(CPL, CPS);
        List<Long> campaignCategories = Arrays.asList(5L, 6L, 7L);
        List<CampaignStatus> campaignStatuses = Arrays.asList(OTHER, WONT_RUN);
        Set<Long> expected = Sets.newHashSet(2L);

        when(campaignMapper.findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignCategories, campaignTypes, campaignStatuses))
                        .thenReturn(expected);

        // when
        Set<Long> actual = underTest.findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignTypes, campaignCategories, campaignStatuses);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertSame(expected, actual);

        verify(campaignMapper).findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignCategories, campaignTypes, campaignStatuses);
    }

    @Test
    public void testFindCampaignIdsByTypesAndCategoriesAndStatusesShouldThrowExceptionWhenTypesAndCategoriesAndStatusesAreNotNullAndNotEmptyAndDataDoesNotExist() {
        // given
        List<CampaignType> campaignTypes = Arrays.asList(CPL, CPS);
        List<Long> campaignCategories = Arrays.asList(5L, 6L, 7L);
        List<CampaignStatus> campaignStatuses = Arrays.asList(OTHER, WONT_RUN);

        when(campaignMapper.findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignCategories, campaignTypes, campaignStatuses))
                        .thenReturn(Sets.newHashSet());

        // when
        try {
            underTest.findCampaignIdsByTypesAndCategoriesAndStatuses(campaignTypes,
                    campaignCategories, campaignStatuses);

            // then
            fail();
        } catch (TaekkyeonException actual) {
            assertEquals(CAMPAIGNS_NOT_FOUND_ERROR_MESSAGE, actual.getMessage());
        }

        verify(campaignMapper).findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignCategories, campaignTypes, campaignStatuses);
    }

    @Test
    public void testFindCampaignIdsByTypesAndCategoriesAndStatusesShouldReturnCorrectCampaignIdsWhenTypesIsNotNullAndNotEmptyAndDataExists() {
        // given
        List<CampaignType> campaignTypes = Arrays.asList(CPL, CPS);
        List<Long> campaignCategories = Collections.emptyList();
        List<CampaignStatus> campaignStatuses = Collections.emptyList();
        Set<Long> expected = Sets.newHashSet(2L);

        when(campaignMapper.findCampaignIdsByTypesAndStatuses(campaignTypes,
                campaignStatuses)).thenReturn(expected);

        // when
        Set<Long> actual = underTest.findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignTypes, campaignCategories, campaignStatuses);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertSame(expected, actual);

        verify(campaignMapper).findCampaignIdsByTypesAndStatuses(campaignTypes,
                campaignStatuses);
    }

    @Test
    public void testFindCampaignIdsByTypesAndCategoriesAndStatusesShouldThrowExceptionWhenTypesIsNotNullAndNotEmptyAndDataDoesNotExist() {
        // given
        List<CampaignType> campaignTypes = Arrays.asList(CPL, CPS);
        List<Long> campaignCategories = Collections.emptyList();
        List<CampaignStatus> campaignStatuses = Collections.emptyList();

        when(campaignMapper.findCampaignIdsByTypesAndStatuses(campaignTypes,
                campaignStatuses)).thenReturn(Sets.newHashSet());

        // when
        try {
            underTest.findCampaignIdsByTypesAndCategoriesAndStatuses(campaignTypes,
                    campaignCategories, campaignStatuses);

            // then
            fail();
        } catch (TaekkyeonException actual) {
            assertEquals(CAMPAIGNS_NOT_FOUND_ERROR_MESSAGE, actual.getMessage());
        }

        verify(campaignMapper).findCampaignIdsByTypesAndStatuses(campaignTypes,
                campaignStatuses);
    }

    @Test
    public void testFindCampaignIdsByTypesAndCategoriesAndStatusesShouldReturnCorrectCampaignIdsWhenStatusesIsNotNullAndNotEmptyAndDataExists() {
        // given
        List<CampaignType> campaignTypes = Collections.emptyList();
        List<Long> campaignCategories = Collections.emptyList();
        List<CampaignStatus> campaignStatuses = Arrays.asList(OTHER, WONT_RUN);
        Set<Long> expected = Sets.newHashSet(2L);

        when(campaignMapper.findCampaignIdsByTypesAndStatuses(campaignTypes,
                campaignStatuses)).thenReturn(expected);

        // when
        Set<Long> actual = underTest.findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignTypes, campaignCategories, campaignStatuses);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertSame(expected, actual);

        verify(campaignMapper).findCampaignIdsByTypesAndStatuses(campaignTypes,
                campaignStatuses);
    }

    @Test
    public void testFindCampaignIdsByTypesAndCategoriesAndStatusesShouldThrowExceptionWhenStatusesIsNotNullAndNotEmptyAndDataDoesNotExist() {
        // given
        List<CampaignType> campaignTypes = Collections.emptyList();
        List<Long> campaignCategories = Collections.emptyList();
        List<CampaignStatus> campaignStatuses = Arrays.asList(OTHER, WONT_RUN);

        when(campaignMapper.findCampaignIdsByTypesAndStatuses(campaignTypes,
                campaignStatuses)).thenReturn(Sets.newHashSet());

        // when
        try {
            underTest.findCampaignIdsByTypesAndCategoriesAndStatuses(campaignTypes,
                    campaignCategories, campaignStatuses);

            // then
            fail();
        } catch (TaekkyeonException actual) {
            assertEquals(CAMPAIGNS_NOT_FOUND_ERROR_MESSAGE, actual.getMessage());
        }

        verify(campaignMapper).findCampaignIdsByTypesAndStatuses(campaignTypes,
                campaignStatuses);
    }

    @Test
    public void testFindCampaignIdsByTypesAndStatusesShouldReturnCorrectCampaignIdsWhenTypesAndStatusesAreNotNullAndNotEmptyAndDataExists() {
        // given
        List<CampaignType> campaignTypes = Arrays.asList(CPL, CPS);
        List<Long> campaignCategories = Collections.emptyList();
        List<CampaignStatus> campaignStatuses = Arrays.asList(OTHER, WONT_RUN);
        Set<Long> expected = Sets.newHashSet(2L);

        when(campaignMapper.findCampaignIdsByTypesAndStatuses(campaignTypes,
                campaignStatuses)).thenReturn(expected);

        // when
        Set<Long> actual = underTest.findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignTypes, campaignCategories, campaignStatuses);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertSame(expected, actual);

        verify(campaignMapper).findCampaignIdsByTypesAndStatuses(campaignTypes,
                campaignStatuses);
    }

    @Test
    public void testFindCampaignIdsByTypesAndStatusesShouldThrowExceptionWhenTypesAndStatusesAreNotNullAndNotEmptyAndDataDoesNotExist() {
        // given
        List<CampaignType> campaignTypes = Arrays.asList(CPL, CPS);
        List<Long> campaignCategories = Collections.emptyList();
        List<CampaignStatus> campaignStatuses = Arrays.asList(OTHER, WONT_RUN);

        when(campaignMapper.findCampaignIdsByTypesAndStatuses(campaignTypes,
                campaignStatuses)).thenReturn(Sets.newHashSet());

        // when
        try {
            underTest.findCampaignIdsByTypesAndCategoriesAndStatuses(campaignTypes,
                    campaignCategories, campaignStatuses);

            // then
            fail();
        } catch (TaekkyeonException actual) {
            assertEquals(CAMPAIGNS_NOT_FOUND_ERROR_MESSAGE, actual.getMessage());
        }

        verify(campaignMapper).findCampaignIdsByTypesAndStatuses(campaignTypes,
                campaignStatuses);
    }

    @Test
    public void testFindCampaignIdsByTypesAndCategoriesAndStatusesShouldReturnEmptySetWhenTypesAndCategoriesAndStatusesAreNull() {
        // given
        List<CampaignType> campaignTypes = null;
        List<Long> campaignCategories = null;
        List<CampaignStatus> campaignStatuses = null;

        // when
        Set<Long> actual = underTest.findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignTypes, campaignCategories, campaignStatuses);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());

        verifyZeroInteractions(campaignMapper);
    }

    @Test
    public void testFindCampaignIdsByTypesAndCategoriesAndStatusesShouldReturnEmptySetWhenTypesAndCategoriesAndStatusesAreEmpty() {
        // given
        List<CampaignType> campaignTypes = Collections.emptyList();
        List<Long> campaignCategories = Collections.emptyList();
        List<CampaignStatus> campaignStatuses = Collections.emptyList();

        // when
        Set<Long> actual = underTest.findCampaignIdsByTypesAndCategoriesAndStatuses(
                campaignTypes, campaignCategories, campaignStatuses);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());

        verifyZeroInteractions(campaignMapper);
    }
}
