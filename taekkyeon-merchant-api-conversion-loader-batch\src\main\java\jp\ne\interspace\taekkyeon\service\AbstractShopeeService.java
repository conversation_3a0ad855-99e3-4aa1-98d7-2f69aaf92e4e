/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.hash.Hashing;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.model.CampaignAdvDetails;
import jp.ne.interspace.taekkyeon.model.CampaignAdvType;
import jp.ne.interspace.taekkyeon.model.ClickConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionsWithClickIdDetails;
import jp.ne.interspace.taekkyeon.model.CurrencyRate;
import jp.ne.interspace.taekkyeon.model.Merchant;
import jp.ne.interspace.taekkyeon.model.MerchantIntegrationHistoryInsertRequest;
import jp.ne.interspace.taekkyeon.model.Pair;
import jp.ne.interspace.taekkyeon.model.ShopeeConversionReport;
import jp.ne.interspace.taekkyeon.model.ShopeeItem;
import jp.ne.interspace.taekkyeon.model.ShopeeNode;
import jp.ne.interspace.taekkyeon.model.ShopeeOrder;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ApiKeyNameMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CurrencyMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.MerchantIntegrationHistoryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.secretsmanager.SecretsManagerClient;

import static com.google.common.base.Joiner.on;
import static com.google.common.base.Strings.isNullOrEmpty;
import static com.google.common.hash.Hashing.md5;
import static java.math.BigDecimal.ZERO;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.time.ZonedDateTime.of;
import static java.util.Collections.frequency;
import static java.util.concurrent.TimeUnit.MINUTES;
import static java.util.stream.Collectors.toList;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.HYPHEN;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.PIPE;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.UNDERSCORE;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.ZONE_IDS;
import static jp.ne.interspace.taekkyeon.model.CampaignAdvType.BRAND;
import static jp.ne.interspace.taekkyeon.model.CampaignAdvType.REGULAR;
import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static jp.ne.interspace.taekkyeon.module.Country.PHILIPPINES;
import static jp.ne.interspace.taekkyeon.module.Country.TAIWAN;
import static jp.ne.interspace.taekkyeon.module.Country.THAILAND;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_API_URL;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_CAMPAIGN_ID;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_IS_CHECK_DUPLICATE_CONVERSION;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_LIMIT_RESET_COUNT;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_LIMIT_TIME_CALL_API;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_MERCHANT;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_RECEIVED_CURRENCY;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_SCAN_TIME_FROM;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_SCAN_TIME_TO;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_SECRET_NAME;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_SLEEP_SECOND;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_TARGET_COUNTRY_CODE;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonSlackMessageSenderModule.BIND_KEY_SHOULD_SEND_NOTIFICATION_TO_SLACK;
import static lombok.AccessLevel.PACKAGE;
import static org.apache.http.HttpHeaders.AUTHORIZATION;
import static org.apache.http.HttpHeaders.CONTENT_TYPE;

/**
 * Abstract service for shopee.
 *
 * <AUTHOR> Shin
 */
@Slf4j
public abstract class AbstractShopeeService extends MerchantApiConversionService {

    private static final String BONUS = "-bonus";
    private static final String CONVERSION_REPORT = "conversionReport";
    private static final String DATA = "data";
    private static final long DEFAULT_CAMPAIGN_ID = 0L;
    private static final int MD5_LENGTH = 32;
    private static final int MAX_RETRY_COUNT = 20;
    private static final String ERRORS = "errors";
    private static final String ERROR_MESSAGE_FORMAT = "CURRENCY_RATE_ERROR: Invalid or non-existent currency conversion unit. \nTargetMonth: %s \nCampaignId : %s \nCurrency: %s";
    private static final String DIRECT_FORMAT = "%s-direct";
    private static final String IN_DIRECT_FORMAT = "%s-indirect";
    private static final String ORDERED_IN_SAME_SHOP = "ORDERED_IN_SAME_SHOP";
    private static final String AT = "-AT";
    private static final String KEY_SEPARATOR = "::";
    private static final String XTRA_COMM = "XTRAComm";
    private static final String REGEX_MD5 = "^[a-fA-F0-9]{32}$";

    @Inject
    private JsonSerializerService jsonSerializerService;

    @Inject @Named(BIND_KEY_SCAN_TIME_FROM) @Getter(PACKAGE)
    private LocalDateTime scanTimeFrom;

    @Inject @Named(BIND_KEY_SCAN_TIME_TO) @Getter(PACKAGE)
    private LocalDateTime scanTimeTo;

    @Inject @Named(BIND_KEY_TARGET_COUNTRY_CODE) @Getter(PACKAGE)
    private String targetCountryCode;

    @Inject @Named(BIND_KEY_SECRET_NAME) @Getter(PACKAGE)
    private String secretName;

    @Inject @Named(BIND_KEY_API_URL) @Getter(PACKAGE)
    private String apiUrl;

    @Inject @Named(BIND_KEY_CAMPAIGN_ID) @Getter(PACKAGE)
    private long campaignId;

    @Inject @Named(BIND_KEY_SLEEP_SECOND) @Getter(PACKAGE)
    private int sleepSecond;

    @Inject @Named(BIND_KEY_LIMIT_RESET_COUNT) @Getter(PACKAGE)
    private int limitResetTimeCount;

    @Inject @Named(BIND_KEY_LIMIT_TIME_CALL_API) @Getter(PACKAGE)
    private int limitTimeCallApi;

    @Inject @Named(BIND_KEY_RECEIVED_CURRENCY) @Getter(PACKAGE)
    private String receivedCurrency;

    @Inject @VisibleForTesting @Getter(PACKAGE)
    @Named(BIND_KEY_IS_CHECK_DUPLICATE_CONVERSION)
    private boolean isDuplicateConversionCheckEnabled;

    @Inject @VisibleForTesting @Getter(PACKAGE) @Named(BIND_KEY_MERCHANT)
    private Merchant merchant;

    @Inject @Named(BIND_KEY_SHOULD_SEND_NOTIFICATION_TO_SLACK) @Getter
    private boolean shouldSendNotificationToSlack;

    @Getter(PACKAGE)
    private String scrollId;

    @Getter(PACKAGE)
    private int emptyTimes;

    @Getter(PACKAGE)
    private String campaignName;

    @Getter(PACKAGE)
    private JsonParser jsonParser = new JsonParser();

    @Getter(PACKAGE)
    private int resetCount;

    @Getter(PACKAGE)
    private boolean isRequestTimeOut;

    @Inject
    private CampaignMapper campaignMapper;

    @Inject
    private CurrencyMapper currencyMapper;

    @Inject
    private SlackService slackService;

    @Inject
    private MerchantIntegrationHistoryMapper merchantIntegrationHistoryMapper;

    @Inject
    private ApiKeyNameMapper apiKeyNameMapper;

    private LoadingCache<String, Integer> merchantIntegrationKeyCache =
            CacheBuilder.newBuilder()
                    .maximumSize(3).expireAfterWrite(5, MINUTES)
                    .build(new CacheLoader<String, Integer>() {
                        @Override
                        public Integer load(String key) {
                            return merchantIntegrationHistoryMapper.findKeyExistingBy(
                                    key);
                        }
                    });

    private LoadingCache<String, CurrencyRate> campaignCurrencyCache = CacheBuilder.newBuilder()
            .maximumSize(5000)
            .build(new CacheLoader<String, CurrencyRate>() {
                @Override
                public CurrencyRate load(String key) {
                    return findCampaignCurrencyRateBy(key);
                }
            });

    private LoadingCache<String, CurrencyRate> currencyCache = CacheBuilder.newBuilder()
            .maximumSize(5000)
            .build(new CacheLoader<String, CurrencyRate>() {
                @Override
                public CurrencyRate load(String key) {
                    return findCurrencyRateBy(key);
                }
            });

    private final LoadingCache<Long, CampaignAdvType> campaignTypeCache =
            CacheBuilder.newBuilder()
                    .maximumSize(1000)
                    .expireAfterWrite(30, MINUTES)
                    .build(new CacheLoader<Long, CampaignAdvType>() {
                        @Override
                        public CampaignAdvType load(Long campaignId) {
                            CampaignAdvDetails details = apiKeyNameMapper
                                    .findAdvCampaignBy(campaignId);
                            return details != null ? details.getCampaignAdvType()
                                    : REGULAR;
                        }
                    });

    private final LoadingCache<Long, Long> parentCampaignCache =
            CacheBuilder.newBuilder()
                    .maximumSize(1000)
                    .expireAfterWrite(30, MINUTES)
                    .build(new CacheLoader<Long, Long>() {
                        @Override
                        public Long load(Long brandCampaignId) {
                            Long parentId = apiKeyNameMapper.findParentCampaignIdBy(
                                    BRAND.name(), brandCampaignId);
                            return parentId != null ? parentId : DEFAULT_CAMPAIGN_ID;
                        }
                    });

    private final LoadingCache<String, Long> brandCampaignCache =
            CacheBuilder.newBuilder()
                    .maximumSize(1000)
                    .expireAfterWrite(30, MINUTES)
                    .build(new CacheLoader<String, Long>() {
                        @Override
                        public Long load(String key) {
                            String[] parts = key.split(KEY_SEPARATOR);
                            String shopId = parts[0];
                            Long parentCampaignId = parts.length > 1 ? Long.parseLong(
                                    parts[1]) : null;
                            Long brandCampaignId = apiKeyNameMapper.findBrandCampaignId(
                                    shopId, parentCampaignId);
                            return brandCampaignId != null ? brandCampaignId
                                    : DEFAULT_CAMPAIGN_ID;
                        }
                    });

    private final LoadingCache<String, Boolean> brandItemCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, MINUTES)
            .build(new CacheLoader<String, Boolean>() {
                @Override
                public Boolean load(String shopId) {
                    return apiKeyNameMapper.isBrandItem(shopId);
                }
            });

    private final LoadingCache<String, Long> campaignByShopIdCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, MINUTES)
            .build(new CacheLoader<String, Long>() {
                @Override
                public Long load(String shopId) {
                    Long campaignId = apiKeyNameMapper.findCampaignBy(shopId);
                    return campaignId != null ? campaignId : DEFAULT_CAMPAIGN_ID;
                }
            });

    @Override
    public String getResponseBody() throws Exception {
        emptyTimes = 0;
        isRequestTimeOut = false;
        Pair<String, String> shopeeApiKey = getShopeeApiKey();
        do {
            ZonedDateTime requestStartTime = getZonedDateTimeNow();
            String responseBody = getResponse(shopeeApiKey.getLeft(),
                    shopeeApiKey.getRight());
            ZonedDateTime requestEndTime = getZonedDateTimeNow();
            Duration duration = Duration.between(requestStartTime, requestEndTime);
            sleep(duration);
            JsonObject obj = getJsonParser().parse(responseBody).getAsJsonObject();
            if (!obj.has(ERRORS)) {
                ShopeeConversionReport conversionReport = parseConversionReport(obj);
                if (isNullOrEmpty(conversionReport.getPageInfo().getScrollId())) {
                    emptyTimes++;
                    if (!conversionReport.getNodes().isEmpty()
                            && emptyTimes == MAX_RETRY_COUNT) {
                        return responseBody;
                    }
                } else {
                    emptyTimes = 0;
                    if (duration.getSeconds() > getLimitTimeCallApi()) {
                        scrollId = EMPTY;
                        isRequestTimeOut = true;
                        resetCount++;
                    } else {
                        scrollId = conversionReport.getPageInfo().getScrollId();
                    }
                    return responseBody;
                }
            } else {
                emptyTimes++;
            }
        } while (getEmptyTimes() < MAX_RETRY_COUNT);
        return null;
    }

    @Override
    public boolean isConversionUnique(String key) {
        return !isDuplicateConversionCheckEnabled() || !isDuplicateConversion(key);
    }

    @Override
    public boolean hasNext() {
        return getEmptyTimes() < MAX_RETRY_COUNT;
    }

    @Override
    public List<ConversionsWithClickIdDetails> createConversionsWithClickIdDetailsBy(
            List<ConversionRegistrationDetails> details) {
        Map<Long, List<ConversionRegistrationDetails>> tempConversionDetails = new HashMap<>();

        for (ConversionRegistrationDetails detail : details) {
            Long targetCampaignId = getCampaignId();

            if (detail instanceof ClickConversionRegistrationDetails) {
                ClickConversionRegistrationDetails clickDetail =
                        (ClickConversionRegistrationDetails) detail;
                targetCampaignId = clickDetail.getSubCampaignId();
            }

            if (!tempConversionDetails.containsKey(targetCampaignId)) {
                tempConversionDetails.put(targetCampaignId, new ArrayList<>());
            }

            tempConversionDetails
                    .computeIfAbsent(targetCampaignId, k -> new ArrayList<>())
                    .add(convertToConversionRegistrationDetails(detail));
        }

        List<ConversionsWithClickIdDetails> results = new ArrayList<>();
        for (Map.Entry<Long, List<ConversionRegistrationDetails>> entry :
                tempConversionDetails.entrySet()) {
            Long subCampaignId = entry.getKey();
            List<ConversionRegistrationDetails> conversions = entry.getValue();

            if (subCampaignId.equals(getCampaignId())) {
                results.add(new ConversionsWithClickIdDetails(subCampaignId,
                        DEFAULT_CAMPAIGN_ID, findCampaignName(), getCurrentLocalDate(),
                        conversions));
            } else {
                results.add(new ConversionsWithClickIdDetails(subCampaignId,
                        getCampaignId(), campaignMapper.findCampaignNameBy(subCampaignId),
                        getCurrentLocalDate(), conversions));
            }
        }

        return results;
    }

    @VisibleForTesting
    ConversionRegistrationDetails convertToConversionRegistrationDetails(
            ConversionRegistrationDetails detail) {
        if (detail instanceof ClickConversionRegistrationDetails) {
            return new ConversionRegistrationDetails(detail.getConversionTime(),
                    detail.getTransactionId(), detail.getResultId(),
                    detail.getCustomerType(), detail.getProductCategoryId(),
                    detail.getProductId(), detail.getProductUnitPrice(),
                    detail.getClickId());
        }
        return detail;
    }

    @Override
    public List<ConversionRegistrationDetails> getConversionData() throws Exception {
        List<ConversionRegistrationDetails> details = new LinkedList<>();
        resetCount = 0;
        do {
            if (isRequestTimeOut()) {
                details.clear();
            }
            String responseBody = getResponseBody();
            List<ConversionRegistrationDetails> parsed = parse(responseBody);
            if (!parsed.isEmpty()) {
                details.addAll(parsed);
                log.info("Loaded data size: {} of total: {}... ", parsed.size(),
                        details.size());
            }
        } while (getResetCount() < getLimitResetTimeCount() && hasNext());
        return details;
    }

    @VisibleForTesting
    boolean isBrandItem(String shopId) {
        if (isNullOrEmpty(shopId)) {
            return false;
        }
        return brandItemCache.getUnchecked(shopId);
    }

    @VisibleForTesting
    CampaignAdvType findCampaignType(Long campaignId) {
        return campaignTypeCache.getUnchecked(campaignId);
    }

    @VisibleForTesting
    boolean isBrandCampaign(Long campaignId) {
        if (campaignId == null || campaignId <= DEFAULT_CAMPAIGN_ID) {
            return false;
        }
        return findCampaignType(campaignId) == BRAND;
    }

    @VisibleForTesting
    List<ConversionRegistrationDetails> createExtraBonuses(
            ZonedDateTime conversionTime, String identifier, int resultId,
            String customerType, String categoryId, String productId, String clickId,
            ShopeeItem shopeeItem, ShopeeOrder shopeeOrder,
            List<ConversionRegistrationDetails> brandItemConversions) {

        BigDecimal grossBrandCommission = getGrossBrandCommission(shopeeItem);
        if (!isValidCommission(grossBrandCommission)) {
            return new ArrayList<>();
        }

        String productIdExtraComm = createExtraBonusProductId(shopeeItem, shopeeOrder,
                productId, grossBrandCommission);
        BigDecimal extraCommission = calculateExtraCommission(grossBrandCommission, conversionTime);

        List<ConversionRegistrationDetails> extraBonuses = new ArrayList<>();

        if (!isBrandCampaign(getCampaignId())) {
            extraBonuses.add(new ConversionRegistrationDetails(conversionTime, identifier,
                    resultId, customerType, categoryId, productIdExtraComm,
                    extraCommission, clickId));
        }

        List<ClickConversionRegistrationDetails> subConversions = extractSubConversions(
                brandItemConversions);
        for (ClickConversionRegistrationDetails subConversion : subConversions) {
            if (!isBrandCampaign(subConversion.getSubCampaignId())) {
                extraBonuses.add(new ClickConversionRegistrationDetails(conversionTime, identifier,
                        resultId, customerType, categoryId, productIdExtraComm, extraCommission,
                        clickId, subConversion.getSubCampaignId()));
            }
        }

        return extraBonuses;
    }

    @VisibleForTesting
    String createExtraBonusProductId(ShopeeItem shopeeItem, ShopeeOrder shopeeOrder,
            String productId, BigDecimal grossBrandCommission) {
        if (INDONESIA.getCode().equals(getTargetCountryCode())) {
            return productId + BONUS;
        } else if (THAILAND.getCode().equals(getTargetCountryCode())
                || PHILIPPINES.getCode().equals(getTargetCountryCode())) {
            return Joiner.on(HYPHEN).join(productId, XTRA_COMM);
        }
        return getProductId(shopeeItem, shopeeOrder.getOrderId(), productId,
                grossBrandCommission, XTRA_COMM);
    }

    @VisibleForTesting
    Long getAdvParentCampaignId(Long brandCampaignId) {
        return parentCampaignCache.getUnchecked(brandCampaignId);
    }

    @VisibleForTesting
    Long findBrandCampaignByShopId(String shopId, Long parentCampaignId) {
        if (isNullOrEmpty(shopId) || parentCampaignId == null) {
            return DEFAULT_CAMPAIGN_ID;
        }
        String cacheKey = shopId + KEY_SEPARATOR + parentCampaignId;
        return brandCampaignCache.getUnchecked(cacheKey);
    }

    @VisibleForTesting
    Long findCampaignBy(String shopId) {
        if (isNullOrEmpty(shopId)) {
            return DEFAULT_CAMPAIGN_ID;
        }
        return campaignByShopIdCache.getUnchecked(shopId);
    }

    @VisibleForTesting
    boolean isValidCommission(BigDecimal commission) {
        return commission != null && commission.compareTo(ZERO) > 0;
    }

    @VisibleForTesting
    String getClickIdFrom(String[] utmContent) {
        if (utmContent.length >= 2 && !isNullOrEmpty(utmContent[1])) {
            return utmContent[1].length() > 7 ? utmContent[1] : utmContent[0];
        }
        return EMPTY;
    }

    @VisibleForTesting
    String getProductId(ShopeeItem shopeeItem, String orderId, String productId,
            BigDecimal commission, String commissionType) {
        if (!isValidCommission(commission)) {
            return productId;
        }
        List<String> parts = new ArrayList<>();
        parts.add(orderId);
        parts.add(shopeeItem.getShopId());
        parts.add(shopeeItem.getItemId());
        parts.add(shopeeItem.getModelId());
        parts.add(shopeeItem.getGlobalCategoryLv3Name());
        parts.add(commissionType);

        return Joiner.on(UNDERSCORE).join(parts);
    }

    @VisibleForTesting
    String getItemId(List<String> items, String sku) {
        int itemFrequency = frequency(items, sku);
        return itemFrequency > 0 ? sku + AT + itemFrequency : sku;
    }

    @VisibleForTesting
    ShopeeConversionReport parseConversionReport(JsonObject obj) {
        return jsonSerializerService
                .fromJson(
                        obj.getAsJsonObject(DATA)
                                .getAsJsonObject(CONVERSION_REPORT).toString(),
                        ShopeeConversionReport.class);
    }

    @VisibleForTesting
    boolean isTargetCampaignId(String[] utmContents, String targetMd5CampaignId) {
        if ((utmContents.length >= 4 && !isNullOrEmpty(utmContents[3])
                && utmContents[3].equals(targetMd5CampaignId))
                || (utmContents.length >= 3 && !isNullOrEmpty(utmContents[2])
                && utmContents[2].equals(targetMd5CampaignId))) {
            return true;
        }
        return Arrays.stream(utmContents)
                .filter(content -> !isNullOrEmpty(content))
                .anyMatch(content -> isMd5Hash(content)
                        && content.equals(targetMd5CampaignId));
    }

    @VisibleForTesting
    boolean isMd5Hash(String utmContent) {
        if (isNullOrEmpty(utmContent) || utmContent.length() != MD5_LENGTH) {
            return false;
        }
        return utmContent.matches(REGEX_MD5);
    }

    @VisibleForTesting
    String hashMd5By(long campaignId) {
        return md5().hashString(String.valueOf(campaignId), UTF_8).toString();
    }

    @VisibleForTesting
    LocalDate getCurrentLocalDate() {
        return LocalDate.now(getZoneId());
    }

    @VisibleForTesting
    SecretsManagerClient getSecretsManagerClient() {
        return SecretsManagerClient.getInstance();
    }

    @VisibleForTesting
    void sleep(Duration duration)
            throws InterruptedException {
        long mills = (getSleepSecond() * 1000) - duration.toMillis();
        if (mills > 0) {
            Thread.sleep(mills);
        }
    }

    @VisibleForTesting
    String getResponse(String appId, String secret) throws Exception {
        ZoneId zoneId = getZoneId();
        long purchaseTimeStart = getScanTimeFrom().atZone(zoneId).toEpochSecond();
        long purchaseTimeEnd = getScanTimeTo().atZone(zoneId).toEpochSecond();
        long now = getEpochSecondNow();
        String raw = getRaw(purchaseTimeStart, purchaseTimeEnd);
        String signature = hash256(appId + now + raw + secret);
        try (CloseableHttpClient client = createHttpClient()) {
            HttpPost request = createHttpPost();
            request.setHeader(AUTHORIZATION, "SHA256 Credential=" + appId + ",Timestamp="
                    + now + ",Signature=" + signature);
            request.setHeader(CONTENT_TYPE, "application/json");
            request.setEntity(createEntityBy(raw));
            HttpResponse response = client.execute(request);
            return EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (Exception e) {
            log.error("Http Request Exception.", e);
            return null;
        }
    }

    @VisibleForTesting
    StringEntity createEntityBy(String raw) throws UnsupportedEncodingException {
        return new StringEntity(raw);
    }

    @VisibleForTesting
    ZonedDateTime getZonedDateTimeNow() {
        return ZonedDateTime.now();
    }

    @VisibleForTesting
    long getEpochSecondNow() {
        return ZonedDateTime.now().toEpochSecond();
    }

    @VisibleForTesting
    String hash256(String data) {
        return Hashing.sha256().hashString(data, StandardCharsets.UTF_8).toString();
    }

    @VisibleForTesting
    HttpPost createHttpPost() {
        return new HttpPost(getApiUrl());
    }

    @VisibleForTesting
    CloseableHttpClient createHttpClient() {
        return HttpClientBuilder.create().build();
    }

    @VisibleForTesting
    String getRaw(long purchaseTimeStart, long purchaseTimeEnd) {
        String scrollIdParam = isNullOrEmpty(getScrollId()) ? EMPTY
                : ", scrollId:\\\"" + getScrollId() + "\\\"";
        return "{\"query\":\"{conversionReport(limit:500, purchaseTimeStart:"
                + purchaseTimeStart + ", purchaseTimeEnd:" + purchaseTimeEnd
                + scrollIdParam
                + ", buyerType:ALL){nodes {checkoutId grossCommission purchaseTime "
                + "cappedCommission totalBrandCommission estimatedTotalCommission "
                + "utmContent referrer buyerType device orders{orderId shopType items {"
                + "itemId shopId qty attributionType itemPrice actualAmount "
                + "itemCommission grossBrandCommission itemSellerCommission modelId "
                + "globalCategoryLv1Name globalCategoryLv2Name globalCategoryLv3Name}}}, "
                + "pageInfo{limit hasNextPage scrollId}  }} \"}";
    }

    @VisibleForTesting
    ZoneId getZoneId() {
        return ZoneId.of(ZONE_IDS.get(getTargetCountryCode()));
    }

    @VisibleForTesting
    boolean isDuplicateConversion(String key) {
        return merchantIntegrationKeyCache.getUnchecked(key) > 0;
    }

    @VisibleForTesting
    void insertDataProceeded(
            MerchantIntegrationHistoryInsertRequest insertRequest) {
        try {
            merchantIntegrationHistoryMapper.insert(insertRequest);
        } catch (Exception ex) {
            getLogger().error("Fail to insert data - {}", insertRequest.getKey());
        }
    }

    @VisibleForTesting
    MerchantIntegrationHistoryInsertRequest createConversionProceeded(
            ZonedDateTime conversionTime, List<ConversionRegistrationDetails> details,
            String key) {
        Gson gson = new Gson();
        String data = gson.toJson(details);
        return new MerchantIntegrationHistoryInsertRequest(key, getCampaignId(),
                conversionTime.toLocalDateTime(), getMerchant().name(), data);
    }

    @VisibleForTesting
    String getSkuFrom(ShopeeItem item) {
        return on(UNDERSCORE).join(item.getItemId(), item.getModelId());
    }

    @VisibleForTesting
    BigDecimal calculateProductUnitPrice(BigDecimal productUnitPrice,
            ZonedDateTime conversionTime) {
        YearMonth conversionMonth = YearMonth.from(conversionTime);
        CurrencyRate currencyRate = getCurrencyRateFrom(getReceivedCurrency(),
                getCampaignId(), conversionMonth);
        return productUnitPrice.multiply(currencyRate.getRate());
    }

    @VisibleForTesting
    CurrencyRate getCurrencyRateFrom(String currency, long campaignId,
            YearMonth conversionMonth) {
        try {
            if (!EMPTY.equals(currency)) {
                return currencyCache.get(createCustomKey(currency, campaignId,
                        conversionMonth));
            } else {
                return campaignCurrencyCache.get(createCurrencyKeyBy(campaignId,
                        conversionMonth));
            }
        } catch (Exception ex) {
            if (isShouldSendNotificationToSlack()) {
                slackService.send(ERROR_MESSAGE_FORMAT, conversionMonth.toString(),
                        String.valueOf(getCampaignId()), getReceivedCurrency());
            }
            throw new TaekkyeonException(
                    String.format(ERROR_MESSAGE_FORMAT, conversionMonth,
                            getCampaignId(), getReceivedCurrency()));
        }
    }

    @VisibleForTesting
    String getCheckoutOrConversionId(ShopeeNode shopeeNode) {
        return !isNullOrEmpty(shopeeNode.getCheckoutId()) ? shopeeNode.getCheckoutId()
                : shopeeNode.getConversionId();
    }

    @VisibleForTesting
    boolean validateDateTimeAfter(ZonedDateTime conversionTime) {
        ZonedDateTime afterDateTime = of(2024, 7, 31, 23, 59, 59, 0, getZoneId());
        return conversionTime.isAfter(afterDateTime);
    }

    @VisibleForTesting
    boolean validateDateTimeAfter(ZonedDateTime conversionTime,
            ZonedDateTime afterDateTime) {
        return conversionTime.isAfter(afterDateTime);
    }

    @VisibleForTesting
    String getNewCustomerTypeSuffix(String customerType, String attributionType) {
        if (ORDERED_IN_SAME_SHOP.equals(attributionType)) {
            return String.format(DIRECT_FORMAT, customerType);
        }
        return String.format(IN_DIRECT_FORMAT, customerType);
    }

    @VisibleForTesting
    List<ConversionRegistrationDetails> processConversionByBrandItemRules(
            ZonedDateTime conversionTime, String identifier, int resultId,
            String customerType, String categoryId, String productId,
            BigDecimal actualAmount, String clickId, String shopId, boolean isBrandItem) {

        List<ConversionRegistrationDetails> details = new ArrayList<>();
        CampaignAdvType campaignType = findCampaignType(getCampaignId());

        ConversionRegistrationDetails mainConversion = createMainConversion(
                conversionTime, identifier, resultId, customerType, categoryId, productId,
                actualAmount, clickId);

        if (isBrandItem) {
            details.addAll(
                    createBrandItemConversions(mainConversion, campaignType, shopId));
        } else {
            details.addAll(createNonBrandConversions(mainConversion, campaignType,
                    conversionTime, identifier, resultId, customerType, categoryId,
                    productId, actualAmount, clickId));
        }

        return details;
    }

    @VisibleForTesting
    Long getBrandSubCampaignIdFrom(CampaignAdvType campaignAdvType, String shopId) {
        long campaignId = 0L;
        if (campaignAdvType == BRAND) {
            Long parentId = getAdvParentCampaignId(getCampaignId());
            campaignId = parentId != null ? parentId : DEFAULT_CAMPAIGN_ID;
        } else {
            Long brandId = findBrandCampaignByShopId(shopId, getCampaignId());
            campaignId = brandId != null ? brandId : DEFAULT_CAMPAIGN_ID;
        }
        return campaignId;
    }

    @VisibleForTesting List<ConversionRegistrationDetails> createBrandItemConversions(
            ConversionRegistrationDetails mainConversion, CampaignAdvType campaignType,
            String shopId) {
        if (isCrossBrandSubConversions(campaignType, shopId)) {
            return createCrossBrandSubConversions(mainConversion, shopId);
        } else {
            return createBrandSubConversions(mainConversion, campaignType, shopId);
        }
    }

    @VisibleForTesting
    List<ConversionRegistrationDetails> createBrandSubConversions(
            ConversionRegistrationDetails mainConversion, CampaignAdvType campaignType,
            String shopId) {
        List<ConversionRegistrationDetails> details = new ArrayList<>();

        details.add(mainConversion);

        Long subCampaignId = getBrandSubCampaignIdFrom(campaignType, shopId);
        if (subCampaignId != null && subCampaignId > 0) {
            details.add(createSubCampaignConversion(mainConversion, subCampaignId));
        }

        return details;
    }

    @VisibleForTesting
    List<ConversionRegistrationDetails> createCrossBrandSubConversions(
            ConversionRegistrationDetails mainConversion,
            String shopId) {

        List<ConversionRegistrationDetails> details = new ArrayList<>();

        Long targetBrandCampaignId = findCampaignBy(shopId);
        if (targetBrandCampaignId != null && targetBrandCampaignId > 0) {
            details.add(createSubCampaignConversion(mainConversion, targetBrandCampaignId));

            Long kolCampaignId = getAdvParentCampaignId(targetBrandCampaignId);
            if (kolCampaignId != null && kolCampaignId > 0) {
                details.add(createSubCampaignConversion(mainConversion, kolCampaignId));
            }
        }

        return details;
    }

    @VisibleForTesting
    boolean isCrossBrandSubConversions(CampaignAdvType campaignType, String shopId) {
        if (campaignType != BRAND) {
            return false;
        }

        Long shopCampaignId = findCampaignBy(shopId);
        if (shopCampaignId == null || shopCampaignId <= 0) {
            return false;
        }

        return !shopCampaignId.equals(getCampaignId());
    }

    @VisibleForTesting
    List<ConversionRegistrationDetails> createNonBrandConversions(
            ConversionRegistrationDetails mainConversion, CampaignAdvType campaignType,
            ZonedDateTime conversionTime, String identifier, int resultId,
            String customerType, String categoryId, String productId,
            BigDecimal actualAmount, String clickId) {

        List<ConversionRegistrationDetails> details = new ArrayList<>();

        if (campaignType == BRAND) {
            Long subCampaignId = getAdvParentCampaignId(getCampaignId());
            if (subCampaignId != null && subCampaignId > 0) {
                details.add(new ClickConversionRegistrationDetails(conversionTime,
                        identifier, resultId, customerType, categoryId, productId,
                        actualAmount, clickId, subCampaignId));
            }
        } else {
            details.add(mainConversion);
        }

        return details;
    }

    private BigDecimal getGrossBrandCommission(ShopeeItem shopeeItem) {
        BigDecimal itemSellerCommission = shopeeItem.getItemSellerCommission();
        return isValidCommission(itemSellerCommission)
                ? itemSellerCommission : shopeeItem.getGrossBrandCommission();
    }

    private BigDecimal calculateExtraCommission(
            BigDecimal grossBrandCommission, ZonedDateTime conversionTime) {
        return isProductUnitPrice() ? calculateProductUnitPrice(grossBrandCommission,
                conversionTime) : grossBrandCommission;
    }

    private List<ClickConversionRegistrationDetails> extractSubConversions(
            List<ConversionRegistrationDetails> brandItemConversions) {
        return brandItemConversions.stream()
                .filter(conversion ->
                        conversion instanceof ClickConversionRegistrationDetails)
                .map(conversion ->
                        (ClickConversionRegistrationDetails) conversion)
                .collect(toList());
    }

    private ConversionRegistrationDetails createMainConversion(
            ZonedDateTime conversionTime, String identifier, int resultId,
            String customerType, String categoryId, String productId,
            BigDecimal actualAmount, String clickId) {

        return new ConversionRegistrationDetails(conversionTime, identifier, resultId,
                customerType, categoryId, productId, actualAmount, clickId);
    }

    private ClickConversionRegistrationDetails createSubCampaignConversion(
            ConversionRegistrationDetails mainConversion, Long subCampaignId) {

        return new ClickConversionRegistrationDetails(mainConversion.getConversionTime(),
                mainConversion.getTransactionId(), mainConversion.getResultId(),
                mainConversion.getCustomerType(), mainConversion.getProductCategoryId(),
                mainConversion.getProductId(), mainConversion.getProductUnitPrice(),
                mainConversion.getClickId(), subCampaignId);
    }

    private String createCurrencyKeyBy(long campaignId, YearMonth conversionMonth) {
        return createCustomKey(campaignId, conversionMonth);
    }

    private String createCustomKey(Object... objects) {
        return Joiner.on(PIPE).join(objects);
    }

    private Pair<String, String> getShopeeApiKey() {
        String secretValue = getSecretsManagerClient().getSecretValue(getSecretName());
        for (Entry<String, JsonElement> entry : getJsonParser().parse(secretValue)
                .getAsJsonObject().entrySet()) {
            String appId = entry.getKey();
            String secret = entry.getValue().getAsString();
            return Pair.of(appId, secret);
        }
        return null;
    }

    private String findCampaignName() {
        if (isNullOrEmpty(getCampaignName())) {
            campaignName = campaignMapper.findCampaignNameBy(getCampaignId());
        }
        return getCampaignName();
    }

    private CurrencyRate findCampaignCurrencyRateBy(String key) {
        List<String> splits = Splitter.on(PIPE).splitToList(key);
        long campaignId = Long.parseLong(splits.get(0));
        YearMonth conversionMonth = YearMonth.parse(splits.get(1));
        return currencyMapper.findCampaignCurrencyRate(campaignId, conversionMonth);
    }

    private CurrencyRate findCurrencyRateBy(String key) {
        List<String> splits = Splitter.on(PIPE).splitToList(key);
        String currency = splits.get(0);
        long campaignId = Long.parseLong(splits.get(1));
        YearMonth conversionMonth = YearMonth.parse(splits.get(2));
        return currencyMapper.findCurrencyRate(currency, campaignId, conversionMonth);
    }

    private boolean isProductUnitPrice() {
        return PHILIPPINES.getCode().equals(getTargetCountryCode())
                || TAIWAN.getCode().equals(getTargetCountryCode());
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }
}
