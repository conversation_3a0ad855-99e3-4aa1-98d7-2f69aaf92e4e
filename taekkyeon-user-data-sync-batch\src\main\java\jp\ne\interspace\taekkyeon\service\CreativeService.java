/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import javax.inject.Singleton;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.common.CreativeHelper;
import jp.ne.interspace.taekkyeon.common.StringHelper;
import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.model.Creative;
import jp.ne.interspace.taekkyeon.model.CreativeDetails;
import jp.ne.interspace.taekkyeon.model.CustomCreativeTrackingUrlRule;
import jp.ne.interspace.taekkyeon.model.SubId;
import jp.ne.interspace.taekkyeon.module.Country;
import jp.ne.interspace.taekkyeon.persist.aws.dynamodb.DynamoDbTable;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CreativeDataMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PublisherSiteMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.MerchantAccountMapper;

import static io.netty.util.internal.StringUtil.isNullOrEmpty;
import static java.lang.String.format;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Optional.ofNullable;
import static java.util.concurrent.TimeUnit.MINUTES;
import static java.util.stream.Collectors.toList;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.model.CreativeType.valueOf;
import static jp.ne.interspace.taekkyeon.module.Country.getCountryBy;
import static jp.ne.interspace.taekkyeon.module.UserDataSyncModule.BIND_KEY_BASE_SHORT_AFFILIATE_DOMAIN;
import static jp.ne.interspace.taekkyeon.module.UserDataSyncModule.BIND_KEY_BASE_TRACKING_URL;
import static lombok.AccessLevel.PACKAGE;

/**
 * Service for creative.
 *
 * <AUTHOR> Huynh
 */
@Singleton @Slf4j
public class CreativeService {

    private static final String PRODUCT_FEED_LINK = "PRODUCT_FEED_LINK";
    private static final String AFFILIATE_LINK_FORMAT = "%s/%s";
    private static final String GO_PATH = "/go/";
    private static final int NUMBER_OF_BYTES_TRUNCATING_TRACKING_URL = 2048;
    private static final String TRUNCATED_ORIGINAL_COLUMN_NAME = "TRUNCATED_ORIGINAL_URL";
    private static final String VALUE_EXPRESSION = ":truncatedOriginalUrl";
    private static final String INDEX_NAME = "TRUNCATED_ORIGINAL_URL-index";
    private static final String PATH = "PATH";
    public static final String QUESTION_MARK = "?";
    public static final String AMPERSAND = "&";
    public static final String EQUALITY_SIGN = "=";
    private static final String DIGIT_REGEX = "\\d";
    private static final String NON_DIGIT_REGEX = "\\D";
    private static final Long NOT_EXISTING_SITE_ID = 0L;
    private static final String TRACKING_URL_PATH = "/adv.php";
    private static final String BASE_RK_PARAMETER = "rk=xxxxxxxxxxxx";
    private static final String COUNTRY_NOT_FOUND_MESSAGE = "The country was not found for campaign: %s.";
    private static final String BANNER_ID_NOT_FOUND_MESSAGE = "The banner %s is not found.";

    private LoadingCache<Long, String> campaignCountryCodeCache = CacheBuilder.newBuilder()
            .maximumSize(5000).expireAfterAccess(30, MINUTES)
            .build(new CacheLoader<Long, String>() {

                @Override
                public String load(Long key) {
                    return merchantAccountMapper.findCountryCodeBy(key);
                }
            });

    private LoadingCache<Long, CreativeDetails> creativeDetailsCache = CacheBuilder.newBuilder()
            .maximumSize(5000).expireAfterAccess(30, MINUTES)
            .build(new CacheLoader<Long, CreativeDetails>() {

                @Override
                public CreativeDetails load(Long key) {
                    return creativeMapper.findCreativeDetailsBy(key);
                }
            });

    @Inject
    private CreativeDataMapper creativeMapper;

    @Inject
    private PublisherSiteMapper publisherSiteMapper;

    @Inject
    private MerchantAccountMapper merchantAccountMapper;

    @Inject
    private CreativeHelper creativeHelper;

    @Inject
    private StringHelper stringHelper;

    @Inject @Named(BIND_KEY_BASE_SHORT_AFFILIATE_DOMAIN) @Getter(PACKAGE)
    private ImmutableMap<Country, String> baseShortAffiliateUrl;

    @Inject @Named(BIND_KEY_BASE_TRACKING_URL) @Getter(PACKAGE)
    private ImmutableMap<String, String> baseTrackingUrl;

    @Inject
    private DynamoDbTable dynamoDbTable;

    /**
     * Returns the list of {@link Creative} after generating an affiliate link.
     *
     * @param creatives the given list of {@link Creative} data
     * @return the list of {@link Creative} after generating an affiliate link
     */
    public List<Creative> processCreatives(List<Creative> creatives) {
        return creatives.stream().filter(creative -> {
            try {
                String affiliateLink = EMPTY;
                switch (valueOf(creative.getCreativeType())) {
                    case QUICK_LINK:
                        CreativeDetails creativeDetails = getCreativeDetailsBy(
                                creative.getId());
                        long siteId = creativeDetails.getSiteId();

                        String countryCode = getCountryCodeBy(creativeDetails
                                .getCampaignId());
                        String baseUrl = getBaseShortAffiliateUrl().get(
                                getCountryBy(countryCode));
                        affiliateLink = createAffiliateLinkForQuickLink(
                                creative.getId(), siteId, baseUrl);
                        break;
                    case PRODUCT_FEED:
                        affiliateLink = PRODUCT_FEED_LINK;
                        break;
                    case CUSTOM:
                        affiliateLink = getAffiliateLink(creative.getId(),
                                creative.getShortenLinkCode());
                        break;
                    default:
                        affiliateLink = getAffiliateLink(creative.getId(), EMPTY);
                        break;
                }
                creative.setAffiliateLink(affiliateLink);
                return true;
            } catch (TaekkyeonException | UnsupportedEncodingException
                     | ExecutionException ex ) {
                getLogger().error("error during process creatives - {}", ex.getMessage());
                return false;
            }
        }).collect(toList());
    }

    /**
     * Returns the affiliate link for quick link.
     *
     * @param creativeId the ID of the given creative
     * @param siteId the ID of the given site
     * @param baseUrl the base URL to be used
     * @return the affiliate link for quick link
     */
    public String createAffiliateLinkForQuickLink(long creativeId, long siteId, String baseUrl) {
        return format(AFFILIATE_LINK_FORMAT, baseUrl,
                creativeHelper.createRk(creativeId, siteId));
    }

    /**
     * Returns the affiliate link.
     *
     * @param creativeId
     *          the ID of the given creative
     * @param shortenLinkCode
     *          the shorten link code to be used
     * @return the affiliate link
     * @throws UnsupportedEncodingException occurred error
     * @throws ExecutionException occurred error
     */
    public String getAffiliateLink(long creativeId, String shortenLinkCode)
            throws UnsupportedEncodingException, ExecutionException {
        CreativeDetails creativeDetails = getCreativeDetailsBy(creativeId);
        String countryCode = getCountryCodeBy(creativeDetails.getCampaignId());
        String baseUrl = getBaseShortAffiliateUrl().get(getCountryBy(countryCode));

        String finalShortenLinkCode = shortenLinkCode;
        if (isNullOrEmpty(finalShortenLinkCode)) {
            finalShortenLinkCode = getShortenLinkCode(creativeId, creativeDetails,
                    countryCode);
            if (isNullOrEmpty(finalShortenLinkCode)) {
                finalShortenLinkCode = getShortenLinkCodeByUrl(
                        creativeDetails.getLandingUrl());
            }
        }

        if (!isNullOrEmpty(finalShortenLinkCode)) {
            return baseUrl + GO_PATH + finalShortenLinkCode;
        }
        String rk = creativeHelper.createRk(creativeId, creativeDetails.getSiteId());
        return String.format("%s/%s", baseUrl, rk);
    }

    /**
     * Returns the shorten link code.
     *
     * @param creativeId the ID of the given creative
     * @param creativeDetails given the {@link CreativeDetails}
     * @param countryCode given country code
     * @return the shorten link code
     * @throws UnsupportedEncodingException occurred error
     */
    public String getShortenLinkCode(long creativeId, CreativeDetails creativeDetails,
            String countryCode) throws UnsupportedEncodingException {
        String originalUrl = getOriginalUrl(creativeId, creativeDetails, countryCode);
        return getShortenLinkCodeByUrl(originalUrl);
    }

    private String getShortenLinkCodeByUrl(String originalUrl) {
        String truncatedOriginalUrl = stringHelper.truncateToBytes(originalUrl,
                NUMBER_OF_BYTES_TRUNCATING_TRACKING_URL);
        Pair<String, AttributeValue> partitionKey = ImmutablePair.of(
                TRUNCATED_ORIGINAL_COLUMN_NAME, new AttributeValue(truncatedOriginalUrl));
        Map<String, AttributeValue> shorterUrlValues = dynamoDbTable.query(
                VALUE_EXPRESSION, partitionKey, INDEX_NAME).orElse(ImmutableMap.of());
        return !shorterUrlValues.isEmpty() ? shorterUrlValues.get(PATH).getS() : EMPTY;
    }

    /**
     * Returns the original url.
     *
     * @param creativeId the ID of the given creative
     * @param creativeDetails given the {@link CreativeDetails}
     * @param countryCode given country code
     * @return the original url.
     * @throws UnsupportedEncodingException occurred error
     */
    public String getOriginalUrl(long creativeId, CreativeDetails creativeDetails,
            String countryCode) throws UnsupportedEncodingException {
        String trackingUrl = generateTrackingUrlFor(creativeDetails.getSiteId(),
                creativeId, countryCode);
        CustomCreativeTrackingUrlRule trackingUrlRule = creativeMapper.findTrackingUrlRuleBy(
                creativeDetails.getCampaignId());
        String landingUrlQueryParameter = getCustomCreativeLandingUrlWithQueryParameter(
                creativeDetails.getLandingUrl(), trackingUrlRule);
        String encodedLandingUrlQueryParameter = getEncodedLandingUrlWithQueryParameter(
                landingUrlQueryParameter, trackingUrlRule);
        return Joiner.on(AMPERSAND).join(trackingUrl, encodedLandingUrlQueryParameter);
    }

    /**
     * Returns the tracking url.
     * @param siteId the ID of the given site
     * @param creativeId the ID of the given creative
     * @param countryCode given country code
     * @return the tracking url
     */
    public String generateTrackingUrlFor(long siteId, long creativeId,
            String countryCode) {
        List<SubId> subIds = creativeMapper.findSubIdBy(siteId, creativeId);
        String subIdQueryString = generateQueryStringFrom(subIds);
        String baseTrackingUrl = generateBaseTrackingUrlFor(siteId, creativeId,
                countryCode);
        String baseTrackingUrlWithRkCode = baseTrackingUrl.replaceAll("xxxxxxxxxxxx",
                creativeHelper.createRk(creativeId, siteId));
        return Strings.isNullOrEmpty(subIdQueryString)
                ? baseTrackingUrlWithRkCode
                : Joiner.on(AMPERSAND).join(baseTrackingUrlWithRkCode, subIdQueryString);
    }

    /**
     * Returns the custom creative landing url.
     * @param landingUrl given landing url
     * @param trackingUrlRule given the {@link CustomCreativeTrackingUrlRule}
     * @return the custom creative landing url
     */
    public String getCustomCreativeLandingUrlWithQueryParameter(String landingUrl,
            CustomCreativeTrackingUrlRule trackingUrlRule) {
        String parameters = trackingUrlRule.getParameters();
        if (!parameters.isEmpty()) {
            if (parameters.startsWith(QUESTION_MARK)) {
                parameters = parameters.substring(1);
            }
            String parameterPrefix = landingUrl.contains(QUESTION_MARK)
                    ? AMPERSAND : QUESTION_MARK;
            parameters = parameterPrefix + parameters;
        }
        return landingUrl + parameters;
    }

    /**
     * Returns the encoded landing url with query parameter.
     *
     * @param landingUrlWithQueryParameter given landing url with query parameter
     * @param trackingUrlRule given the {@link CustomCreativeTrackingUrlRule}
     * @return the encoded landing url with query parameter
     * @throws UnsupportedEncodingException occurred error
     */
    public String getEncodedLandingUrlWithQueryParameter(
            String landingUrlWithQueryParameter,
            CustomCreativeTrackingUrlRule trackingUrlRule)
            throws UnsupportedEncodingException {
        String encodedLandingUrl = landingUrlWithQueryParameter;
        for (int i = 0; i < trackingUrlRule.getUrlEncodeTimes() - 1; i++) {
            encodedLandingUrl = URLEncoder.encode(encodedLandingUrl, UTF_8.name());
        }
        return format("url=%s",
                URLEncoder.encode(trackingUrlRule.getPrefix() + encodedLandingUrl,
                        UTF_8.name()));
    }

    /**
     * Returns the base tracking url.
     *
     * @param siteId the ID of the given site
     * @param creativeId the ID of the given creative
     * @param countryCode given country code
     * @return the base tracking url
     */
    public String generateBaseTrackingUrlFor(long siteId, long creativeId,
            String countryCode) {
        String baseTrackingUrl = createBaseTrackingUrl(
                getBaseTrackingUrl().get(countryCode));
        Long externalSiteId = ofNullable(
                creativeMapper.findExternalSiteIdBy(siteId, creativeId)).orElse(
                NOT_EXISTING_SITE_ID);
        if (!externalSiteId.equals(NOT_EXISTING_SITE_ID)) {
            baseTrackingUrl = Joiner.on(AMPERSAND)
                    .join(baseTrackingUrl, format("esid=%d", externalSiteId));
        }
        return baseTrackingUrl;
    }

    public String createBaseTrackingUrl(String trackingDomains) {
        return Joiner.on(EMPTY).join(trackingDomains, TRACKING_URL_PATH, QUESTION_MARK,
                BASE_RK_PARAMETER);
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }

    @VisibleForTesting
    String getCountryCodeBy(long campaignId) {
        try {
            return campaignCountryCodeCache.get(campaignId);
        } catch (ExecutionException e) {
            throw new TaekkyeonException(format(COUNTRY_NOT_FOUND_MESSAGE, campaignId));
        }
    }

    @VisibleForTesting
    CreativeDetails getCreativeDetailsBy(long creativeId) {
        try {
            return creativeDetailsCache.get(creativeId);
        } catch (ExecutionException e) {
            throw new TaekkyeonException(format(BANNER_ID_NOT_FOUND_MESSAGE, creativeId));
        }
    }

    private String generateQueryStringFrom(List<SubId> subIds) {
        return subIds.stream().sorted((subId1, subId2) -> {
            int compareString = extractCharacters(subId1).compareTo(
                    extractCharacters(subId2));
            return compareString == 0 ? extractInt(subId1) - extractInt(subId2)
                    : compareString;
        }).map((subId) -> Joiner.on(EQUALITY_SIGN).useForNull(EMPTY)
                        .join(subId.getName(), subId.getValue()))
                .collect(Collectors.joining(AMPERSAND));
    }

    private String extractCharacters(SubId sudId) {
        return sudId.getName().replaceAll(DIGIT_REGEX, EMPTY);
    }

    private int extractInt(SubId sudId) {
        String number = sudId.getName().replaceAll(NON_DIGIT_REGEX, EMPTY);
        return number.isEmpty() ? 0 : Integer.parseInt(number);
    }
}
