/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.inject.Inject;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.common.StringHelper;
import jp.ne.interspace.taekkyeon.module.FileOutputDirectoryResolver;

import static java.nio.file.StandardOpenOption.APPEND;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.CSV_FILE_EXTENSION;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.HYPHEN;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.SLASH;
import static lombok.AccessLevel.PACKAGE;

/**
 * Service layer for handling all file related operations.
 *
 * <AUTHOR> Van Nguyen
 */
@Slf4j
public class FileService {

    private static final String REPORT = "report";
    private static final int RANDOM_STRING_LENGTH = 16;

    @Inject
    private StringHelper stringHelper;

    @Inject @FileOutputDirectoryResolver @VisibleForTesting @Getter(value = PACKAGE)
    private String fileOutputDirectory;

    /**
     * Returns the created file name.
     * @param reportType
     *          type of report
     * @param countryCode
     *          given country code
     * @param creationDate
     *          string date of report creation
     * @return the created file name
     */
    public String createFileName(String reportType, String countryCode,
            String creationDate) {
        return Joiner.on(EMPTY)
                .join(Joiner.on(HYPHEN).join(reportType.toLowerCase(), REPORT,
                        countryCode.toLowerCase(), creationDate,
                        stringHelper
                                .generateRandomAlphanumericString(RANDOM_STRING_LENGTH)),
                        CSV_FILE_EXTENSION);
    }

    /**
     * write the data to file.
     * @param filename
     *          given file name
     * @param data
     *          content to write on file
     * @throws java.io.IOException
     *          if an I/O error occurs
     */
    public void writeInFile(String filename, List<String> data) throws IOException {
        Path path = getFilePath(filename);
        if (!isFileExist(path)) {
            createFile(path);
        }
        write(path, data);
    }

    /**
     * Returns the file.
     * @param filename
     *          given file name
     * @return the file
     * @throws java.io.IOException
     *          if an I/O error occurs
     */
    public File getFile(String filename) throws IOException {
        return getFilePath(filename).toFile();
    }

    /**
     * delete the given file.
     * @param filename
     *         given file name
     * @throws java.io.IOException
     *         if an I/O error occurs
     */
    public void deleteFile(String filename) throws IOException {
        Files.delete(getFilePath(filename));
    }

    /**
     * delete files with the name match the given fileNameKeyword.
     *   @param fileNameKeyword
     *          given file name keyword
     */
    public void deleteMatchingFilesInFolder(String fileNameKeyword) {
        File folder = new File(getFileOutputDirectory());
        if (folder.exists() && folder.isDirectory()) {
            File[] files = folder.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile() && file.getName().contains(fileNameKeyword)) {
                        if (file.delete()) {
                            getLogger().info("Deleted file: {}", file.getName());
                        } else {
                            getLogger().info("Can not delete file: {}", file.getName());
                        }
                    }
                }
            }
        } else {
            getLogger().info("Folder does not exist or is not a directory: {}",
                    folder.getAbsolutePath());
        }
    }

    @VisibleForTesting
    boolean isFileExist(Path path) {
        return Files.exists(path);
    }

    @VisibleForTesting
    void createFile(Path path) throws IOException {
        Files.createFile(path);
    }

    @VisibleForTesting
    void write(Path path, List<String> data) throws IOException {
        try (BufferedWriter writer = Files.newBufferedWriter(path, APPEND)) {
            for (String line : data) {
                writer.write(line);
                writer.newLine();
            }
            writer.flush();
        }
    }

    @VisibleForTesting
    void writeOneFile(String filename, List<String> data) throws IOException {
        Path path = getFilePath(filename);
        if (!isFileExist(path)) {
            createFile(path);
        }
        try (BufferedWriter writer = Files.newBufferedWriter(path)) {
            for (String line : data) {
                writer.write(line);
                writer.newLine();
            }
            writer.flush();
        }
    }

    @VisibleForTesting
    Path getFilePath(String filename) {
        return Paths.get(getFileOutputDirectory() + SLASH + filename);
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }
}
