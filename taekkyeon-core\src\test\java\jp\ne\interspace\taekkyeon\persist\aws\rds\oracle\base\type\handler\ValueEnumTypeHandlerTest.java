/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.base.type.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;

import lombok.AllArgsConstructor;
import lombok.Getter;

import org.apache.ibatis.type.JdbcType;
import org.junit.Test;

import jp.ne.interspace.taekkyeon.model.ValueEnum;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ValueEnumTypeHandler}.
 *
 * <AUTHOR> Varga
 */
public class ValueEnumTypeHandlerTest {

    private ValueEnumTypeHandler<TestEnum> underTest = new TestValueEnumTypeHandler();

    @Test
    public void testSetParameterShouldSetRawValueIntoPreparedStatementParameterWhenParameterIsNotNull()
            throws SQLException {
        // given
        PreparedStatement preparedStatement = mock(PreparedStatement.class);
        int index = 42;
        TestEnum parameter = TestEnum.TEST_ITEM_1;
        JdbcType jdbcType = JdbcType.INTEGER;

        int expectedSetValue = 1;

        // when
        underTest.setParameter(preparedStatement, index, parameter, jdbcType);

        // then
        verify(preparedStatement).setInt(index, expectedSetValue);
    }

    @Test
    public void testSetParameterShouldSetNullIntoPreparedStatementParameterWhenParameterIsNull()
            throws SQLException {
        // given
        PreparedStatement preparedStatement = mock(PreparedStatement.class);
        int index = 42;
        TestEnum parameter = null;
        JdbcType jdbcType = JdbcType.INTEGER;

        // when
        underTest.setParameter(preparedStatement, index, parameter, jdbcType);

        // then
        verify(preparedStatement).setNull(index, Types.INTEGER);
    }

    @Test
    public void testGetResultFromResultSetWithColumnNameShouldReturnTransformedRawValueWhenEnumElementExistsForRawValue()
            throws SQLException {
        // given
        ResultSet resultSet = mock(ResultSet.class);
        String columnName = "columnName";

        int rawValue = 1;
        when(resultSet.getInt(columnName)).thenReturn(rawValue);

        TestEnum expected = TestEnum.TEST_ITEM_1;

        // when
        TestEnum actual = underTest.getResult(resultSet, columnName);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetResultFromResultSetWithColumnNameShouldReturnDefaultElementWhenEnumElementDoesNotExistForRawValue()
            throws SQLException {
        // given
        ResultSet resultSet = mock(ResultSet.class);
        String columnName = "columnName";

        int rawValue = 10;
        when(resultSet.getInt(columnName)).thenReturn(rawValue);

        TestEnum expected = TestEnum.TEST_DEFAULT_ITEM;

        // when
        TestEnum actual = underTest.getResult(resultSet, columnName);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetResultFromResultSetWithColumnIndexShouldReturnTransformedRawValueWhenEnumElementExistsForRawValue()
            throws SQLException {
        // given
        ResultSet resultSet = mock(ResultSet.class);
        int columnIndex = 84;

        int rawValue = 1;
        when(resultSet.getInt(columnIndex)).thenReturn(rawValue);

        TestEnum expected = TestEnum.TEST_ITEM_1;

        // when
        TestEnum actual = underTest.getResult(resultSet, columnIndex);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetResultFromResultSetWithColumnIndexShouldReturnDefaultElementWhenEnumElementDoesNotExistForRawValue()
            throws SQLException {
        // given
        ResultSet resultSet = mock(ResultSet.class);
        int columnIndex = 84;

        int rawValue = 10;
        when(resultSet.getInt(columnIndex)).thenReturn(rawValue);

        TestEnum expected = TestEnum.TEST_DEFAULT_ITEM;

        // when
        TestEnum actual = underTest.getResult(resultSet, columnIndex);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetResultFromCallableStatementShouldReturnTransformedRawValueWhenEnumElementExistsForRawValue()
            throws SQLException {
        // given
        CallableStatement callableStatement = mock(CallableStatement.class);
        int columnIndex = 84;

        int rawValue = 1;
        when(callableStatement.getInt(columnIndex)).thenReturn(rawValue);

        TestEnum expected = TestEnum.TEST_ITEM_1;

        // when
        TestEnum actual = underTest.getResult(callableStatement, columnIndex);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetResultFromCallableStatementShouldReturnDefaultElementWhenEnumElementDoesNotExistForRawValue()
            throws SQLException {
        // given
        CallableStatement callableStatement = mock(CallableStatement.class);
        int columnIndex = 84;

        int rawValue = 10;
        when(callableStatement.getInt(columnIndex)).thenReturn(rawValue);

        TestEnum expected = TestEnum.TEST_DEFAULT_ITEM;

        // when
        TestEnum actual = underTest.getResult(callableStatement, columnIndex);

        // then
        assertEquals(expected, actual);
    }

    /**
     * Simple implementation for {@link ValueEnumTypeHandler} filling runtime data with
     * test data.
     *
     * <AUTHOR> Varga
     */
    private static class TestValueEnumTypeHandler extends ValueEnumTypeHandler<TestEnum> {

        private TestValueEnumTypeHandler() {
            super(TestEnum.class, TestEnum.TEST_DEFAULT_ITEM);
        }
    }

    /**
     * {@link ValueEnum} implementation for the unit tests.
     *
     * <AUTHOR> Varga
     */
    @Getter
    @AllArgsConstructor
    private enum TestEnum implements ValueEnum {
        TEST_DEFAULT_ITEM(0), TEST_ITEM_1(1);

        private int value;
    }
}
