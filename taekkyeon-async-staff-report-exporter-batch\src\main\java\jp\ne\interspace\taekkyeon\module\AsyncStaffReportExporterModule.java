/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.util.Map;

import javax.inject.Singleton;

import com.google.common.collect.ImmutableMap;
import com.google.inject.AbstractModule;
import com.google.inject.Injector;
import com.google.inject.Provides;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Named;

import org.easybatch.core.processor.RecordProcessor;
import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;

import jp.ne.interspace.taekkyeon.batch.processor.AsyncStaffReportExporterRecordProcessor;
import jp.ne.interspace.taekkyeon.batch.reader.AsyncStaffReportExporterRecordReader;
import jp.ne.interspace.taekkyeon.batch.writer.AsyncStaffReportExporterWriter;
import jp.ne.interspace.taekkyeon.model.BaseReportExportRequest;
import jp.ne.interspace.taekkyeon.model.BonusReportExportRequest;
import jp.ne.interspace.taekkyeon.model.CampaignClosureReportExportRequest;
import jp.ne.interspace.taekkyeon.model.CampaignReportExportRequest;
import jp.ne.interspace.taekkyeon.model.ConversionReportExportRequest;
import jp.ne.interspace.taekkyeon.model.MerchantActionApprovalReportExportRequest;
import jp.ne.interspace.taekkyeon.model.PublisherConversionReportExportRequest;
import jp.ne.interspace.taekkyeon.model.PublisherSiteReportExportRequest;
import jp.ne.interspace.taekkyeon.model.ReportExportItem;
import jp.ne.interspace.taekkyeon.model.ReportExportType;
import jp.ne.interspace.taekkyeon.service.BaseReportCsvExporter;
import jp.ne.interspace.taekkyeon.service.BaseReportExporter;
import jp.ne.interspace.taekkyeon.service.BonusReportCsvExporter;
import jp.ne.interspace.taekkyeon.service.BonusReportExporter;
import jp.ne.interspace.taekkyeon.service.CampaignClosureReportCsvExporter;
import jp.ne.interspace.taekkyeon.service.CampaignClosureReportExporter;
import jp.ne.interspace.taekkyeon.service.CampaignReportCsvExporter;
import jp.ne.interspace.taekkyeon.service.CampaignReportExporter;
import jp.ne.interspace.taekkyeon.service.ConversionReportCsvExporter;
import jp.ne.interspace.taekkyeon.service.ConversionReportExporter;
import jp.ne.interspace.taekkyeon.service.DefaultEmailPropertiesCreator;
import jp.ne.interspace.taekkyeon.service.EmailPropertiesCreator;
import jp.ne.interspace.taekkyeon.service.MerchantActionApprovalReportCsvExporter;
import jp.ne.interspace.taekkyeon.service.MerchantActionApprovalReportEmailPropertiesCreator;
import jp.ne.interspace.taekkyeon.service.MerchantActionApprovalReportExporter;
import jp.ne.interspace.taekkyeon.service.PublisherConversionReportCsvExporter;
import jp.ne.interspace.taekkyeon.service.PublisherConversionReportEmailPropertiesCreator;
import jp.ne.interspace.taekkyeon.service.PublisherConversionReportExporter;
import jp.ne.interspace.taekkyeon.service.PublisherSiteReportCsvExporter;
import jp.ne.interspace.taekkyeon.service.PublisherSiteReportExporter;

import static java.lang.Integer.parseInt;
import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.RDS_PARTITION_SIZE;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.getPropertyBy;
import static jp.ne.interspace.taekkyeon.model.ReportExportType.ACTION_APPROVAL;
import static jp.ne.interspace.taekkyeon.model.ReportExportType.BONUS;
import static jp.ne.interspace.taekkyeon.model.ReportExportType.CAMPAIGN;
import static jp.ne.interspace.taekkyeon.model.ReportExportType.CAMPAIGN_CLOSURE;
import static jp.ne.interspace.taekkyeon.model.ReportExportType.CONVERSION;
import static jp.ne.interspace.taekkyeon.model.ReportExportType.PUBLISHER_CONVERSION;
import static jp.ne.interspace.taekkyeon.model.ReportExportType.PUBLISHER_SITE;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.PRODUCTION;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;

/**
 * Guice module for the report export batch.
 *
 * <AUTHOR> Mayur
 */
public class AsyncStaffReportExporterModule extends AbstractModule {

    public static final String BIND_KEY_EMAIL_TEMPLATE_PATH = "email.template.path";
    public static final String BIND_KEY_REPORT_WARNING_EMAIL_TEMPLATE_PATH = "warning.email.template.path";
    public static final String BIND_KEY_MERCHANT_ACTION_APPROVAL_REPORT_TEMPLATE_PATHS = "merchant.action.approval.email.template.path";
    public static final String BIND_KEY_MERCHANT_ACTION_APPROVAL_REPORT_EMAIL_SUBJECT = "merchant.action.approval.email.subject";
    public static final String BIND_KEY_PUBLISHER_CONVERSION_REPORT_TEMPLATE_PATHS = "publisher.conversion.email.template.path";
    public static final String BIND_KEY_PUBLISHER_CONVERSION_REPORT_EMAIL_SUBJECT = "publisher.conversion.email.subject";
    public static final String BIND_KEY_ORACLE_IN_LIMIT = "oracle.in.limit";
    public static final String BIND_KEY_CHECK_REPORT_ROWS_LIMIT = "check.report.limit";
    public static final String BIND_KEY_REPORT_ROWS_NUMBER_LIMIT = "report.row.limit";

    private static final String TEMPLATE_FILE_PATH = "downloadLinkReportEmail.ftl";
    private static final String TEMPLATE_WARNING_EMAIL_FILE_PATH = "reportReachLimitInforEmail.ftl";

    public static final String BIND_KEY_PARTITION_LIMITATION = "partition.limitation";
    private static final String PARTITION_LIMITATION = "partitionLimitation";

    public static final String BIND_KEY_CUSTOM_FIELDS_PARTITION_LIMITATION = "custom.fields.partition.limitation";
    private static final String CUSTOM_FIELDS_PARTITION_LIMITATION = "customFieldsPartitionLimitation";

    private static final String FILE_OUTPUT_DIRECTORY = "fileOutputDirectory";

    private static final String CHECK_REPORT_ROWS_LIMIT = "isCheckReportRowsLimit";
    private static final String REPORT_ROWS_LIMIT = "reportRowsLimit";
    public static final String BIND_IS_TEST = "bind.is.test";

    private static final String DEFAULT_EMAIL_SUBJECT = "ACCESSTRADE Report Download Link: Action Approval Report";
    private static final String DEFAULT_PUBLISHER_CONVERSION_EMAIL_SUBJECT = "ACCESSTRADE Report Download Link: Conversion Report";

    private static final ImmutableMap<Environment, String> REPORT_EXPORT_BUCKET_NAMES = new ImmutableMap.Builder<Environment, String>()
            .put(DEV, "images.accesstrade.dev")
            .put(STAGING, "images.accesstrade.dev")
            .put(PRODUCTION, "accesstrade.exports")
            .build();

    private static final ImmutableMap<ReportExportType, Class<? extends BaseReportExportRequest>> REPORT_TYPE_MAPPING = new ImmutableMap.Builder<ReportExportType, Class<? extends BaseReportExportRequest>>()
            .put(CAMPAIGN, CampaignReportExportRequest.class)
            .put(CAMPAIGN_CLOSURE, CampaignClosureReportExportRequest.class)
            .put(CONVERSION, ConversionReportExportRequest.class)
            .put(PUBLISHER_SITE, PublisherSiteReportExportRequest.class)
            .put(ACTION_APPROVAL, MerchantActionApprovalReportExportRequest.class)
            .put(PUBLISHER_CONVERSION, PublisherConversionReportExportRequest.class)
            .put(BONUS, BonusReportExportRequest.class)
            .build();

    private static final ImmutableMap<String, String> MERCHANT_ACTION_APPROVAL_REPORT_TEMPLATE_PATHS = new ImmutableMap.Builder<String, String>()
            .put("ID", "actionApprovalDownloadLinkReportEmail_id.ftl")
            .put("MY", "actionApprovalDownloadLinkReportEmail_en.ftl")
            .put("SG", "actionApprovalDownloadLinkReportEmail_en.ftl")
            .put("TH", "actionApprovalDownloadLinkReportEmail_en.ftl")
            .put("VN", "actionApprovalDownloadLinkReportEmail_en.ftl")
            .build();

    private static final ImmutableMap<String, String> MERCHANT_ACTION_APPROVAL_REPORT_EMAIL_SUBJECT = new ImmutableMap.Builder<String, String>()
            .put("ID", "Tautan Pengunduh Laporan ACCESSTRADE: Laporan Persetujuan Action")
            .put("MY", DEFAULT_EMAIL_SUBJECT).put("SG", DEFAULT_EMAIL_SUBJECT)
            .put("TH", DEFAULT_EMAIL_SUBJECT).put("VN", DEFAULT_EMAIL_SUBJECT)
            .build();

    private static final ImmutableMap<String, String> PUBLISHER_CONVERSION_REPORT_TEMPLATE_PATHS = new ImmutableMap.Builder<String, String>()
            .put("en", "publisherConversionReportEmail_en.ftl")
            .put("id", "publisherConversionReportEmail_id.ftl")
            .put("ja", "publisherConversionReportEmail_ja.ftl")
            .put("th", "publisherConversionReportEmail_th.ftl")
            .put("vi", "publisherConversionReportEmail_en.ftl")
            .put("zh_TW", "publisherConversionReportEmail_zhTW.ftl").build();

    private static final ImmutableMap<String, String> PUBLISHER_CONVERSION_REPORT_EMAIL_SUBJECT = new ImmutableMap.Builder<String, String>()
            .put("en", DEFAULT_PUBLISHER_CONVERSION_EMAIL_SUBJECT)
            .put("id", "Tautan Pengunduh Laporan ACCESSTRADE: Laporan Konversi")
            .put("ja", "ACCESSTRADE レポートダウンロードリンク: コンバージョンレポート")
            .put("th", "ลิงก์สำหรับดาวน์โหลด: รายงานการขาย")
            .put("vi", DEFAULT_PUBLISHER_CONVERSION_EMAIL_SUBJECT)
            .put("zh_TW", "ACCESSTRADE報表下載網址: 交易報表").build();

    @Override
    protected void configure() {
        install(new SimpleQueueServiceQueueConsumerModule());

        bind(RecordReader.class).annotatedWith(MainRecordReaderBinding.class)
                .to(AsyncStaffReportExporterRecordReader.class);
        bind(new TypeLiteral<RecordProcessor<? extends Record<?>, ? extends Record<?>>>() {
        }).annotatedWith(MainRecordProcessorBinding.class)
                .to(AsyncStaffReportExporterRecordProcessor.class);
        bind(new TypeLiteral<Map<ReportExportType, Class<? extends BaseReportExportRequest>>>() {
        }).toInstance(REPORT_TYPE_MAPPING);
        bind(RecordWriter.class).annotatedWith(MainRecordWriterBinding.class)
                .to(AsyncStaffReportExporterWriter.class);
        bind(Integer.class).annotatedWith(CustomBatchSizeBinding.class).toInstance(1);
    }

    @Provides @Singleton @Named(BIND_KEY_EMAIL_TEMPLATE_PATH)
    private String provideEmailTemplatePath() {
        return TEMPLATE_FILE_PATH;
    }

    @Provides @Singleton @Named(BIND_KEY_REPORT_WARNING_EMAIL_TEMPLATE_PATH)
    private String provideWarningEmailTemplatePath() {
        return TEMPLATE_WARNING_EMAIL_FILE_PATH;
    }

    @Provides @Singleton
    private Map<Class<? extends BaseReportExportRequest>,
            BaseReportExporter<? extends BaseReportExportRequest,
               ? extends ReportExportItem, ?>> provideReportExporterMapping(
                  Injector injector) {
        return new ImmutableMap.Builder<Class<? extends BaseReportExportRequest>,
                BaseReportExporter<? extends BaseReportExportRequest,
                        ? extends ReportExportItem, ?>>()
                        .put(CampaignReportExportRequest.class,
                                injector.getInstance(CampaignReportExporter.class))
                        .put(CampaignClosureReportExportRequest.class,
                                injector.getInstance(CampaignClosureReportExporter.class))
                        .put(ConversionReportExportRequest.class,
                                injector.getInstance(ConversionReportExporter.class))
                        .put(PublisherSiteReportExportRequest.class,
                                injector.getInstance(PublisherSiteReportExporter.class))
                        .put(MerchantActionApprovalReportExportRequest.class,
                                injector.getInstance(MerchantActionApprovalReportExporter.class))
                        .put(PublisherConversionReportExportRequest.class,
                                injector.getInstance(PublisherConversionReportExporter.class))
                        .put(BonusReportExportRequest.class,
                                injector.getInstance(BonusReportExporter.class))
                        .build();
    }

    @Provides @Singleton
    private Map<Class<? extends BaseReportExportRequest>,
            BaseReportCsvExporter<? extends ReportExportItem>>
               provideReportCsvExporterMapping(Injector injector) {
        return new ImmutableMap.Builder<Class<? extends BaseReportExportRequest>,
                BaseReportCsvExporter<? extends ReportExportItem>>()
                        .put(CampaignReportExportRequest.class,
                                injector.getInstance(CampaignReportCsvExporter.class))
                        .put(CampaignClosureReportExportRequest.class,
                                injector.getInstance(CampaignClosureReportCsvExporter.class))
                        .put(ConversionReportExportRequest.class,
                                injector.getInstance(ConversionReportCsvExporter.class))
                        .put(PublisherSiteReportExportRequest.class,
                                injector.getInstance(PublisherSiteReportCsvExporter.class))
                        .put(MerchantActionApprovalReportExportRequest.class,
                                injector.getInstance(MerchantActionApprovalReportCsvExporter.class))
                        .put(PublisherConversionReportExportRequest.class,
                                injector.getInstance(PublisherConversionReportCsvExporter.class))
                        .put(BonusReportExportRequest.class,
                                injector.getInstance(BonusReportCsvExporter.class))
                        .build();
    }

    @Provides @Singleton
    private Map<ReportExportType, EmailPropertiesCreator>
                provideEmailPropertiesCreatorMapping(Injector injector) {
        EmailPropertiesCreator defaultInstance = injector
                .getInstance(DefaultEmailPropertiesCreator.class);
        return new ImmutableMap.Builder<ReportExportType, EmailPropertiesCreator>()
                .put(CAMPAIGN, defaultInstance)
                .put(CAMPAIGN_CLOSURE, defaultInstance)
                .put(CONVERSION, defaultInstance)
                .put(PUBLISHER_SITE, defaultInstance)
                .put(ACTION_APPROVAL,
                        injector.getInstance(
                                MerchantActionApprovalReportEmailPropertiesCreator.class))
                .put(PUBLISHER_CONVERSION,
                        injector.getInstance(
                                PublisherConversionReportEmailPropertiesCreator.class))
                .put(BONUS, defaultInstance)
                .build();
    }

    @Provides @Singleton @Named(BIND_KEY_PARTITION_LIMITATION)
    private int getPartitionLimitation() {
        return parseInt(getProperty(PARTITION_LIMITATION));
    }

    @Provides @Singleton @Named(BIND_KEY_CUSTOM_FIELDS_PARTITION_LIMITATION)
    private int getCustomFieldsPartitionLimitation() {
        return parseInt(getProperty(CUSTOM_FIELDS_PARTITION_LIMITATION));
    }

    @Provides @Singleton @FileOutputDirectoryResolver
    private String getFileOutputDirectory() {
        return getPropertyBy(FILE_OUTPUT_DIRECTORY);
    }

    @Provides @Singleton @BucketNameResolver
    private String getBucketName() {
        return REPORT_EXPORT_BUCKET_NAMES.get(getCurrentEnvironment());
    }

    @Provides @Singleton @Named(BIND_KEY_MERCHANT_ACTION_APPROVAL_REPORT_TEMPLATE_PATHS)
    private Map<String, String> provideMerchantActionApprovalTemplatePaths() {
        return MERCHANT_ACTION_APPROVAL_REPORT_TEMPLATE_PATHS;
    }

    @Provides @Singleton @Named(BIND_KEY_MERCHANT_ACTION_APPROVAL_REPORT_EMAIL_SUBJECT)
    private Map<String, String> provideMerchantEmailSubject() {
        return MERCHANT_ACTION_APPROVAL_REPORT_EMAIL_SUBJECT;
    }

    @Provides @Singleton @Named(BIND_KEY_PUBLISHER_CONVERSION_REPORT_TEMPLATE_PATHS)
    private Map<String, String> providePublihserConversionReportTemplatePaths() {
        return PUBLISHER_CONVERSION_REPORT_TEMPLATE_PATHS;
    }

    @Provides @Singleton @Named(BIND_KEY_PUBLISHER_CONVERSION_REPORT_EMAIL_SUBJECT)
    private Map<String, String> providePublihserConversionReportEmailSubject() {
        return PUBLISHER_CONVERSION_REPORT_EMAIL_SUBJECT;
    }

    @Provides @Singleton @Named(BIND_KEY_ORACLE_IN_LIMIT)
    private int provideOracleInLimit() {
        return RDS_PARTITION_SIZE;
    }

    @Provides @Singleton @Named(BIND_KEY_CHECK_REPORT_ROWS_LIMIT)
    private boolean provideIsCheckReportRowsLimit() {
        return Boolean.getBoolean(CHECK_REPORT_ROWS_LIMIT);
    }

    @Provides @Singleton @Named(BIND_KEY_REPORT_ROWS_NUMBER_LIMIT)
    private int provideReportRowsNumberLimit() {
        return Integer.valueOf(getPropertyBy(REPORT_ROWS_LIMIT));
    }

    @Provides @Singleton @Named(BIND_IS_TEST)
    private boolean isTest() {
        return false;
    }
}
