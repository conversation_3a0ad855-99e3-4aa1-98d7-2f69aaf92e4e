/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.util.Locale;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CustomerSupport;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.CountryMapper;

import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link CountryService}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class CountryServiceTest {

    private static final String COUNTRY_CODE = "TH";

    @InjectMocks @Spy
    private CountryService underTest;

    @Mock
    private CountryMapper countryMapper;

    @Test
    public void testFindCustomerSupportByShouldReturnCorrectCustomerSupportWhenNoErrorOccured() {
        // given
        CustomerSupport expected = mock(CustomerSupport.class);
        when(countryMapper.findCustomerSupportBy(COUNTRY_CODE)).thenReturn(expected);

        // when
        CustomerSupport actual = underTest.findCustomerSupportBy(COUNTRY_CODE);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testFindCustomerSupportByShouldReturnDefaultCustomerSupportWhenErrorOccured() {
        // given
        CustomerSupport expected = CustomerSupport.DEFAULT_CUSTOMER_SUPPORT;
        doThrow(Exception.class).when(countryMapper).findCustomerSupportBy(COUNTRY_CODE);

        // when
        CustomerSupport actual = underTest.findCustomerSupportBy(COUNTRY_CODE);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testFindCurrencyByShouldReturnCorrectCurrencyWhenCalled() {
        // given
        String expected = "THB";
        when(countryMapper.findCurrencyBy(COUNTRY_CODE)).thenReturn(expected);

        // when
        String actual = underTest.findCurrencyBy(COUNTRY_CODE);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testFindZoneIdByShouldReturnCorrectZoneIdWhenCalled() {
        // given
        String expected = "Asia/Bangkok";
        when(countryMapper.findZoneIdBy(COUNTRY_CODE)).thenReturn(expected);

        // when
        String actual = underTest.findZoneIdBy(COUNTRY_CODE);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testFindLocaleByShouldReturnCorrectLocaleWhenCalled() {
        // given
        String locale = "en-US";
        when(countryMapper.findLocaleBy(COUNTRY_CODE)).thenReturn(locale);

        // when
        Locale actual = underTest.findLocaleBy(COUNTRY_CODE);

        // then
        assertSame(Locale.US, actual);
    }

    @Test
    public void testFindCountryNameByShouldReturnCorrectCountryNameWhenCalled() {
        // given
        String countryName = "Thailand";
        when(countryMapper.findCountryNameBy(COUNTRY_CODE)).thenReturn(countryName);

        // when
        String actual = underTest.findCountryNameBy(COUNTRY_CODE);

        // then
        assertSame(countryName, actual);
    }

    @Test
    public void testFindTaxMinCalculationByShouldReturnCorrectDataWhenCalled() {
        // given
        BigDecimal expected = new BigDecimal("11.11");
        when(countryMapper.findTaxMinCalculationBy(COUNTRY_CODE)).thenReturn(expected);

        // when
        BigDecimal actual = underTest.findTaxMinCalculationBy(COUNTRY_CODE);

        // then
        assertSame(expected, actual);
    }
}
