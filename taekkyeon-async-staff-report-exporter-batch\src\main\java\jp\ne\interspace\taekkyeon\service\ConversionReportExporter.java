/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.model.ConversionReport;
import jp.ne.interspace.taekkyeon.model.ConversionReportExportRequest;
import jp.ne.interspace.taekkyeon.model.ConversionReportRowCount;
import jp.ne.interspace.taekkyeon.model.ReportExportPagedDetails;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ClickParameterMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionMapper;

import static jp.ne.interspace.taekkyeon.module.AsyncStaffReportExporterModule.BIND_IS_TEST;
import static jp.ne.interspace.taekkyeon.module.AsyncStaffReportExporterModule.BIND_KEY_CHECK_REPORT_ROWS_LIMIT;
import static jp.ne.interspace.taekkyeon.module.AsyncStaffReportExporterModule.BIND_KEY_PARTITION_LIMITATION;
import static jp.ne.interspace.taekkyeon.module.AsyncStaffReportExporterModule.BIND_KEY_REPORT_ROWS_NUMBER_LIMIT;
import static lombok.AccessLevel.PACKAGE;

/**
 * Conversion implementation of {@link BaseReportExporter}.
 *
 * <AUTHOR> Tran
 */
@Slf4j
public class ConversionReportExporter
        implements BaseReportExporter<ConversionReportExportRequest, ConversionReport, Long> {
    private static final long DEFAULT_PAGE = 1;

    @Inject
    private ConversionMapper conversionMapper;

    @Inject
    private ClickParameterMapper clickParamterMapper;

    @Inject @Named(BIND_KEY_PARTITION_LIMITATION) @Getter(PACKAGE)
    private int partitionLimitation;

    @Inject @Named(BIND_KEY_CHECK_REPORT_ROWS_LIMIT) @Getter(PACKAGE)
    private boolean isCheckReportRowsLimit;

    @Inject @Named(BIND_KEY_REPORT_ROWS_NUMBER_LIMIT) @Getter(PACKAGE)
    private int reportRowsNumberLimit;

    @Inject @Named(BIND_IS_TEST) @Getter(PACKAGE)
    private boolean isTest;

    @Override
    public ReportExportPagedDetails<ConversionReport, Long> findReportData(
            ConversionReportExportRequest request, Long page) {
        page = Optional.ofNullable(page)
                .orElse(DEFAULT_PAGE);
        return findConversionReportDataPaging(page);
    }

    @Override
    public List<String> findCustomFields(ConversionReportExportRequest request) {
        int insertedConversionTempCount = 0;
        try {
            insertedConversionTempCount = conversionMapper
                    .insertConversionTempTable(request,
                    request.getFromDate().toLocalDate(),
                    request.getToDate().toLocalDate());
            getLogger().info("Conversion Insertion Temporary has been successfully inserted!"
                    + " Inserted count: {}", insertedConversionTempCount);
            ConversionReportRowCount.setRowCount(insertedConversionTempCount);
        } catch (Exception ex) {
            getLogger().error("Insert Conversion data temporary is failed: \n{}", ex);
        }
        if (insertedConversionTempCount == 0
                || (isCheckReportRowsLimit()
                && insertedConversionTempCount > getReportRowsNumberLimit())) {
            return Collections.emptyList();
        }
        int insertedClickParameterTempCount = 0;
        try {
            insertedClickParameterTempCount = clickParamterMapper
                    .insertClickParameterTempTable();
            getLogger().info("Click parameter Insertion Temporary has been successfully inserted!"
                    + " Inserted count: {}", insertedClickParameterTempCount);
        } catch (Exception ex) {
            getLogger().error("Insert Click parameter data temporary is failed: \n{}", ex);
        }
        if (insertedClickParameterTempCount == 0) {
            return Collections.emptyList();
        }
        return new LinkedList<>(clickParamterMapper
                .getClickParameterNamesFromTempTable());
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }

    @VisibleForTesting
    ReportExportPagedDetails<ConversionReport, Long> findConversionReportDataPaging(
            long page) {
        if (ConversionReportRowCount.getRowCount() == 0
                || (isCheckReportRowsLimit()
                && ConversionReportRowCount.getRowCount() > getReportRowsNumberLimit())) {
            return new ReportExportPagedDetails<>(Collections.emptyList(),
                    page);
        }
        long offset = (page - 1) * getPartitionLimitation() + 1;
        long limit =  page * getPartitionLimitation();
        List<ConversionReport> conversions = conversionMapper
                .findFullConversionReportFromTempTableBy(
                        offset, limit, isTest());
        return new ReportExportPagedDetails<>(conversions,
                ++page);
    }
}
