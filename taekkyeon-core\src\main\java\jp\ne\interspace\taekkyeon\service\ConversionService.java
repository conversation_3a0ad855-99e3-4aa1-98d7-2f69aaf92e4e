/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.model.CampaignSettingDuplicationCutDetails;
import jp.ne.interspace.taekkyeon.validator.LogValidator;

import static com.google.common.base.Joiner.on;
import static com.google.common.base.Strings.emptyToNull;
import static com.google.common.base.Strings.isNullOrEmpty;
import static java.util.UUID.randomUUID;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DOLLAR;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.HYPHEN;
import static jp.ne.interspace.taekkyeon.model.DuplicationCutTarget.DAILY;

/**
 * Service layer for handling conversion data.
 *
 * <AUTHOR> Ferreras
 */
@Singleton
public class ConversionService {

    private static final String TRANSACTION_ID = "TRANSACTION_ID";

    private static final int INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID = 3;
    private static final int INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID = 30;

    private static final int TRANSACTION_ID_MAX_BYTE_LENGTH = 512;

    @Inject
    private CampaignSettingService campaignSettingService;

    @Inject
    private LogValidator logValidator;

    /**
     * Creates internal transaction ID.
     *
     * @param campaignId
     *            the given campaign ID
     * @param conversionDate
     *            the given conversion date
     * @param identifier
     *            the given indentifier
     * @param resultId
     *            the given result ID
     * @param customerType
     *            the given customer type
     * @return internal transaction ID
     */
    public String createInternalTransactionIdBy(long campaignId, LocalDate conversionDate,
            String identifier, int resultId, String customerType) {
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                campaignSettingService.findDuplicationCutDetailsBy(campaignId);
        if (duplicationCutDetails.isEnabled()) {
            String permanentTargetInternalTransactionId =
                    createPermanentTargetInternalTransactionId(campaignId, identifier,
                            resultId, customerType);
            return duplicationCutDetails.getTarget() == DAILY
                    ? on(DOLLAR).join(conversionDate,
                            permanentTargetInternalTransactionId)
                    : permanentTargetInternalTransactionId;
        } else {
            return randomUUID().toString();
        }
    }

    /**
     * Generates a list of transaction IDs.
     *
     * @param quantity
     *            the given quantity
     * @param conversionDate
     *            the given conversion date
     * @param identifier
     *            the given identifier
     * @param productId
     *            the given product ID
     * @param resultId
     *            the given result ID
     * @param campaignId
     *            the given campaign ID
     * @param customerType
     *            the given customer type
     * @return a list of transaction IDs
     */
    public List<String> generateTransactionIdsFrom(long quantity,
            LocalDateTime conversionDate, String identifier, String productId,
            int resultId, long campaignId, String customerType) {
        return generateTransactionIdsFrom(quantity, conversionDate, identifier,
                productId, resultId, campaignId, customerType, null);
    }

    /**
     * Generates a list of transaction IDs with conversion parameters support.
     *
     * @param quantity
     *            the given quantity
     * @param conversionDate
     *            the given conversion date
     * @param identifier
     *            the given identifier
     * @param productId
     *            the given product ID
     * @param resultId
     *            the given result ID
     * @param campaignId
     *            the given campaign ID
     * @param customerType
     *            the given customer type
     * @param atDuplicatedProduct
     *            the conversion duplicated product
     * @return a list of transaction IDs
     */
    public List<String> generateTransactionIdsFrom(long quantity,
            LocalDateTime conversionDate, String identifier, String productId,
            int resultId, long campaignId, String customerType,
            String atDuplicatedProduct) {
        List<String> transactionIds = new ArrayList<>();
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                campaignSettingService.findDuplicationCutDetailsBy(campaignId);
        String transactionIdPrefix = createTransactionIdPrefix(conversionDate, identifier,
                resultId, customerType);

        for (int index = 0; index < quantity; index++) {
            String transactionId = transactionIdPrefix;
            if (!isNullOrEmpty(productId)) {
                String productIdForTransactionId = productId;
                if (isMultipleProductConversion(resultId, quantity)) {
                    productIdForTransactionId = on(HYPHEN).join(productId, index);
                }
                transactionId = on(HYPHEN).join(transactionId,
                        productIdForTransactionId);
            }
            if (!isNullOrEmpty(atDuplicatedProduct)) {
                transactionId = on(HYPHEN).join(transactionId, atDuplicatedProduct);
            } else {
                transactionId = duplicationCutDetails.isEnabled() ? transactionId
                        : on(HYPHEN).join(transactionId,
                                getUniqueKeyForTransactionId());
            }
            logValidator.validateMaxByteCountOf(transactionId,
                    TRANSACTION_ID_MAX_BYTE_LENGTH, TRANSACTION_ID);
            transactionIds.add(transactionId);
        }
        return transactionIds;
    }

    @VisibleForTesting
    String createPermanentTargetInternalTransactionId(long campaignId, String identifier,
            int resultId, String customerType) {
        return on(DOLLAR).skipNulls().join(campaignId, identifier, resultId,
                emptyToNull(customerType));
    }

    @VisibleForTesting
    String createTransactionIdPrefix(LocalDateTime conversionDate, String identifier,
            int resultId, String customerType) {
        LocalDateTime truncatedDateTime = conversionDate.toLocalDate().atStartOfDay();
        return on(HYPHEN).skipNulls()
                .join(truncatedDateTime.format(DATE_TIME_FORMATTER), identifier, resultId,
                        emptyToNull(customerType));
    }

    @VisibleForTesting
    boolean isMultipleProductConversion(int resultId, long quantity) {
        return (resultId == INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID
                || resultId == INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID)
                && quantity > 1;
    }

    @VisibleForTesting
    String getUniqueKeyForTransactionId() {
        return randomUUID().toString().replaceAll(HYPHEN, EMPTY);
    }
}
