/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

import jp.ne.interspace.taekkyeon.model.CreativeType;
import jp.ne.interspace.taekkyeon.model.ConversionReport;
import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.BonusStatus;
import jp.ne.interspace.taekkyeon.model.CampaignClosureConversionItem;
import jp.ne.interspace.taekkyeon.model.ConversionBonusReportDetail;
import jp.ne.interspace.taekkyeon.model.CampaignClosurePeriod;
import jp.ne.interspace.taekkyeon.model.CampaignReportConversionItem;
import jp.ne.interspace.taekkyeon.model.CampaignReportExportRequest;
import jp.ne.interspace.taekkyeon.model.ConversionClickParameterRequest;
import jp.ne.interspace.taekkyeon.model.ConversionReportExportMapperRequest;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.FindMerchantActionApprovalReportDataRequest;
import jp.ne.interspace.taekkyeon.model.MerchantActionApprovalHolder;
import jp.ne.interspace.taekkyeon.model.PostbackStatus;
import jp.ne.interspace.taekkyeon.model.PublisherConversionLogItem;
import jp.ne.interspace.taekkyeon.model.PublisherType;
import jp.ne.interspace.taekkyeon.model.ReportQueryPeriodBase;
import jp.ne.interspace.taekkyeon.model.RewardType;
import jp.ne.interspace.taekkyeon.model.SiteType;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for handling result data.
 *
 * <AUTHOR> Cao
 */
public interface ConversionMapper {

    String START_TAG_SCRIPT = "<script>";
    String END_TAG_SCRIPT = "</script>";
    String SELECT = " SELECT ";
    String PARALLEL = " /*+ PARALLEL(AUTO) */ ";
    String END_FOREACH = " </foreach> ";

    /**
        SELECT
            report.id as id,
            report.siteId as siteId,
            report.campaignId as campaignId,
            report.actionId as actionId,
            report.actionDate as actionDate,
            report.publisherSite as publisherSite,
            report.status as status,
            report.salesAmount as salesAmount,
            report.reward as reward,
            report.deadline as deadline,
            report.categoryId as categoryId,
            report.discountAmount as discount,
            report.hideClickReferrer as hideClickReferrer
        FROM (
            SELECT
                conversion.*,
                ROWNUM rowNumber
            FROM (
                SELECT
                    sl.seq_no as id,
                    s.site_no as siteId,
                    c.campaign_no as campaignId,
                    sl.verify as actionId,
                    sl.sales_date as actionDate,
                    s.site_name as publisherSite,
                    sl.sales_log_status as status,
                    sl.total_price as salesAmount,
                    (CASE
                        WHEN
                            sl.reward_type = 1
                        THEN
                            NVL(sl.sales_reward, 0) + NVL(sl.at_commission, 0)
                        ELSE
                            NVL(sl.total_price_reward, 0) + NVL(sl.at_commission, 0)
                    END) as reward,
                    (CASE
                        WHEN
                            NVL(NVL(rt1.auto_action_appr_duration,
                            rt2.auto_action_appr_duration), 0) > 0
                        THEN
                            sl.sales_date + NVL(rt1.auto_action_appr_duration,
                            rt2.auto_action_appr_duration)
                    END) as deadline,
                    sl.category_id as categoryId,
                    NVL(sl.discount, 0) as discountAmount,
                    c.hide_click_referrer as hideClickReferrer
                FROM
                    sales_log sl
                INNER JOIN
                    partner_site s
                ON
                    sl.partner_site_no = s.site_no
                INNER JOIN
                    merchant_campaign c
                ON
                    sl.merchant_campaign_no = c.campaign_no
                LEFT JOIN
                    result_target_setting rt1
                ON
                    sl.merchant_campaign_no = rt1.merchant_campaign_no
                AND
                    sl.result_id = rt1.result_id
                AND
                    sl.customer_type = rt1.customer_type
                LEFT JOIN
                    result_target_setting rt2
                ON
                    sl.merchant_campaign_no = rt2.merchant_campaign_no
                AND
                    sl.result_id = rt2.result_id
                AND
                    rt2.customer_type IS NULL
                WHERE
                    SALES_DATE BETWEEN #{fromDate}
                        AND #{toDate}
                <if test = "campaignIds != null and !campaignIds.isEmpty()">
                    AND
                        c.campaign_no IN
                            <foreach item="item" index="index" collection="campaignIds"
                                open="(" separator="," close=")">
                                #{item}
                            </foreach>
                </if>
                <if test = "siteName != null and !siteName.isEmpty()">
                    AND
                        (s.site_no = (CASE
                                WHEN
                                    c.hide_click_referrer =1
                                THEN
                                    <choose>
                                        <when test = "isSiteId">
                                            TO_NUMBER(#{siteName})
                                        </when>
                                        <otherwise>
                                            0
                                        </otherwise>
                                    </choose>
                                ELSE
                                    0
                            END)
                        OR s.site_name = (CASE
                                WHEN
                                    c.hide_click_referrer !=1
                                THEN
                                    #{siteName}
                                ELSE
                                    ''
                            END))
                </if>
                <if test = "status != -1">
                    AND
                        sl.sales_log_status = #{status}
                </if>
                <if test = "verify != null and !verify.isEmpty()">
                    AND
                        sl.verify = #{verify}
                </if>
                ) conversion
            WHERE
                <![CDATA[
                    ROWNUM < ((#{pageNumber} * #{pageSize}) + 1 )
                ]]>
            ) report
        WHERE
            <![CDATA[
                rowNumber >= (((#{pageNumber}-1) * #{pageSize}) + 1)
            ]]>
        ORDER BY actionDate DESC
     */
    @Multiline String SELECT_MERCHANT_ACTION_APPROVAL_REPORT_DATA = "";

    /**
        SELECT
            sl.partner_site_no
        FROM
            sales_log sl
        INNER JOIN
            partner_site ps
        ON
            sl.partner_site_no = ps.site_no
        INNER JOIN
            partner_account pa
        ON
            ps.account_no = pa.account_no
        LEFT JOIN
            site_categories sc
        ON
            sc.site_id = ps.site_no
        WHERE
            pa.country_code = #{countryCode}
        <if test = "periodBase.name() == 'CONVERSION_DATE'">
        AND
            sl.sales_date
                BETWEEN #{fromDate, jdbcType=DATE, typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.ResultColumnZonedDateTimeTypeHandler}
                AND #{toDate, jdbcType=DATE, typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.ResultColumnZonedDateTimeTypeHandler}
        </if>
        <if test = "periodBase.name() == 'CONFIRMATION_DATE'">
        AND
            sl.confirmed_date
                BETWEEN #{fromDate, jdbcType=DATE, typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.ResultColumnZonedDateTimeTypeHandler}
                AND #{toDate, jdbcType=DATE, typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.ResultColumnZonedDateTimeTypeHandler}
        </if>
        <if test = "publisherType != null and publisherType.name() != 'UNSPECIFIED'">
        AND
            pa.is_global = #{publisherType.value}
        </if>
        <if test = "publisherAgencyId != null and publisherAgencyId > 0">
        AND
            pa.agency_id = #{publisherAgencyId}
        </if>
        <if test = "siteCategories != null and !siteCategories.isEmpty()">
        AND
            sc.category_id IN
                <foreach item="item" index="index" collection="siteCategories"
                        open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>
        <if test = "siteTypes != null and !siteTypes.isEmpty()">
        AND
            ps.site_type IN
            <foreach item="item" index="index" collection="siteTypes"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
     */
    @Multiline String SELECT_SITE_IDS = "";

    /**
         SELECT
            DISTINCT partner_site_no
         FROM
         (
             <foreach item="materializeViewName" index="index"
                    collection="materializeViewNames" separator=" UNION ALL ">
                 SELECT
                    DISTINCT sl.partner_site_no
                 FROM
                    ${materializeViewName} sl
                 INNER JOIN
                    partner_site ps
                 ON
                    sl.partner_site_no = ps.site_no
                 INNER JOIN
                    partner_account pa
                 ON
                    ps.account_no = pa.account_no
                 LEFT JOIN
                    site_categories sc
                 ON
                    sc.site_id = ps.site_no
                 WHERE
                    pa.country_code = #{countryCode}
                 <if test = "periodBase.name() == 'CONVERSION_DATE'">
                 AND
                    sl.sales_date
                        BETWEEN #{fromDate, jdbcType=DATE, typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.ResultColumnZonedDateTimeTypeHandler}
                        AND #{toDate, jdbcType=DATE, typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.ResultColumnZonedDateTimeTypeHandler}
                 </if>
                 <if test = "periodBase.name() == 'CONFIRMATION_DATE'">
                 AND
                    sl.confirmed_date
                        BETWEEN #{fromDate, jdbcType=DATE, typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.ResultColumnZonedDateTimeTypeHandler}
                        AND #{toDate, jdbcType=DATE, typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.ResultColumnZonedDateTimeTypeHandler}
                 </if>
                 <if test = "publisherType != null and publisherType.name() != 'UNSPECIFIED'">
                 AND
                    pa.is_global = #{publisherType.value}
                 </if>
                 <if test = "publisherAgencyId != null and publisherAgencyId > 0">
                 AND
                    pa.agency_id = #{publisherAgencyId}
                 </if>
                 <if test = "siteCategories != null and !siteCategories.isEmpty()">
                 AND
                    sc.category_id IN
                     <foreach item="item" index="index" collection="siteCategories"
                            open="(" separator="," close=")">
                        #{item}
                     </foreach>
                 </if>
                 <if test = "siteTypes != null and !siteTypes.isEmpty()">
                 AND
                    ps.site_type IN
                     <foreach item="item" index="index" collection="siteTypes"
                            open="(" separator="," close=")">
                        #{item}
                     </foreach>
                 </if>
             </foreach>
        )
     */
    @Multiline String SELECT_SITE_IDS_WITH_MV = "";

    /**
     * Returns the {@link MerchantActionApprovalHolder}s for the given result merchant holders.
     *
     * @param request
     *            the give report data request for merchant action aprroval
     * @return the {@link MerchantActionApprovalHolder}s for the given result merchant holders
     */
    @Select(START_TAG_SCRIPT + SELECT_MERCHANT_ACTION_APPROVAL_REPORT_DATA
            + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "id", javaType = long.class),
            @Arg(column = "siteId", javaType = long.class),
            @Arg(column = "campaignId", javaType = long.class),
            @Arg(column = "actionId", javaType = String.class),
            @Arg(column = "actionDate", javaType = LocalDate.class),
            @Arg(column = "publisherSite", javaType = String.class),
            @Arg(column = "status", javaType = int.class),
            @Arg(column = "salesAmount", javaType = BigDecimal.class),
            @Arg(column = "reward", javaType = BigDecimal.class),
            @Arg(column = "deadline", javaType = LocalDate.class),
            @Arg(column = "categoryId", javaType = String.class),
            @Arg(column = "discount", javaType = BigDecimal.class),
            @Arg(column = "hideClickReferrer", javaType = boolean.class) })
    List<MerchantActionApprovalHolder> findMerchantActionApprovalReportData(
            FindMerchantActionApprovalReportDataRequest request);

    /**
     * Returns the site IDs by the given search criteria.
     *
     * @param fromDate
     *          period of start date
     * @param toDate
     *          period of end date
     * @param publisherAgencyId
     *          ID by the given publisher agency
     * @param publisherType
     *          type of the publisher
     * @param siteTypes
     *          type of the site
     * @param siteCategories
     *          category of the site
     * @param countryCode
     *          short code of country
     * @param periodBase
     *          report period condition
     * @return the site IDs by the given search criteria
     */
    @Select(START_TAG_SCRIPT + SELECT_SITE_IDS + END_TAG_SCRIPT)
    Set<Long> findSiteIds(@Param("fromDate") ZonedDateTime fromDate,
            @Param("toDate") ZonedDateTime toDate,
            @Param("publisherAgencyId") Long publisherAgencyId,
            @Param("publisherType") PublisherType publisherType,
            @Param("siteTypes") List<SiteType> siteTypes,
            @Param("siteCategories") List<Long> siteCategories,
            @Param("countryCode") String countryCode,
            @Param("periodBase") ReportQueryPeriodBase periodBase);

    /**
        SELECT
            cc.id,
            NVL(SUM(sl.at_commission) + SUM(sl.sales_reward)
            + SUM(sl.total_price_reward) + SUM(sl.agent_commission)
            + SUM(sl.p_agent_commission), 0) totalCommission,
            NVL(SUM(sl.at_commission), 0) atCommission,
            NVL(SUM(sl.sales_reward) + SUM(sl.total_price_reward), 0) publisherReward,
            COUNT(DISTINCT(sl.internal_transaction_id)) conversions,
            NVL(SUM(sl.sales_count), 0) transactionItems
        FROM
            sales_log sl
        INNER JOIN
            campaign_closure cc
        ON
            sl.merchant_campaign_no = cc.campaign_id
        WHERE
            cc.id IN
            <foreach item="item" index="index" collection="ids"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        AND
            sl.sales_log_status = 1
        AND
            cc.is_processing = 0
        <![CDATA[
        AND
            sl.confirmed_date >= cc.closed_from
        AND
            sl.confirmed_date < ADD_MONTHS(TRUNC(closed_from, 'MM'), 1)
        ]]>
        GROUP BY
            cc.id
     */
    @Multiline String SELECT_CONVERSIONS_CAMPAIGN_CLOSURE_DETAILS = "";

    /**
     * Find the conversions campaign closure details by the given search criteria.
     *
     * @param targetDate
     *            the given target date
     * @param ids
     *            the given campaign closure ID
     * @return the conversions campaign closure details by the given search criteria
     */
    @Select(START_TAG_SCRIPT + SELECT_CONVERSIONS_CAMPAIGN_CLOSURE_DETAILS
            + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "id", javaType = long.class),
            @Arg(column = "totalCommission", javaType = BigDecimal.class),
            @Arg(column = "atCommission", javaType = BigDecimal.class),
            @Arg(column = "publisherReward", javaType = BigDecimal.class),
            @Arg(column = "conversions", javaType = int.class),
            @Arg(column = "transactionItems", javaType = int.class) })
    List<CampaignClosureConversionItem> findConversionsCampaignClosureDetails(
            @Param("targetDate") ZonedDateTime targetDate, @Param("ids") List<Long> ids);

    /**
        SELECT
            salesLogData.*
        FROM (
            SELECT
                salesLog.*, ROWNUM as rowNumber
            FROM (
     */
    @Multiline String START_ROWNUM = "";

    /**
            ) salesLog
            WHERE
            <![CDATA[
                ROWNUM < ((#{pageNumber} * #{pageSize}) + 1 )
            ]]>
        ) salesLogData
        WHERE
        <![CDATA[
            rowNumber >= (((#{pageNumber}-1) * #{pageSize}) + 1)
        ]]>
     */
    @Multiline String END_ROWNUM = "";

    /**
        SELECT
            seq_no AS id,
            0 AS bonusId,
            merchant_campaign_no AS campaignId,
            partner_site_no AS siteId,
            confirmed_date AS confirmationTime,
            sales_log_status AS status,
            at_bonus AS atBonus,
            publisher_bonus AS publisherBonus,
            merchant_agent_bonus AS merchantAgentBonus ,
            publisher_agent_bonus AS publisherAgentBonus,
            at_bonus_in_usd AS atBonusInUsd,
            publisher_bonus_in_usd AS publisherBonusInUsd,
            merchant_agent_bonus_in_usd AS merchantAgentBonusInUsd,
            publisher_agent_bonus_in_usd AS publisherAgentBonusInUsd,
            bonus_setting_id AS bonusSettingId,
            bonus_created_on AS createdOn,
            bonus_created_by AS createdBy,
            sales_date AS conversionTime
     */
    @Multiline String CONVERSION_BONUS_REPORT_DETAILS = "";

    /**
        FROM
            sales_log
        WHERE
     */
    @Multiline String FROM_SALES_LOG = "";

    /**
        <if test = "statuses != null and !statuses.isEmpty()">
        AND
            sales_log_status IN
            <foreach item="item" index="index" collection="statuses"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test = "siteIds != null and !siteIds.isEmpty()">
        AND
            partner_site_no IN
            <foreach item="item" index="index" collection="siteIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test = "campaignIds != null and !campaignIds.isEmpty()">
        AND
            merchant_campaign_no IN
            <foreach item="item" index="index" collection="campaignIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test = "bonusSettingIds != null and !bonusSettingIds.isEmpty()">
        AND
            bonus_setting_id IN
            <foreach item="item" index="index" collection="bonusSettingIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
     */
    @Multiline String SEARCH_SALES_LOG_CONDITIONS = "";

    /**
        <![CDATA[
            sales_date >= #{fromDate}
        AND
            sales_date <= #{toDate}
        ]]>
     */
    @Multiline String CONDITION_BY_CONVERSION_DATE = "";

    /**
        <![CDATA[
            confirmed_date >= #{fromDate}
        AND
            confirmed_date <= #{toDate}
        ]]>
     */
    @Multiline String CONDITION_BY_CONFIRMATION_DATE = "";

    /**
        WITH conversions AS (
     */
    @Multiline String START_WITH_CONVERSIONS = "";

    /**
        <foreach item="materializeViewName" index="index"
            collection="materializeViewNames" separator=" UNION ALL ">
     */
    @Multiline String START_CONVERSION_FOREACH = "";

    /**
         SELECT
            data.seq_no,
            data.merchant_campaign_no,
            data.internal_transaction_id,
            ROWNUM
         FROM
            SALES_LOG data
     */
    @Multiline String SELECT_CONVERSION_DATA_FROM_TEMP_TABLE_START = "";

    /**
        INSERT INTO
            conversion_temp_ids (
                id,
                merchant_campaign_no,
                internal_transaction_id,
                row_num
            )
     */
    @Multiline String INSERT_CONVERSION_DATA_TO_TEMP_TABLE_START = "";

    /**
            distinct data.internal_transaction_id,
            data.merchant_campaign_no
        FROM
            ${materializeViewName} data
     */
    @Multiline String SELECT_CONVERSION_INTERNAL_TRANSACTION_ID_CONVERSIONS = "";

    /**
        WHERE
        <if test = "request.periodBase.name() == 'CONVERSION_DATE'">
        <![CDATA[
            sales_date >= #{fromDate}
        AND
            sales_date < #{toDate}
        ]]>
        </if>
        <if test = "request.periodBase.name() == 'CONFIRMATION_DATE'">
        <![CDATA[
            confirmed_date >= #{fromDate}
        AND
            confirmed_date < #{toDate}
        ]]>
        </if>
        <if test = "request.isPendingForMerchantPayment
                and request.conversionStatuses != null
                and !request.conversionStatuses.isEmpty()">
        AND (
            sales_log_status IN
            <foreach item="item" index="index" collection="request.conversionStatuses"
                open="(" separator="," close=")">
                #{item}
            </foreach>
            OR (
                sales_log_status = 1
                AND EXISTS (
                    SELECT
                        0
                    FROM
                        campaign_closure cc
                    WHERE
                        cc.status = 1
                    AND
                        data.merchant_campaign_no = cc.campaign_id
                    AND
                    <![CDATA[
                        data.confirmed_date >= cc.closed_from
                    AND
                        data.confirmed_date <= cc.closed_to
                    ]]>
                )
            )
        )
        </if>
        <if test = "request.isPendingForMerchantPayment
                and (request.conversionStatuses == null
                or request.conversionStatuses.isEmpty())">
        AND
            sales_log_status = 1
        AND EXISTS (
            SELECT
                0
            FROM
                campaign_closure cc
            WHERE
                cc.status = 1
            AND
                data.merchant_campaign_no = cc.campaign_id
            AND
            <![CDATA[
                data.confirmed_date >= cc.closed_from
            AND
                data.confirmed_date <= cc.closed_to
            ]]>
        )
        </if>
        <if test = "!request.isPendingForMerchantPayment
                and request.conversionStatuses != null
                and !request.conversionStatuses.isEmpty()">
        AND
            sales_log_status IN
            <foreach item="item" index="index" collection="request.conversionStatuses"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
     */
    @Multiline String WHERE_CONVERSIONS = "";

    /**
        <if test = "request.campaignIds != null
                and !request.campaignIds.isEmpty()">
            AND (data.merchant_campaign_no IN
            <foreach item="item" index="index" collection="request.campaignIds"
                open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 0">
                            ) OR data.merchant_campaign_no IN (
                        </when>
                        <otherwise>
                            ,
                        </otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
            )
        </if>
     */
    @Multiline String AND_CAMPAIGN_IDS = "";

    /**
        <if test = "request.siteIds != null
                and !request.siteIds.isEmpty()">
            AND (data.partner_site_no IN
            <foreach item="item" index="index" collection="request.siteIds"
                open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 0">
                            ) OR data.partner_site_no IN (
                        </when>
                        <otherwise>
                            ,
                        </otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
            )
        </if>
     */
    @Multiline String AND_SITE_IDS = "";

    /**
        <if test = "request.conversionIds != null
                and !request.conversionIds.isEmpty()">
            AND (seq_no IN
            <foreach item="item" index="index" collection="request.conversionIds"
                open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 0">
                            ) OR seq_no IN (
                        </when>
                        <otherwise>
                            ,
                        </otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
            )
        </if>
     */
    @Multiline String AND_CONVERSION_IDS = "";

    /**
        <if test = "request.creativeIds != null
                and !request.creativeIds.isEmpty()">
            AND (banner_id IN
            <foreach item="item" index="index" collection="request.creativeIds"
                open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 0">
                            ) OR banner_id IN (
                        </when>
                        <otherwise>
                            ,
                        </otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
            )
        </if>
     */
    @Multiline String AND_CREATIVE_IDS = "";

    /**
        <if test = "request.deviceTypes != null
                and !request.deviceTypes.isEmpty()">
        AND
            device_type IN
            <foreach item="item" index="index" collection="request.deviceTypes"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
     */
    @Multiline String AND_DEVICE_TYPES = "";

    /**
        <if test = "request.productIds != null
                and !request.productIds.isEmpty()">
            AND (goods_id IN
            <foreach item="item" index="index" collection="request.productIds"
                open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 0">
                            ) OR goods_id IN (
                        </when>
                        <otherwise>
                            ,
                        </otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
            )
        </if>
     */
    @Multiline String AND_PRODUCT_IDS = "";

    /**
        <if test = "request.ranks != null
                and !request.ranks.isEmpty()">
            AND (rank IN
            <foreach item="item" index="index" collection="request.ranks"
                open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 0">
                            ) OR rank IN (
                        </when>
                        <otherwise>
                            ,
                        </otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
            )
        </if>
     */
    @Multiline String AND_RANKS = "";

    /**
        <if test = "request.resultIds != null
                and !request.resultIds.isEmpty()">
            AND (result_id IN
            <foreach item="item" index="index" collection="request.resultIds"
                open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 0">
                            ) OR result_id IN (
                        </when>
                        <otherwise>
                            ,
                        </otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
            )
        </if>
     */
    @Multiline String AND_RESULT_IDS = "";

    /**
        <if test = "request.verificationIds != null
                and !request.verificationIds.isEmpty()">
            AND (verify IN
            <foreach item="item" index="index" collection="request.verificationIds"
                open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 0">
                            ) OR verify IN (
                        </when>
                        <otherwise>
                            ,
                        </otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
            )
        </if>
     */
    @Multiline String AND_VERIFIES = "";

    /**
        ) SELECT
            data.internal_transaction_id,
            data.merchant_campaign_no
        FROM
            conversions data
     */
    @Multiline String END_WITH_CONVERSIONS_TRANS_ID = "";

    /**
     OFFSET #{offset} ROWS FETCH NEXT #{limit} ROWS ONLY
     */
    @Multiline String OFFSET_FETCH = "";

    /**
         SELECT
             conversionId,
             siteId,
             campaignId,
             creativeId,
             verificationId,
             internalTransactionId,
             sessionId,
             productId,
             productQuantity,
             productCategoryId,
             customerType,
             clickIpAddress,
             language,
             clickUserAgent,
             deviceType,
             clickReferer,
             status,
             reward,
             transactionAmount,
             productUnitPrice,
             clickTime,
             conversionTime,
             confirmationTime,
             postbackStatus,
             postbackErrorCount,
             latestPostbackTime,
             postbackUrl,
             ma.country_code as merchantCountryCode,
             pa.country_code as publisherCountryCode
         FROM (
     */
    @Multiline String SELECT_CONVERSION_START = "";

    /**
        SELECT
            distinct data.internal_transaction_id as internalTransactionId,
            data.merchant_campaign_no as campaignId
        FROM
            sales_log data
        <if test = "request.periodBase.name() == 'PAID_DATE'">
        INNER JOIN (
            SELECT
                cc.campaign_id,
                cc.closed_from,
                cc.closed_to
            FROM
                campaign_closure cc
            WHERE
                cc.id IN
            <foreach item="item" index="index" collection="campaignClosureIds"
                    open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test = "request.campaignIds != null and !request.campaignIds.isEmpty()">
            AND
                cc.campaign_id IN
            <foreach item="item" index="index" collection="request.campaignIds"
                    open="(" separator="," close=")">
                #{item}
            </foreach>
            </if>
        ) cc
        ON
            cc.campaign_id = data.merchant_campaign_no
        </if>
     */
    @Multiline String SELECT_PUBLISHER_TRANS_ID_DATA = "";

    /**
        SELECT
             data.seq_no as conversionId,
             data.partner_site_no as siteId,
             data.merchant_campaign_no as campaignId,
             data.banner_id as creativeId,
             data.verify as verificationId,
             data.internal_transaction_id as internalTransactionId,
             data.session_id as sessionId,
             data.goods_id as productId,
             data.sales_count as productQuantity,
             data.category_id as productCategoryId,
             data.customer_type as customerType,
             data.click_ip as clickIpAddress,
             data.language as language,
             data.click_user_agent as clickUserAgent,
             data.device_type as deviceType,
             data.click_referer as clickReferer,
             data.sales_log_status as status,
             <choose>
                 <when test = "currency == 'USD'">
                     data.publisher_reward_in_usd AS reward,
                     data.transaction_amount_in_usd AS transactionAmount,
                     data.unit_price_in_usd AS productUnitPrice,
                 </when>
                 <otherwise>
                     data.sales_reward + data.total_price_reward AS reward,
                     data.total_price AS transactionAmount,
                     data.price AS productUnitPrice,
                 </otherwise>
             </choose>
             data.click_date as clickTime,
             data.sales_date as conversionTime,
             data.confirmed_date as confirmationTime,
             data.postback_status as postbackStatus,
             data.postback_error_count as postbackErrorCount,
             data.latest_postback_time as latestPostbackTime,
             data.postback_url as postbackUrl
         FROM
            sales_log data
         <if test = "request.periodBase.name() == 'PAID_DATE'">
         INNER JOIN (
             SELECT
                 cc.campaign_id,
                 cc.closed_from,
                 cc.closed_to
             FROM
                campaign_closure cc
             WHERE
                cc.id IN
             <foreach item="item" index="index" collection="campaignClosureIds"
                    open="(" separator="," close=")">
                #{item}
             </foreach>
             <if test = "request.campaignIds != null and !request.campaignIds.isEmpty()">
             AND
                cc.campaign_id IN
             <foreach item="item" index="index" collection="request.campaignIds"
                    open="(" separator="," close=")">
                #{item}
             </foreach>
             </if>
         ) cc
         ON
            cc.campaign_id = data.merchant_campaign_no
         </if>
     */
    @Multiline String SELECT_RAW_CONVERSION_DATA = "";

    /**
         WHERE
         <if test = "request.periodBase.name() == 'CONVERSION_DATE'">
         <![CDATA[
            data.sales_date >= #{fromDate}
         AND
            data.sales_date < #{toDate}
         ]]>
         </if>
         <if test = "request.periodBase.name() == 'CONFIRMATION_DATE'">
         <![CDATA[
            data.confirmed_date >= #{fromDate}
         AND
            data.confirmed_date < #{toDate}
         ]]>
         </if>
         <if test = "request.periodBase.name() == 'PAID_DATE'">
            data.confirmed_date >= cc.closed_from
         AND
            cc.closed_to >= data.confirmed_date
         </if>
         <if test = "request.siteIds != null and !request.siteIds.isEmpty()">
         AND
            data.partner_site_no IN
             <foreach item="item" index="index" collection="request.siteIds"
                    open="(" separator="," close=")">
                #{item}
             </foreach>
         </if>
         <if test = "request.conversionStatuses != null
                and !request.conversionStatuses.isEmpty()">
         AND
            data.sales_log_status IN
             <foreach item="item" index="index" collection="request.conversionStatuses"
                    open="(" separator="," close=")">
                #{item}
             </foreach>
         </if>
         <if test = "request.campaignIds != null and !request.campaignIds.isEmpty()">
         AND
            data.merchant_campaign_no IN
             <foreach item="item" index="index" collection="request.campaignIds"
                    open="(" separator="," close=")">
                #{item}
             </foreach>
         </if>
     */
    @Multiline String WHERE_RAW_CONVERSION_DATA_REPORT = "";

    /**
         <if test = "request.isPendingForMerchantPayment">
         AND EXISTS (
             SELECT
                0
             FROM
                campaign_closure cc
             WHERE
                cc.status = 1
             AND
                data.merchant_campaign_no = cc.campaign_id
             AND
             <![CDATA[
                TRUNC(data.confirmed_date, 'DD') >= TRUNC(cc.closed_from, 'DD')
             AND
                TRUNC(data.confirmed_date, 'DD') <= TRUNC(cc.closed_to, 'DD')
             ]]>
         )
         </if>
     */
    @Multiline String WHERE_IS_PENDING_FOR_MERCHANT_PAYMENT = "";

    /**
         ) data
         INNER JOIN
            partner_site ps
         ON
            ps.site_no = data.siteId
         INNER JOIN
            partner_account pa
         ON
            pa.account_no = ps.account_no
         INNER JOIN
            merchant_campaign mc
         ON
            mc.campaign_no = data.campaignId
         INNER JOIN
            merchant_account ma
         ON
            ma.account_no = mc.account_no
     */
    @Multiline String END_RAW_CONVERSION_REPORT = "";

    /**
         <if test = "request.countryCode == 'ID' or request.countryCode == 'MY'
            or request.countryCode == 'SG' or request.countryCode == 'PH'
            or request.countryCode == 'TW'
            or (request.merchantIds != null and !request.merchantIds.isEmpty())">
             AND EXISTS (
                 SELECT
                    0
                 FROM
                    merchant_campaign mc
                 INNER JOIN
                    merchant_account ma
                 ON
                    mc.account_no = ma.account_no
                 WHERE
                    mc.campaign_no = data.merchant_campaign_no
                    <if test = "request.countryCode != null">
                        AND
                            ma.country_code = #{request.countryCode}
                    </if>
                    <if test = "request.merchantIds != null
                        and !request.merchantIds.isEmpty()">
                        AND (ma.account_no IN
                        <foreach item="item" index="index"
                            collection="request.merchantIds" open="(" close=")">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 0">
                                        ) OR data.merchant_campaign_no IN (
                                    </when>
                                    <otherwise> , </otherwise>
                                </choose>
                            </if>
                            #{item}
                        </foreach>
                        )
                    </if>
         )
         </if>
     */
    @Multiline String EXISTS_MERCHANT = "";

    /**
        <if test = "request.countryCode == 'ID' or request.countryCode == 'MY'
            or request.countryCode == 'SG' or request.countryCode == 'PH'
            or request.countryCode == 'TW'
            or (request.publisherIds != null and !request.publisherIds.isEmpty())
            or (request.publisherType != null and request.publisherType.name() != 'UNSPECIFIED')">
            AND EXISTS (
                SELECT
                    0
                FROM
                    partner_account pa
                INNER JOIN
                    partner_site ps
                ON
                    pa.account_no = ps.account_no
                WHERE
                    ps.site_no = data.partner_site_no
                <if test = "request.publisherIds != null
                    and !request.publisherIds.isEmpty()">
                    AND
                        (pa.account_no IN
                        <foreach item="item" index="index"
                            collection="request.publisherIds" open="(" close=")">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 0">
                                        ) OR pa.account_no IN (
                                    </when>
                                    <otherwise> , </otherwise>
                                </choose>
                            </if>
                            #{item}
                        </foreach>
                        )
                </if>
                <if test = "request.publisherType != null
                    and request.publisherType.name() != 'UNSPECIFIED'">
                    AND
                        pa.is_global = #{request.publisherType.value}
                </if>
            )
        </if>
     */
    @Multiline String EXISTS_SITE = "";

    /**
         SELECT
             data.seq_no AS conversionId,
             data.sales_log_status AS status,
             data.verify AS verificationId,
             data.click_date AS clickTime,
             data.sales_date AS conversionTime,
             data.confirmed_date AS confirmationTime,
             data.total_price AS transactionAmount,
             data.discount AS discountAmount,
             data.reward_type AS rewardType,
             data.sales_reward + data.total_price_reward AS reward,
             data.at_commission,
             data.at_commission AS atCommission,
             data.agent_commission AS merchantAgentCommission,
             data.p_agent_commission AS publisherAgentCommission,
             data.sales_reward + data.total_price_reward + data.at_commission
                + data.agent_commission + data.p_agent_commission AS totalCommission,
             data.merchant_campaign_no AS campaignId,
             mc.campaign_name AS campaignName,
             data.result_id AS resultId,
             rtm.result_name AS resultName,
             data.goods_id AS productId,
             data.category_id AS productCategoryId,
             data.partner_site_no AS siteId,
             ps.site_name AS siteName,
             data.rank,
             data.banner_id AS creativeId,
             b.banner_type_id AS creativeType,
             cg.group_id AS creativeGroupId,
             cg.group_name AS creativeGroupName,
             CASE
                WHEN b.width IS NOT NULL AND b.height IS NOT NULL
                THEN b.width || 'x' || b.height
                ELSE null
             END AS creativeSize,
             data.postback_status AS postbackStatus,
             data.postback_error_count AS postbackErrorCount,
             data.latest_postback_time AS latestPostbackTime,
             data.postback_url AS postbackUrl,
             data.device_type AS deviceType,
             data.pointback_id AS pointbackId,
             data.click_referer AS clickReferer,
             data.click_url AS clickUrl,
             data.click_user_agent AS clickUserAgent,
             cpp.parameters AS parameters,
            data.customer_type AS customerType,
            CASE
                WHEN data.result_id in (3, 30)
                THEN data.sales_count
                ELSE 0
            END AS transactionItems,
            data.bonus_setting_id AS bonusSettingId,
            data.publisher_bonus AS publisherBonus,
            data.publisher_agent_bonus AS publisherAgentBonus,
            data.merchant_agent_bonus AS merchantAgentBonus,
            data.at_bonus AS atBonus
         FROM
            conversion_temp_ids cti
         INNER JOIN
            sales_log data
         ON
            cti.id = data.seq_no
         LEFT JOIN
            merchant_campaign mc
         ON
            data.merchant_campaign_no = mc.campaign_no
         LEFT JOIN
            partner_site ps
         ON
            data.partner_site_no = ps.site_no
         LEFT JOIN
            banner b
         ON
            b.banner_id = data.banner_id
         LEFT JOIN
            result_target_master rtm
         ON
            data.result_id = rtm.result_id
         LEFT JOIN
            creative_group cg
         ON
            cg.group_id = b.group_id
         AND
            cg.campaign_id = b.merchant_campaign_no
        LEFT JOIN (
            SELECT
                cp.internal_transaction_id,
                <choose>
                    <when test = "isTest">
                        <![CDATA[
                        GROUP_CONCAT(cp.param_name || '##e##' || cp.param_value
                        ORDER BY cp.param_name SEPARATOR '##a##') AS parameters
                        ]]>
                    </when>
                    <otherwise>
                        <![CDATA[
                        RTRIM(
                            XMLAGG(
                                XMLPARSE(CONTENT cp.param_name || '##e##' || cp.param_value || '##a##' WELLFORMED)
                                ORDER BY cp.param_name
                            ).getClobVal(),
                            '##a##'
                        ) AS parameters
                        ]]>
                    </otherwise>
                </choose>
            FROM
                click_parameters_temp cp
            GROUP BY
                cp.internal_transaction_id
        ) cpp ON cpp.internal_transaction_id = data.internal_transaction_id
         WHERE cti.row_num BETWEEN #{offset} AND #{limit}
     */
    @Multiline String NEW_SELECT_FULL_CONVERSION_REPORT = "";

    String INSERT_CONVERSION_DATA_BY_REQUEST_TO_TEMP_TABLE = START_TAG_SCRIPT
            + INSERT_CONVERSION_DATA_TO_TEMP_TABLE_START
            + SELECT_CONVERSION_DATA_FROM_TEMP_TABLE_START
            + WHERE_CONVERSIONS
            + AND_CAMPAIGN_IDS
            + AND_SITE_IDS
            + AND_CONVERSION_IDS
            + AND_CREATIVE_IDS
            + AND_DEVICE_TYPES
            + AND_PRODUCT_IDS
            + AND_RANKS
            + AND_RESULT_IDS
            + AND_VERIFIES
            + EXISTS_MERCHANT
            + EXISTS_SITE
            + END_TAG_SCRIPT;

    String SELECT_RAW_CONVERSION_DATA_REPORT = START_TAG_SCRIPT
            + SELECT_CONVERSION_START
            + SELECT_RAW_CONVERSION_DATA
            + WHERE_RAW_CONVERSION_DATA_REPORT
            + WHERE_IS_PENDING_FOR_MERCHANT_PAYMENT
            + END_RAW_CONVERSION_REPORT
            + OFFSET_FETCH
            + END_TAG_SCRIPT;

    String SELECT_PUBLISHER_CONVERSION_TRANSACTION_ID = START_TAG_SCRIPT
            + SELECT_PUBLISHER_TRANS_ID_DATA
            + WHERE_RAW_CONVERSION_DATA_REPORT
            + WHERE_IS_PENDING_FOR_MERCHANT_PAYMENT
            + END_TAG_SCRIPT;

    /**
     * Finds all conversion bonus report detail by the given request criteria.
     *
     * @param countryCode
     *          short code for country
     * @param siteIds
     *          the given site IDs
     * @param campaignIds
     *          the given campaign IDs
     * @param bonusSettingIds
     *          the given bonus setting IDs
     * @param statuses
     *          the given bonus statuses
     * @param fromDate
     *          the given start date
     * @param toDate
     *          the given end date
     * @param pageNumber
     *          number of the page
     * @param pageSize
     *          number of the size
     * @return The list of conversion bonus report detail data
     */
    @Select(START_TAG_SCRIPT + START_ROWNUM + CONVERSION_BONUS_REPORT_DETAILS
                    + FROM_SALES_LOG + CONDITION_BY_CONVERSION_DATE
                    + SEARCH_SALES_LOG_CONDITIONS + END_ROWNUM + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "id", javaType = long.class),
            @Arg(column = "bonusId", javaType = long.class),
            @Arg(column = "campaignId", javaType = long.class),
            @Arg(column = "siteId", javaType = long.class),
            @Arg(column = "confirmationTime", javaType = LocalDateTime.class),
            @Arg(column = "status", javaType = BonusStatus.class),
            @Arg(column = "atBonus", javaType = BigDecimal.class),
            @Arg(column = "publisherBonus", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentBonus", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentBonus", javaType = BigDecimal.class),
            @Arg(column = "atBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "publisherBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "bonusSettingId", javaType = long.class),
            @Arg(column = "createdOn", javaType = LocalDateTime.class),
            @Arg(column = "createdBy", javaType = String.class),
            @Arg(column = "conversionTime", javaType = LocalDateTime.class)})
    List<ConversionBonusReportDetail> findConversionBonusByConversionDate(
            @Param("countryCode") String countryCode, @Param("siteIds") Set<Long> siteIds,
            @Param("campaignIds") Set<Long> campaignIds,
            @Param("bonusSettingIds") Set<Long> bonusSettingIds,
            @Param("statuses") List<BonusStatus> statuses,
            @Param("fromDate") LocalDateTime fromDate,
            @Param("toDate") LocalDateTime toDate,
            @Param("pageNumber") int pageNumber,
            @Param("pageSize") int pageSize);

    /**
     * Finds all conversion bonus report detail by the given request criteria.
     *
     * @param countryCode
     *          short code for country
     * @param siteIds
     *          the given site IDs
     * @param campaignIds
     *          the given campaign IDs
     * @param bonusSettingIds
     *          the given bonus setting IDs
     * @param statuses
     *          the given bonus statuses
     * @param fromDate
     *          the given start date
     * @param toDate
     *          the given end date
     * @param pageNumber
     *          number of the page
     * @param pageSize
     *          number of the size
     * @return The list of conversion bonus report detail data
     */
    @Select(START_TAG_SCRIPT + START_ROWNUM + CONVERSION_BONUS_REPORT_DETAILS
                    + FROM_SALES_LOG + CONDITION_BY_CONFIRMATION_DATE
                    + SEARCH_SALES_LOG_CONDITIONS + END_ROWNUM + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "id", javaType = long.class),
            @Arg(column = "bonusId", javaType = long.class),
            @Arg(column = "campaignId", javaType = long.class),
            @Arg(column = "siteId", javaType = long.class),
            @Arg(column = "confirmationTime", javaType = LocalDateTime.class),
            @Arg(column = "status", javaType = BonusStatus.class),
            @Arg(column = "atBonus", javaType = BigDecimal.class),
            @Arg(column = "publisherBonus", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentBonus", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentBonus", javaType = BigDecimal.class),
            @Arg(column = "atBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "publisherBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "bonusSettingId", javaType = long.class),
            @Arg(column = "createdOn", javaType = LocalDateTime.class),
            @Arg(column = "createdBy", javaType = String.class),
            @Arg(column = "conversionTime", javaType = LocalDateTime.class) })
    List<ConversionBonusReportDetail> findConversionBonusByConfirmationDate(
            @Param("countryCode") String countryCode, @Param("siteIds") Set<Long> siteIds,
            @Param("campaignIds") Set<Long> campaignIds,
            @Param("bonusSettingIds") Set<Long> bonusSettingIds,
            @Param("statuses") List<BonusStatus> statuses,
            @Param("fromDate") LocalDateTime fromDate,
            @Param("toDate") LocalDateTime toDate,
            @Param("pageNumber") int pageNumber,
            @Param("pageSize") int pageSize);

    /**
     * Returns conversion report items by the given {@link ConversionReportExportMapperRequest}.
     *
     * @param offset
     *            offset of row
     * @param limit
     *           limit of result
     * @return conversion report items by the given {@link ConversionReportExportMapperRequest}
     */
    @Select(START_TAG_SCRIPT + NEW_SELECT_FULL_CONVERSION_REPORT + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "conversionId", javaType = long.class),
            @Arg(column = "status", javaType = ConversionStatus.class),
            @Arg(column = "verificationId", javaType = String.class),
            @Arg(column = "clickTime", javaType = LocalDateTime.class),
            @Arg(column = "conversionTime", javaType = LocalDateTime.class),
            @Arg(column = "confirmationTime", javaType = LocalDateTime.class),
            @Arg(column = "transactionAmount", javaType = BigDecimal.class),
            @Arg(column = "discountAmount", javaType = BigDecimal.class),
            @Arg(column = "rewardType", javaType = RewardType.class),
            @Arg(column = "reward", javaType = BigDecimal.class),
            @Arg(column = "atCommission", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentCommission", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentCommission", javaType = BigDecimal.class),
            @Arg(column = "totalCommission", javaType = BigDecimal.class),
            @Arg(column = "campaignId", javaType = long.class),
            @Arg(column = "campaignName", javaType = String.class),
            @Arg(column = "resultId", javaType = int.class),
            @Arg(column = "resultName", javaType = String.class),
            @Arg(column = "productId", javaType = String.class),
            @Arg(column = "productCategoryId", javaType = String.class),
            @Arg(column = "siteId", javaType = long.class),
            @Arg(column = "siteName", javaType = String.class),
            @Arg(column = "rank", javaType = Integer.class),
            @Arg(column = "creativeId", javaType = long.class),
            @Arg(column = "creativeType", javaType = CreativeType.class),
            @Arg(column = "creativeGroupId", javaType = Long.class),
            @Arg(column = "creativeGroupName", javaType = String.class),
            @Arg(column = "creativeSize", javaType = String.class),
            @Arg(column = "postbackStatus", javaType = PostbackStatus.class),
            @Arg(column = "postbackErrorCount", javaType = long.class),
            @Arg(column = "latestPostbackTime", javaType = LocalDateTime.class),
            @Arg(column = "postbackUrl", javaType = String.class),
            @Arg(column = "deviceType", javaType = DeviceType.class),
            @Arg(column = "pointbackId", javaType = String.class),
            @Arg(column = "clickReferer", javaType = String.class),
            @Arg(column = "clickUrl", javaType = String.class),
            @Arg(column = "clickUserAgent", javaType = String.class),
            @Arg(column = "parameters", javaType = String.class),
            @Arg(column = "customerType", javaType = String.class),
            @Arg(column = "transactionItems", javaType = int.class),
            @Arg(column = "bonusSettingId", javaType = long.class),
            @Arg(column = "atBonus", javaType = BigDecimal.class),
            @Arg(column = "publisherBonus", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentBonus", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentBonus", javaType = BigDecimal.class) })
    List<ConversionReport> findFullConversionReportFromTempTableBy(
            @Param("offset") long offset,
            @Param("limit") long limit,
            @Param("isTest") boolean isTest);

    /**
     * Inserts the conversion data to temp table by the given
     * {@link ConversionReportExportMapperRequest}.
     *
     * @param request
     *            DTO holding the search criteria
     * @param fromDate
     *            fromDate criteria
     * @param toDate
     *            toDate criteria
     * @return the number of inserted rows
     */
    @Insert(INSERT_CONVERSION_DATA_BY_REQUEST_TO_TEMP_TABLE)
    int insertConversionTempTable(
            @Param("request") ConversionReportExportMapperRequest request,
            @Param("fromDate") LocalDate fromDate,
            @Param("toDate") LocalDate toDate);

    /**
     * Returns {@link ConversionClickParameterRequest}s by the given {@link ConversionReportExportMapperRequest}.
     *
     * @param request
     *            DTO holding the search criteria
     * @param campaignClosureIds
     *            IDs by the given campaign closures
     * @param fromDate
     *            fromDate criteria
     * @param toDate
     *            toDate criteria
     * @return {@link ConversionClickParameterRequest}s by the given {@link ConversionReportExportMapperRequest}
     */
    @Select(SELECT_PUBLISHER_CONVERSION_TRANSACTION_ID)
    @ConstructorArgs({ @Arg(column = "internalTransactionId", javaType = String.class),
            @Arg(column = "campaignId", javaType = long.class) })
    Set<ConversionClickParameterRequest> findAllPublisherClickParameterRequestsBy(
            @Param("campaignClosureIds") List<Long> campaignClosureIds,
            @Param("fromDate") LocalDate fromDate,
            @Param("toDate") LocalDate toDate,
            @Param("request") ConversionReportExportMapperRequest request);

    /**
     * Returns raw conversion data by the given {@link ConversionReportExportMapperRequest}.
     *
     * @param request
     *            DTO holding the search criteria
     * @param currency
     *            the given currency
     * @param campaignClosureIds
     *            IDs by the given campaign closures
     * @param offset
     *            offset to paging
     * @param limit
     *            limit to paging
     * @param fromDate
     *            fromDate criteria
     * @param toDate
     *            toDate criteria
     * @return conversion transaction IDs by the given {@link ConversionReportExportMapperRequest}
     */
    @Select(SELECT_RAW_CONVERSION_DATA_REPORT)
    @ConstructorArgs({ @Arg(column = "conversionId", javaType = long.class),
            @Arg(column = "siteId", javaType = long.class),
            @Arg(column = "campaignId", javaType = long.class),
            @Arg(column = "creativeId", javaType = long.class),
            @Arg(column = "verificationId", javaType = String.class),
            @Arg(column = "internalTransactionId", javaType = String.class),
            @Arg(column = "sessionId", javaType = String.class),
            @Arg(column = "productId", javaType = String.class),
            @Arg(column = "productQuantity", javaType = Integer.class),
            @Arg(column = "productCategoryId", javaType = String.class),
            @Arg(column = "merchantCountryCode", javaType = String.class),
            @Arg(column = "publisherCountryCode", javaType = String.class),
            @Arg(column = "customerType", javaType = String.class),
            @Arg(column = "clickIpAddress", javaType = String.class),
            @Arg(column = "language", javaType = String.class),
            @Arg(column = "clickUserAgent", javaType = String.class),
            @Arg(column = "deviceType", javaType = DeviceType.class),
            @Arg(column = "clickReferer", javaType = String.class),
            @Arg(column = "postbackErrorCount", javaType = int.class),
            @Arg(column = "postbackUrl", javaType = String.class),
            @Arg(column = "status", javaType = ConversionStatus.class),
            @Arg(column = "productUnitPrice", javaType = BigDecimal.class),
            @Arg(column = "reward", javaType = BigDecimal.class),
            @Arg(column = "transactionAmount", javaType = BigDecimal.class),
            @Arg(column = "clickTime", javaType = LocalDateTime.class),
            @Arg(column = "conversionTime", javaType = LocalDateTime.class),
            @Arg(column = "confirmationTime", javaType = LocalDateTime.class),
            @Arg(column = "postbackStatus", javaType = PostbackStatus.class),
            @Arg(column = "latestPostbackTime", javaType = LocalDateTime.class)})
    List<PublisherConversionLogItem> findPublisherConversionReportData(
            @Param("currency") String currency,
            @Param("campaignClosureIds") List<Long> campaignClosureIds,
            @Param("offset") long offset,
            @Param("limit") long limit,
            @Param("fromDate") LocalDate fromDate,
            @Param("toDate") LocalDate toDate,
            @Param("request") ConversionReportExportMapperRequest request);

    /**
            SUM(data.transactionAmount) AS transactionAmount,
            SUM(data.salesReward) AS salesReward,
            SUM(data.transactionAmountReward) AS transactionAmountReward,
            SUM(data.atCommission) AS atCommission,
            SUM(data.merchantAgentCommission) AS merchantAgentCommission,
            SUM(data.publisherAgentCommission) AS publisherAgentCommission,
            SUM(data.totalCommission) AS totalCommission,
            SUM(data.transactionItems) AS transactionItems,
            SUM(data.conversionCount) AS conversionCount,
            SUM(data.pendingConversionCount) AS pendingConversionCount,
            SUM(data.approvedConversionCount) AS approvedConversionCount,
            SUM(data.rejectedConversionCount) AS rejectedConversionCount,
            data.campaignId AS campaignId
        FROM (
     */
    @Multiline String SELECT_CAMPAIGN_REPORT_CONVERSION_ITEM = "";

    /**
         <foreach item="materializedViewsName" index="index"
            collection="materializedViewsNames" separator=" UNION ALL ">
     */
    @Multiline String START_MATERIALIZED_VIEW_FOREACH = "";

    /**
            NVL(SUM(data.total_price), 0) AS transactionAmount,
            NVL(SUM(data.sales_reward), 0) AS salesReward,
            NVL(SUM(data.total_price_reward), 0) AS transactionAmountReward,
            NVL(SUM(data.at_commission), 0) AS atCommission,
            NVL(SUM(data.agent_commission), 0) AS merchantAgentCommission,
            NVL(SUM(data.p_agent_commission), 0) AS publisherAgentCommission,
            NVL(SUM(data.sales_reward) + SUM(data.total_price_reward)
                + SUM(data.at_commission) + SUM(data.agent_commission)
                + SUM(data.p_agent_commission), 0) AS totalCommission,
            SUM(CASE WHEN result_id IN (3, 30) THEN sales_count ELSE 0 END)
                AS transactionItems,
            COUNT(DISTINCT CASE WHEN sales_log_status = 0
                THEN internal_transaction_id END) AS pendingConversionCount,
            COUNT(DISTINCT CASE WHEN sales_log_status = 1
                THEN internal_transaction_id END) AS approvedConversionCount,
            COUNT(DISTINCT CASE WHEN sales_log_status = 2
                THEN internal_transaction_id END) AS rejectedConversionCount,
            COUNT(DISTINCT CASE WHEN sales_log_status IN (0, 1, 2)
                THEN internal_transaction_id END) AS conversionCount,
            data.merchant_campaign_no AS campaignId
        FROM
            ${materializedViewsName} data
     */
    @Multiline String SELECT_CONVERSIONS = "";

    /**
        WHERE
            <if test = "request.periodBase.name() == 'CONVERSION_DATE'">
            <![CDATA[
                sales_date >= #{request.fromDate}
            AND
                sales_date < #{request.toDate}
            ]]>
            </if>
            <if test = "request.periodBase.name() == 'CONFIRMATION_DATE'">
            <![CDATA[
                confirmed_date >= #{request.fromDate}
            AND
                confirmed_date < #{request.toDate}
            ]]>
            </if>
     */
    @Multiline String WHERE_CONVERSIONS_REPORT_DATA = "";

    /**
        <if test = "campaignIds != null and !campaignIds.isEmpty()">
        AND (data.merchant_campaign_no IN
            <foreach item="item" index="index" collection="campaignIds"
                open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 0">
                            ) OR data.merchant_campaign_no IN (
                        </when>
                        <otherwise> , </otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
            )
        </if>
     */
    @Multiline String WHERE_CAMPAIGN_IDS = "";

    /**
        <if test = "request.isPendingForMerchantPayment
            and request.conversionStatuses != null
            and !request.conversionStatuses.isEmpty()">
        AND
            (( data.sales_log_status IN
            <foreach item="item" index="index" collection="request.conversionStatuses"
                open="(" separator="," close=")">
                #{item}
            </foreach>
            )
            <if test = "campaignClosurePeriods != null
                and !campaignClosurePeriods.isEmpty()">
            OR (
                <foreach item="item" index="index" collection="campaignClosurePeriods"
                    open="(" separator="OR" close=")">
                (
                <![CDATA[
                    data.confirmed_date >= #{item.fromDate}
                AND
                    data.confirmed_date < #{item.toDate}
                ]]>
                AND
                    data.merchant_campaign_no = #{item.campaignId}
                )
                </foreach>
            )
            </if>
            )
        </if>
        <if test = "request.isPendingForMerchantPayment
            and request.conversionStatuses == null
            and campaignClosurePeriods != null
            and !campaignClosurePeriods.isEmpty()">
            AND
                data.sales_log_status = 1
            AND
            <foreach item="item" index="index" collection="campaignClosurePeriods"
                open="(" separator="OR" close=")">
            (
            <![CDATA[
                data.confirmed_date >= #{item.fromDate}
            AND
                data.confirmed_date < #{item.toDate}
            ]]>
            AND
                data.merchant_campaign_no = #{item.campaignId}
            )
            </foreach>
        </if>
        <if test = "request.isPendingForMerchantPayment == false
            and request.conversionStatuses != null
            and !request.conversionStatuses.isEmpty()">
        AND
            data.sales_log_status IN
            <foreach item="item" index="index" collection="request.conversionStatuses"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
     */
    @Multiline String WHERE_CLOSURE_PERIODS = "";

    /**
        GROUP BY
            merchant_campaign_no
     */
    @Multiline String GROUP_BY_CAMPAIGN_ID = "";

    /**
        ) data
     */
    @Multiline String END_DATA_REPORT = "";

    /**
        INNER JOIN
            merchant_campaign mc
        ON
            data.campaignId = mc.campaign_no
        INNER JOIN
            merchant_account ma
        ON
            mc.account_no = ma.account_no
        WHERE
            ma.country_code = #{request.countryCode}
        GROUP BY
            data.campaignId
     */
    @Multiline String JOIN_MERCHANT_CAMPAIGN = "";

    String SELECT_CAMPAIGN_REPORT_CONVERSION = START_TAG_SCRIPT
            + SELECT
            + SELECT_CAMPAIGN_REPORT_CONVERSION_ITEM
            + START_MATERIALIZED_VIEW_FOREACH
            + SELECT
            + PARALLEL
            + SELECT_CONVERSIONS
            + WHERE_CONVERSIONS_REPORT_DATA
            + WHERE_CAMPAIGN_IDS
            + AND_SITE_IDS
            + WHERE_CLOSURE_PERIODS
            + GROUP_BY_CAMPAIGN_ID
            + END_FOREACH
            + END_DATA_REPORT
            + JOIN_MERCHANT_CAMPAIGN
            + END_TAG_SCRIPT;

    /**
     * Finds all conversion campaign report detail by the given request criteria.
     *
     * @param materializedViewsNames
     *              the given mv names
     * @param request
     *              the given request {@link CampaignReportExportRequest}
     * @param campaignIds
     *              the given campaign IDs
     * @param campaignClosurePeriods
     *              the given request campaign closure periods
     * @return conversion campaign report detail by the given request criteria
     */
    @Select(SELECT_CAMPAIGN_REPORT_CONVERSION)
    @ConstructorArgs({ @Arg(column = "conversionCount", javaType = long.class),
            @Arg(column = "salesReward", javaType = BigDecimal.class),
            @Arg(column = "transactionAmountReward", javaType = BigDecimal.class),
            @Arg(column = "transactionAmount", javaType = BigDecimal.class),
            @Arg(column = "transactionItems", javaType = int.class),
            @Arg(column = "atCommission", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentCommission", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentCommission", javaType = BigDecimal.class),
            @Arg(column = "totalCommission", javaType = BigDecimal.class),
            @Arg(column = "pendingConversionCount", javaType = long.class),
            @Arg(column = "rejectedConversionCount", javaType = long.class),
            @Arg(column = "approvedConversionCount", javaType = long.class),
            @Arg(column = "campaignId", javaType = long.class) })
    List<CampaignReportConversionItem> findCampaignReportData(
            @Param("materializedViewsNames") List<String> materializedViewsNames,
            @Param("request") CampaignReportExportRequest request,
            @Param("campaignIds") Set<Long> campaignIds,
            @Param("campaignClosurePeriods")
            List<CampaignClosurePeriod> campaignClosurePeriods);
}
