/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
/**
 * The MIT License
 *
 *  Copyright (c) 2016, <PERSON><PERSON><PERSON> (<EMAIL>)
 *
 *  Permission is hereby granted, free of charge, to any person obtaining a copy
 *  of this software and associated documentation files (the "Software"), to deal
 *  in the Software without restriction, including without limitation the rights
 *  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the Software is
 *  furnished to do so, subject to the following conditions:
 *
 *  The above copyright notice and this permission notice shall be included in
 *  all copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 *  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 *  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 *  THE SOFTWARE.
 */

package org.easybatch.core.job;

import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.LogManager;
import java.util.logging.Logger;

import org.apache.ibatis.session.SqlSessionManager;

import org.easybatch.core.filter.RecordFilter;
import org.easybatch.core.listener.BatchListener;
import org.easybatch.core.listener.JobListener;
import org.easybatch.core.listener.PipelineListener;
import org.easybatch.core.listener.RecordReaderListener;
import org.easybatch.core.listener.RecordWriterListener;
import org.easybatch.core.mapper.RecordMapper;
import org.easybatch.core.marshaller.RecordMarshaller;
import org.easybatch.core.processor.RecordProcessor;
import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.validator.RecordValidator;
import org.easybatch.core.writer.RecordWriter;

import static org.easybatch.core.util.Utils.checkNotNull;

/**
 * Batch job builder. This is the main entry point to configure batch jobs.
 * <p>
 * Modified aNewJob() method to {@link #newJob()} in order to make it possible
 * to inject transaction manager.
 * </p>
 *
 * <AUTHOR> Ben Hassine (<EMAIL>)
 * <AUTHOR> VAN NGUYEN
 */
public final class JobBuilder {

    private static final Logger LOGGER = Logger.getLogger(BatchJob.class.getName());

    private BatchJob job;

    private JobParameters parameters;

    static {
        try {
            if (System.getProperty("java.util.logging.config.file") == null
                    && System.getProperty("java.util.logging.config.class") == null) {
                LogManager.getLogManager().readConfiguration(
                        BatchJob.class.getResourceAsStream("/logging.properties"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.WARNING, "Unable to load logging configuration file", e);
        }
    }

    /**
     * Create a new {@link JobBuilder}.
     *
     * @param sqlSessionManager
     *            {@link SqlSessionManager} transaction manager.
     */
    public JobBuilder(SqlSessionManager sqlSessionManager) {
        parameters = new JobParameters();
        job = new BatchJob(parameters, sqlSessionManager);
    }

    /**
     * Create a new {@link JobBuilder}.
     *
     * @param sqlSessionManager session manager.
     * @return a new job builder.
     */
    public static JobBuilder newJob(SqlSessionManager sqlSessionManager) {
        return new JobBuilder(sqlSessionManager);
    }

    /**
     * Set the job name.
     *
     * @param name the job name
     * @return the job builder
     */
    public JobBuilder named(final String name) {
        checkNotNull(name, "job name");
        job.setName(name);
        return this;
    }

    public JobBuilder setSession(SqlSessionManager sqlSessionManager) {

        return this;
    }

    /**
     * Register a record reader.
     *
     * @param recordReader the record reader to register
     * @return the job builder
     */
    public JobBuilder reader(final RecordReader recordReader) {
        checkNotNull(recordReader, "record reader");
        job.setRecordReader(recordReader);
        return this;
    }

    /**
     * Register a record filter.
     *
     * @param recordFilter the record filter to register
     * @return the job builder
     */
    @SuppressWarnings("rawtypes")
    public JobBuilder filter(final RecordFilter recordFilter) {
        checkNotNull(recordFilter, "record filter");
        job.addRecordProcessor(recordFilter);
        return this;
    }

    /**
     * Register a record mapper.
     *
     * @param recordMapper the record mapper to register
     * @return the job builder
     */
    @SuppressWarnings("rawtypes")
    public JobBuilder mapper(final RecordMapper recordMapper) {
        checkNotNull(recordMapper, "record mapper");
        job.addRecordProcessor(recordMapper);
        return this;
    }

    /**
     * Register a record validator.
     *
     * @param recordValidator the record validator to register
     * @return the job builder
     */
    @SuppressWarnings("rawtypes")
    public JobBuilder validator(final RecordValidator recordValidator) {
        checkNotNull(recordValidator, "record validator");
        job.addRecordProcessor(recordValidator);
        return this;
    }

    /**
     * Register a record processor.
     *
     * @param recordProcessor the record processor to register
     * @return the job builder
     */
    @SuppressWarnings("rawtypes")
    public JobBuilder processor(final RecordProcessor recordProcessor) {
        checkNotNull(recordProcessor, "record processor");
        job.addRecordProcessor(recordProcessor);
        return this;
    }

    /**
     * Register a record marshaller.
     *
     * @param recordMarshaller the record marshaller to register
     * @return the job builder
     */
    @SuppressWarnings("rawtypes")
    public JobBuilder marshaller(final RecordMarshaller recordMarshaller) {
        checkNotNull(recordMarshaller, "record marshaller");
        job.addRecordProcessor(recordMarshaller);
        return this;
    }

    /**
     * Register a record writer.
     *
     * @param recordWriter the record writer to register
     * @return the job builder
     */
    public JobBuilder writer(final RecordWriter recordWriter) {
        checkNotNull(recordWriter, "record writer");
        job.setRecordWriter(recordWriter);
        return this;
    }

    /**
     * Set a threshold for errors. The job will be aborted if the threshold is exceeded.
     *
     * @param errorThreshold the error threshold
     * @return the job builder
     */
    public JobBuilder errorThreshold(final long errorThreshold) {
        if (errorThreshold < 1) {
            throw new IllegalArgumentException("error threshold must be >= 1");
        }
        parameters.setErrorThreshold(errorThreshold);
        return this;
    }

    /**
     * Activate JMX monitoring.
     *
     * @param jmx true to enable jmx monitoring
     * @return the job builder
     */
    public JobBuilder enableJmx(final boolean jmx) {
        parameters.setJmxMonitoring(jmx);
        return this;
    }

    /**
     * Set the batch size.
     *
     * @param batchSize the batch size
     * @return the job builder
     */
    public JobBuilder batchSize(final int batchSize) {
        if (batchSize < 1) {
            throw new IllegalArgumentException("Batch size must be >= 1");
        }
        parameters.setBatchSize(batchSize);
        return this;
    }

    /**
     * Register a job listener.
     * See {@link JobListener} for available callback methods.
     *
     * @param jobListener The job listener to add.
     * @return the job builder
     */
    public JobBuilder jobListener(final JobListener jobListener) {
        checkNotNull(jobListener, "job listener");
        job.addJobListener(jobListener);
        return this;
    }

    /**
     * Register a batch listener.
     * See {@link BatchListener} for available callback methods.
     *
     * @param batchListener The batch listener to add.
     * @return the job builder
     */
    public JobBuilder batchListener(final BatchListener batchListener) {
        checkNotNull(batchListener, "batch listener");
        job.addBatchListener(batchListener);
        return this;
    }

    /**
     * Register a record reader listener.
     * See {@link RecordReaderListener} for available callback methods.
     *
     * @param recordReaderListener The record reader listener to add.
     * @return the job builder
     */
    public JobBuilder readerListener(final RecordReaderListener recordReaderListener) {
        checkNotNull(recordReaderListener, "record reader listener");
        job.addRecordReaderListener(recordReaderListener);
        return this;
    }

    /**
     * Register a pipeline listener.
     * See {@link PipelineListener} for available callback methods.
     *
     * @param pipelineListener The pipeline listener to add.
     * @return the job builder
     */
    public JobBuilder pipelineListener(final PipelineListener pipelineListener) {
        checkNotNull(pipelineListener, "pipeline listener");
        job.addPipelineListener(pipelineListener);
        return this;
    }

    /**
     * Register a record writer listener.
     * See {@link RecordWriterListener} for available callback methods.
     *
     * @param recordWriterListener The record writer listener to register.
     * @return the job builder
     */
    public JobBuilder writerListener(final RecordWriterListener recordWriterListener) {
        checkNotNull(recordWriterListener, "record writer listener");
        job.addRecordWriterListener(recordWriterListener);
        return this;
    }

    /**
     * Build a batch job instance.
     *
     * @return a batch job instance
     */
    public Job build() {
        return job;
    }

}
