/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.model.CampaignAdvDetails;
import jp.ne.interspace.taekkyeon.model.CampaignAdvType;
import jp.ne.interspace.taekkyeon.model.ClickConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionsWithClickIdDetails;
import jp.ne.interspace.taekkyeon.model.CurrencyRate;
import jp.ne.interspace.taekkyeon.model.Merchant;
import jp.ne.interspace.taekkyeon.model.MerchantIntegrationHistoryInsertRequest;
import jp.ne.interspace.taekkyeon.model.ShopeeConversionReport;
import jp.ne.interspace.taekkyeon.model.ShopeeItem;
import jp.ne.interspace.taekkyeon.model.ShopeeNode;
import jp.ne.interspace.taekkyeon.model.ShopeeOrder;
import jp.ne.interspace.taekkyeon.model.ShopeePageInfo;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ApiKeyNameMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CurrencyMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.MerchantIntegrationHistoryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.secretsmanager.SecretsManagerClient;

import static java.math.BigDecimal.ZERO;
import static java.time.ZonedDateTime.of;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.model.CampaignAdvType.BRAND;
import static jp.ne.interspace.taekkyeon.model.CampaignAdvType.KOL;
import static jp.ne.interspace.taekkyeon.model.CampaignAdvType.REGULAR;
import static jp.ne.interspace.taekkyeon.model.Merchant.SHOPEE;
import static org.apache.http.HttpHeaders.AUTHORIZATION;
import static org.apache.http.HttpHeaders.CONTENT_TYPE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link AbstractShopeeService}.
 *
 * <AUTHOR> Shin
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class AbstractShopeeServiceTest {

    private static final Long BRAND_CAMPAIGN_ID = 789L;
    private static final Long DEFAULT_CAMPAIGN_ID = 0L;
    private static final Long PARENT_CAMPAIGN_ID = 456L;
    private static final Long SUB_CAMPAIGN_ID = 456L;
    private static final ZoneId ZONE_ID = ZoneId.of("Asia/Singapore");
    private static final String SECRET_VALUE = "{\"appId\":\"secret\"}";
    private static final String SECRET_NAME = "secretName";
    private static final String APP_ID = "appId";
    private static final String SECRET = "secret";
    private static final long PURCHASE_TIME_START = 10L;
    private static final long PURCHASE_TIME_END = 20L;
    private static final String RAW_FORMAT = "{\"query\":\"{conversionReport(limit:500, purchaseTimeStart:%d, purchaseTimeEnd:%d%s, buyerType:ALL){nodes {checkoutId grossCommission purchaseTime cappedCommission totalBrandCommission estimatedTotalCommission utmContent referrer buyerType device orders{orderId shopType items {itemId shopId qty attributionType itemPrice actualAmount itemCommission grossBrandCommission itemSellerCommission modelId globalCategoryLv1Name globalCategoryLv2Name globalCategoryLv3Name}}}, pageInfo{limit hasNextPage scrollId}  }} \"}";
    private static final ZonedDateTime REQUEST_START_TIME = ZonedDateTime.of(2023, 10, 5,
            0, 0, 0, 0, ZoneId.of("Asia/Ho_Chi_Minh"));
    private static final ZonedDateTime REQUEST_END_TIME = ZonedDateTime.of(2023, 10, 5, 0,
            0, 1, 0, ZoneId.of("Asia/Ho_Chi_Minh"));

    private static final ZonedDateTime CONVERSION_TIME = ZonedDateTime.of(2023, 11, 5, 0,
            0, 1, 0, ZoneId.of("Asia/Ho_Chi_Minh"));
    private static final Duration DURATION_TIME = Duration.between(REQUEST_START_TIME, REQUEST_END_TIME);

    private static final String MCN_ID_DEFAULT = "fec8d47d412bcbeece3d9128ae855a7a";
    public static final String FAIL_TO_INSERT_DATA = "Fail to insert data - {}";
    private static final ZonedDateTime TIME1 = ZonedDateTime.of(2023, 7, 17, 23, 58, 20,
            0, ZoneId.of("Asia/Jakarta"));
    private static final BigDecimal GROSS_BRAND_COMMISSION = new BigDecimal("20");
    private static final String CLICK_ID = "clickId";
    private static final String CHECKOUT_ID = "checkoutId";
    private static final String CONVERSION_ID = "conversionId";
    private static final int RESULT_ID = 3;
    private static final BigDecimal ITEM_PRICE = new BigDecimal("100");
    private static final BigDecimal ITEM_COMMISSION = new BigDecimal("10");
    private static final BigDecimal ITEM_SELLER_COMMISSION = new BigDecimal("12.3");
    private static final String MODEL_ID = "modelId";
    private static final String GLOBAL_CATEGORY_LV1_NAME = "category, name";
    private static final String ITEM_ID = "id";
    private static final Merchant SHOPEE_MERCHANT = SHOPEE;
    private static final long CAMPAIGN_ID = 3L;
    private static final long CAMPAIGN_BAND_ID = 33L;
    private static final YearMonth CONVERSION_MONTH = YearMonth.of(2023, 7);
    private static final BigDecimal UNIT_PRICE = BigDecimal.valueOf(1000);
    private static final String CURRENCY = "USD";
    private static final BigDecimal CURRENCY_RATE = BigDecimal.valueOf(1.2);
    private static final BigDecimal GROSS_COMMISSION = new BigDecimal("400");
    private static final long PURCHASE_TIME2 = 1589613100;
    private static final BigDecimal CAPPED_COMMISSION = new BigDecimal("410");
    private static final BigDecimal TOTAL_BRAND_COMMISSION = new BigDecimal("420");
    private static final BigDecimal ESTIMATED_TOTAL_COMMISSION = new BigDecimal("430");
    private static final String BUYER_TYPE = "EXISTING";
    private static final String UTM_CONTENT = "12345-RKVALUEQQQ-url";
    private static final String ERROR_MESSAGE_CURRENCY_RATE = "CURRENCY_RATE_ERROR: Invalid or non-existent currency conversion unit. \nTargetMonth: 2023-07 \nCampaignId : 3 \nCurrency: USD";
    private static final String ERROR_MESSAGE_FORMAT = "CURRENCY_RATE_ERROR: Invalid or non-existent currency conversion unit. \nTargetMonth: %s \nCampaignId : %s \nCurrency: %s";
    private static final String ORDERED_IN_SAME_SHOP = "ORDERED_IN_SAME_SHOP";
    private static final String ORDERED_IN_DIFFERENT_SHOP = "ORDERED_IN_DIFFERENT_SHOP";
    private static final ZonedDateTime AFTER_DATE_TIME = of(2024, 8, 31, 23, 59, 59, 0, ZONE_ID);
    private static final String SHOP_ID = "shopId";
    private static final String XTRA_COMM = "XTRAComm";
    private static final String SHOPEE_COMM = "ShopeeComm";
    private static final String GLOBAL_CATEGORY_LV_3_NAME = "globalCategoryLv3Name";
    private static final ZonedDateTime TEST_CONVERSION_TIME = ZonedDateTime.now();
    private static final String TEST_IDENTIFIER = "identifier123";
    private static final int TEST_RESULT_ID = 3;
    private static final String TEST_CUSTOMER_TYPE = "NEW";
    private static final String TEST_CATEGORY_ID = "electronics";
    private static final String TEST_PRODUCT_ID = "product123";
    private static final BigDecimal TEST_ACTUAL_AMOUNT = new BigDecimal("100.00");
    private static final String TEST_CLICK_ID = "click123";
    private static final String TEST_SHOP_ID = "shop123";

    private static final Long TEST_SUB_CAMPAIGN_ID = 456L;
    private static final Long TEST_KOL_SUB_CAMPAIGN_ID = 789L;
    private static final Long TEST_BRAND_SUB_CAMPAIGN_ID = 101L;
    private static final Long TEST_MAIN_CAMPAIGN_ID = 123L;
    private static final String TEST_BRAND_COMMISSION_CATEGORY = "Brand Commission";
    private static final String TEST_ITEM_ID = "itemId";
    private static final String TEST_MODEL_ID = "modelId";
    private static final String TEST_CATEGORY_NAME = "category";
    private static final String TEST_SUBCATEGORY_NAME = "subcategory";
    private static final String TEST_ORDER_ID = "orderId";
    private static final String TEST_SHOP_TYPE = "shopType";
    private static final BigDecimal TEST_ITEM_SELLER_COMMISSION = new BigDecimal("15");
    private static final BigDecimal TEST_GROSS_BRAND_COMMISSION = new BigDecimal("20");
    private static final BigDecimal TEST_SUB_CONVERSION_AMOUNT = new BigDecimal("100");
    private static final String TEST_EXPECTED_PRODUCT_ID = "orderId_shopId_itemId_modelId_subcategory_XTRAComm";

    private static final String INDONESIA_COUNTRY_CODE = "ID";
    private static final String THAILAND_COUNTRY_CODE = "TH";
    private static final String PHILIPPINES_COUNTRY_CODE = "PH";
    private static final String MALAYSIA_COUNTRY_CODE = "MY";
    private static final String SINGAPORE_COUNTRY_CODE = "SG";
    private static final String TAIWAN_COUNTRY_CODE = "TW";

    private static final String BONUS_SUFFIX = "-bonus";
    private static final String XTRA_COMM_SUFFIX = "-XTRAComm";
    private static final String XTRA_COMM_KEYWORD = "XTRAComm";

    @InjectMocks @Spy
    private AbstractShopeeService underTest = new IndonesiaShopeeService();

    @Mock
    private JsonSerializerService jsonSerializerService;

    @Mock
    private CampaignMapper campaignMapper;

    @Mock
    private CurrencyMapper currencyMapper;

    @Mock
    private ApiKeyNameMapper apiKeyNameMapper;

    @Mock
    private SlackService slackService;

    @Mock
    private MerchantIntegrationHistoryMapper merchantIntegrationHistoryMapper;

    @Mock
    private Logger logger;

    @Test
    public void testIsConversionUniqueShouldReturnTrueWhenIsDuplicateConversionCheckDisabled()
            throws Exception {
        // given
        String key = "key";
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();

        // when
        boolean actual = underTest.isConversionUnique(key);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsConversionUniqueShouldReturnTrueWhenConversionIsDuplicate()
            throws Exception {
        // given
        String key = "key";
        doReturn(true).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(false).when(underTest).isDuplicateConversion(key);

        // when
        boolean actual = underTest.isConversionUnique(key);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsConversionUniqueShouldReturnFalseWhenConversionIsNotUnique()
            throws Exception {
        // given
        String key = "key";
        doReturn(true).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(true).when(underTest).isDuplicateConversion(key);

        // when
        boolean actual = underTest.isConversionUnique(key);

        // then
        assertFalse(actual);
    }

    @Test
    public void testHasNextShouldReturnFalseWhenEmptyTimesEqualMaxRetryCount() {
        // given
        doReturn(20).when(underTest).getEmptyTimes();

        // when
        boolean actual = underTest.hasNext();

        // then
        assertFalse(actual);
    }

    @Test
    public void testHasNextShouldReturnTrueWhenScrollIdIsNotEmptyAndRunTimesLessThanMaxRetryCount() {
        // given
        doReturn("abc").when(underTest).getScrollId();

        // when
        boolean actual = underTest.hasNext();

        // then
        assertTrue(actual);
    }

    @Test
    public void testHasNextShouldReturnFalseWhenScrollIdIsNotEmptyAndRunTimesGreaterThanMaxRetryCount() {
        // given
        doReturn("abc").when(underTest).getScrollId();
        doReturn(20).when(underTest).getEmptyTimes();

        // when
        boolean actual = underTest.hasNext();

        // then
        assertFalse(actual);
    }

    @Test
    public void testCreateConversionsWithClickIdDetailsByShouldGroupByMainCampaignWhenOnlyMainConversions() {
        // given
        ConversionRegistrationDetails mainConversion = new ConversionRegistrationDetails(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_ACTUAL_AMOUNT,
                TEST_CLICK_ID);
        List<ConversionRegistrationDetails> details = asList(mainConversion);

        long mainCampaignId = 123L;
        doReturn(mainCampaignId).when(underTest).getCampaignId();
        LocalDate currentDate = LocalDate.of(2023, 10, 15);
        doReturn(currentDate).when(underTest).getCurrentLocalDate();

        // when
        List<ConversionsWithClickIdDetails> actual = underTest
                .createConversionsWithClickIdDetailsBy(details);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());

        ConversionsWithClickIdDetails result = actual.get(0);
        assertEquals(mainCampaignId, result.getCampaignId());
        assertEquals(0L, result.getClickFromCampaignId());
        assertEquals(currentDate, result.getConfirmationDate());
        assertEquals(1, result.getConversions().size());
        assertEquals(mainConversion, result.getConversions().get(0));
    }

    @Test
    public void testCreateConversionsWithClickIdDetailsByShouldGroupBySubCampaignWhenOnlySubConversions() {
        // given
        Long subCampaignId = 456L;
        ClickConversionRegistrationDetails subConversion =
                new ClickConversionRegistrationDetails(TEST_CONVERSION_TIME,
                        TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                        TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID,
                        TEST_ACTUAL_AMOUNT, TEST_CLICK_ID, subCampaignId);
        List<ConversionRegistrationDetails> details = asList(subConversion);

        long mainCampaignId = 123L;
        doReturn(mainCampaignId).when(underTest).getCampaignId();
        String subCampaignName = "Sub Campaign";
        when(campaignMapper.findCampaignNameBy(subCampaignId))
                .thenReturn(subCampaignName);
        LocalDate currentDate = LocalDate.of(2023, 10, 15);
        doReturn(currentDate).when(underTest).getCurrentLocalDate();

        // when
        List<ConversionsWithClickIdDetails> actual = underTest
                .createConversionsWithClickIdDetailsBy(details);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());

        ConversionsWithClickIdDetails result = actual.get(0);
        assertEquals(subCampaignId.longValue(), result.getCampaignId());
        assertEquals(mainCampaignId, result.getClickFromCampaignId());
        assertEquals(subCampaignName, result.getCampaignName());
        assertEquals(currentDate, result.getConfirmationDate());
        assertEquals(1, result.getConversions().size());

        ConversionRegistrationDetails convertedConversion = result.getConversions().get(0);
        assertFalse(convertedConversion instanceof ClickConversionRegistrationDetails);
        assertEquals(TEST_CONVERSION_TIME, convertedConversion.getConversionTime());
        assertEquals(TEST_IDENTIFIER, convertedConversion.getTransactionId());
        assertEquals(TEST_RESULT_ID, convertedConversion.getResultId());
    }

    @Test
    public void testCreateConversionsWithClickIdDetailsByShouldGroupByBothCampaignsWhenMixedConversions() {
        // given
        ConversionRegistrationDetails mainConversion = new ConversionRegistrationDetails(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_ACTUAL_AMOUNT,
                TEST_CLICK_ID);

        Long subCampaignId = 456L;
        ClickConversionRegistrationDetails subConversion =
                new ClickConversionRegistrationDetails(TEST_CONVERSION_TIME,
                        TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                        TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID,
                        TEST_ACTUAL_AMOUNT, TEST_CLICK_ID, subCampaignId);

        List<ConversionRegistrationDetails> details = Arrays.asList(mainConversion,
                subConversion);

        long mainCampaignId = 123L;
        doReturn(mainCampaignId).when(underTest).getCampaignId();
        String subCampaignName = "Sub Campaign";
        when(campaignMapper.findCampaignNameBy(subCampaignId)).thenReturn(
                subCampaignName);
        LocalDate currentDate = LocalDate.of(2023, 10, 15);
        doReturn(currentDate).when(underTest).getCurrentLocalDate();

        // when
        List<ConversionsWithClickIdDetails> actual = underTest
                .createConversionsWithClickIdDetailsBy(details);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());

        ConversionsWithClickIdDetails mainGroup = actual.stream()
                .filter(group -> group.getCampaignId() == mainCampaignId).findFirst()
                .orElse(null);
        assertNotNull(mainGroup);
        assertEquals(0L, mainGroup.getClickFromCampaignId());
        assertEquals(1, mainGroup.getConversions().size());

        ConversionsWithClickIdDetails subGroup = actual.stream()
                .filter(group -> group.getCampaignId() == subCampaignId).findFirst()
                .orElse(null);
        assertNotNull(subGroup);
        assertEquals(mainCampaignId, subGroup.getClickFromCampaignId());
        assertEquals(subCampaignName, subGroup.getCampaignName());
        assertEquals(1, subGroup.getConversions().size());
    }

    @Test
    public void testCreateConversionsWithClickIdDetailsByShouldUseDefaultClickFromCampaignIdWhenSubCampaignIdEqualsMainCampaignId() {
        // given
        Long mainCampaignId = 123L;
        ClickConversionRegistrationDetails subConversion =
                new ClickConversionRegistrationDetails(TEST_CONVERSION_TIME,
                        TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                        TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID,
                        TEST_ACTUAL_AMOUNT, TEST_CLICK_ID, mainCampaignId);
        List<ConversionRegistrationDetails> details = asList(subConversion);

        doReturn(mainCampaignId).when(underTest).getCampaignId();
        LocalDate currentDate = LocalDate.of(2023, 10, 15);
        doReturn(currentDate).when(underTest).getCurrentLocalDate();

        // when
        List<ConversionsWithClickIdDetails> actual = underTest
                .createConversionsWithClickIdDetailsBy(details);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());

        ConversionsWithClickIdDetails result = actual.get(0);
        assertEquals(mainCampaignId.longValue(), result.getCampaignId());
        assertEquals(0L, result.getClickFromCampaignId());
        assertEquals(currentDate, result.getConfirmationDate());
        assertEquals(1, result.getConversions().size());
        ConversionRegistrationDetails convertedConversion = result.getConversions().get(0);
        assertFalse(convertedConversion instanceof ClickConversionRegistrationDetails);
    }

    @Test
    public void testCreateConversionsWithClickIdDetailsShouldReturnEmptyListWhenNoDetails() {
        // given
        List<ConversionRegistrationDetails> details = emptyList();

        // when
        List<ConversionsWithClickIdDetails> actual = underTest
                .createConversionsWithClickIdDetailsBy(details);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testConvertToConversionRegistrationDetailsShouldReturnSameObjectWhenAlreadyConversionRegistrationDetails() {
        // given
        ConversionRegistrationDetails originalConversion =
                new ConversionRegistrationDetails(TEST_CONVERSION_TIME, TEST_IDENTIFIER,
                        TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                        TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID,
                        TEST_ACTUAL_AMOUNT, TEST_CLICK_ID);

        // when
        ConversionRegistrationDetails actual = underTest
                .convertToConversionRegistrationDetails(originalConversion);

        // then
        assertSame(originalConversion, actual);
    }

    @Test
    public void testConvertToConversionRegistrationDetailsShouldConvertClickConversionToConversionRegistrationDetails() {
        // given
        Long subCampaignId = 456L;
        ClickConversionRegistrationDetails clickConversion =
                new ClickConversionRegistrationDetails(TEST_CONVERSION_TIME,
                        TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                        TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID,
                        TEST_ACTUAL_AMOUNT, TEST_CLICK_ID, subCampaignId);

        // when
        ConversionRegistrationDetails actual = underTest
                .convertToConversionRegistrationDetails(clickConversion);

        // then
        assertFalse(actual instanceof ClickConversionRegistrationDetails);

        assertEquals(TEST_CONVERSION_TIME, actual.getConversionTime());
        assertEquals(TEST_IDENTIFIER, actual.getTransactionId());
        assertEquals(TEST_RESULT_ID, actual.getResultId());
        assertEquals(TEST_CUSTOMER_TYPE, actual.getCustomerType());
        assertEquals(TEST_BRAND_COMMISSION_CATEGORY, actual.getProductCategoryId());
        assertEquals(TEST_PRODUCT_ID, actual.getProductId());
        assertEquals(TEST_ACTUAL_AMOUNT, actual.getProductUnitPrice());
        assertEquals(TEST_CLICK_ID, actual.getClickId());
    }

    @Test
    public void testGetBrandSubCampaignIdFromShouldReturnKolCampaignIdWhenCampaignTypeIsBrand() {
        // given
        Long expected = 2073L;
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(expected).when(underTest)
                .getAdvParentCampaignId(CAMPAIGN_ID);

        // when
        Long actual = underTest.getBrandSubCampaignIdFrom(BRAND, SHOP_ID);

        // then
        assertEquals(expected, actual);
        verify(underTest, never()).findBrandCampaignByShopId(anyString(), anyLong());
    }

    @Test
    public void testGetBrandSubCampaignIdFromShouldReturnBrandCampaignIdWhenCampaignTypeIsRegular() {
        // given
        Long expected = 1045L;

        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(expected).when(underTest)
                .findBrandCampaignByShopId(SHOP_ID, CAMPAIGN_ID);

        // when
        Long actual = underTest.getBrandSubCampaignIdFrom(REGULAR, SHOP_ID);

        // then
        assertEquals(expected, actual);
        verify(underTest, never()).getAdvParentCampaignId(CAMPAIGN_ID);
    }

    @Test
    public void testGetBrandSubCampaignIdFromShouldReturnBrandCampaignIdWhenCampaignTypeIsKol() {
        // given
        Long expected = 1045L;

        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(expected).when(underTest)
                .findBrandCampaignByShopId(SHOP_ID, CAMPAIGN_ID);

        // when
        Long actual = underTest.getBrandSubCampaignIdFrom(KOL, SHOP_ID);

        // then
        assertEquals(expected, actual);
        verify(underTest, never()).getAdvParentCampaignId(anyLong());
    }

    @Test
    public void testProcessConversionByBrandItemRulesShouldReturnBrandItemConversionsWhenIsBrandItemIsTrue() {
        // given
        boolean isBrandItem = true;
        ConversionRegistrationDetails mainConversion = createTestMainConversion();
        CampaignAdvType campaignType = BRAND;

        List<ConversionRegistrationDetails> expectedBrandItemConversions = Arrays.asList(
                mainConversion, createTestClickConversion(456L));

        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(campaignType).when(underTest).findCampaignType(CAMPAIGN_ID);
        doReturn(expectedBrandItemConversions).when(underTest)
                .createBrandItemConversions(any(ConversionRegistrationDetails.class),
                        eq(campaignType), eq(TEST_SHOP_ID));

        // when
        List<ConversionRegistrationDetails> actual = underTest
                .processConversionByBrandItemRules(TEST_CONVERSION_TIME, TEST_IDENTIFIER,
                        TEST_RESULT_ID, TEST_CUSTOMER_TYPE, TEST_CATEGORY_ID,
                        TEST_PRODUCT_ID, TEST_ACTUAL_AMOUNT, TEST_CLICK_ID, TEST_SHOP_ID,
                        isBrandItem);

        // then
        assertEquals(expectedBrandItemConversions, actual);
    }

    @Test
    public void testProcessConversionByBrandItemRulesShouldReturnNormalConversionsWhenIsBrandItemIsFalse() {
        // given
        boolean isBrandItem = false;
        ConversionRegistrationDetails mainConversion = createTestMainConversion();
        CampaignAdvType campaignType = REGULAR;

        List<ConversionRegistrationDetails> expectedNormalConversions = asList(mainConversion);

        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(campaignType).when(underTest).findCampaignType(CAMPAIGN_ID);
        doReturn(expectedNormalConversions).when(underTest)
                .createNonBrandConversions(any(ConversionRegistrationDetails.class),
                        eq(campaignType), eq(TEST_CONVERSION_TIME), eq(TEST_IDENTIFIER),
                        eq(TEST_RESULT_ID), eq(TEST_CUSTOMER_TYPE), eq(TEST_CATEGORY_ID),
                        eq(TEST_PRODUCT_ID), eq(TEST_ACTUAL_AMOUNT), eq(TEST_CLICK_ID));

        // when
        List<ConversionRegistrationDetails> actual = underTest
                .processConversionByBrandItemRules(TEST_CONVERSION_TIME, TEST_IDENTIFIER,
                        TEST_RESULT_ID, TEST_CUSTOMER_TYPE, TEST_CATEGORY_ID,
                        TEST_PRODUCT_ID, TEST_ACTUAL_AMOUNT, TEST_CLICK_ID, TEST_SHOP_ID,
                        isBrandItem);

        // then
        assertEquals(expectedNormalConversions, actual);
    }

    @Test
    public void testCreateExtraBonusesShouldReturnEmptyWhenGrossBrandCommissionIsInvalid() {
        // given
        ShopeeItem testShopeeItem = createTestShopeeItem(BigDecimal.ZERO,
                BigDecimal.ZERO);
        ShopeeOrder testShopeeOrder = createTestShopeeOrder(testShopeeItem);
        List<ConversionRegistrationDetails> brandItemConversions =
                createBrandItemConversionsWithSubConversion();
        doReturn(false).when(underTest).isValidCommission(BigDecimal.ZERO);

        // when
        List<ConversionRegistrationDetails> actual = underTest.createExtraBonuses(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_CLICK_ID,
                testShopeeItem, testShopeeOrder, brandItemConversions);

        // then
        assertExtraBonusResultIsEmpty(actual);
    }

    @Test
    public void testCreateExtraBonusesShouldReturnEmptyWhenBothMainAndSubCampaignsAreBrand() {
        // given
        ShopeeItem testShopeeItem = createTestShopeeItem(TEST_ITEM_SELLER_COMMISSION,
                TEST_GROSS_BRAND_COMMISSION);
        ShopeeOrder testShopeeOrder = createTestShopeeOrder(testShopeeItem);
        List<ConversionRegistrationDetails> brandItemConversions =
                createBrandItemConversionsWithSubConversion();
        doReturn(true).when(underTest)
                .isValidCommission(TEST_ITEM_SELLER_COMMISSION);
        doReturn(TEST_MAIN_CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(true).when(underTest).isBrandCampaign(TEST_MAIN_CAMPAIGN_ID);
        doReturn(true).when(underTest).isBrandCampaign(TEST_SUB_CAMPAIGN_ID);

        // when
        List<ConversionRegistrationDetails> actual = underTest.createExtraBonuses(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_CLICK_ID,
                testShopeeItem, testShopeeOrder, brandItemConversions);

        // then
        assertExtraBonusResultIsEmpty(actual);
    }

    @Test
    public void testCreateExtraBonusesShouldCreateBothMainAndSubConversionsWhenBothCampaignsAreKol() {
        // given
        ShopeeItem testShopeeItem = createTestShopeeItem(TEST_ITEM_SELLER_COMMISSION,
                TEST_GROSS_BRAND_COMMISSION);
        ShopeeOrder testShopeeOrder = createTestShopeeOrder(testShopeeItem);
        List<ConversionRegistrationDetails> brandItemConversions =
                createBrandItemConversionsWithSubConversion();
        doReturn(true).when(underTest)
                .isValidCommission(TEST_ITEM_SELLER_COMMISSION);
        doReturn(TEST_MAIN_CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(false).when(underTest).isBrandCampaign(TEST_MAIN_CAMPAIGN_ID);
        doReturn(false).when(underTest).isBrandCampaign(TEST_SUB_CAMPAIGN_ID);
        doReturn(TEST_EXPECTED_PRODUCT_ID).when(underTest)
                .createExtraBonusProductId(testShopeeItem, testShopeeOrder,
                        TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION);
        doReturn(INDONESIA_COUNTRY_CODE).when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createExtraBonuses(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_CLICK_ID,
                testShopeeItem, testShopeeOrder, brandItemConversions);

        // then
        assertExtraBonusResultContainsMainAndSubConversions(actual, TEST_SUB_CAMPAIGN_ID);
    }

    @Test
    public void testCreateExtraBonusesShouldCreateOnlyMainConversionWhenMainIsKolAndSubIsBrand() {
        // given
        ShopeeItem testShopeeItem = createTestShopeeItem(TEST_ITEM_SELLER_COMMISSION,
                TEST_GROSS_BRAND_COMMISSION);
        ShopeeOrder testShopeeOrder = createTestShopeeOrder(testShopeeItem);
        List<ConversionRegistrationDetails> brandItemConversions =
                createBrandItemConversionsWithSubConversion();
        doReturn(true).when(underTest)
                .isValidCommission(TEST_ITEM_SELLER_COMMISSION);
        doReturn(TEST_MAIN_CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(false).when(underTest).isBrandCampaign(TEST_MAIN_CAMPAIGN_ID);
        doReturn(true).when(underTest).isBrandCampaign(TEST_SUB_CAMPAIGN_ID);
        doReturn(TEST_EXPECTED_PRODUCT_ID).when(underTest)
                .createExtraBonusProductId(testShopeeItem, testShopeeOrder,
                        TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION);
        doReturn(INDONESIA_COUNTRY_CODE).when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createExtraBonuses(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_CLICK_ID,
                testShopeeItem, testShopeeOrder, brandItemConversions);

        // then
        assertExtraBonusResultContainsOnlyMainConversion(actual);
    }

    @Test
    public void testCreateExtraBonusesShouldCreateOnlySubConversionWhenMainIsBrandAndSubIsKol() {
        // given
        ShopeeItem testShopeeItem = createTestShopeeItem(TEST_ITEM_SELLER_COMMISSION,
                TEST_GROSS_BRAND_COMMISSION);
        ShopeeOrder testShopeeOrder = createTestShopeeOrder(testShopeeItem);
        List<ConversionRegistrationDetails> brandItemConversions =
                createBrandItemConversionsWithSubConversion();
        doReturn(true).when(underTest).isValidCommission(TEST_ITEM_SELLER_COMMISSION);
        doReturn(TEST_MAIN_CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(true).when(underTest).isBrandCampaign(TEST_MAIN_CAMPAIGN_ID);
        doReturn(false).when(underTest).isBrandCampaign(TEST_SUB_CAMPAIGN_ID);
        doReturn(TEST_EXPECTED_PRODUCT_ID).when(underTest)
                .createExtraBonusProductId(testShopeeItem, testShopeeOrder,
                        TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION);
        doReturn(INDONESIA_COUNTRY_CODE).when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createExtraBonuses(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_CLICK_ID,
                testShopeeItem, testShopeeOrder, brandItemConversions);

        // then
        assertExtraBonusResultContainsOnlySubConversion(actual, TEST_SUB_CAMPAIGN_ID);
    }

    @Test
    public void testCreateExtraBonusesShouldReturnEmptyWhenSubConversionsIsEmpty() {
        // given
        ShopeeItem testShopeeItem = createTestShopeeItem(TEST_ITEM_SELLER_COMMISSION, TEST_GROSS_BRAND_COMMISSION);
        ShopeeOrder testShopeeOrder = createTestShopeeOrder(testShopeeItem);
        List<ConversionRegistrationDetails> emptyBrandItemConversions = new ArrayList<>();
        doReturn(true).when(underTest).isValidCommission(TEST_ITEM_SELLER_COMMISSION);
        doReturn(TEST_MAIN_CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(true).when(underTest).isBrandCampaign(TEST_MAIN_CAMPAIGN_ID);

        // when
        List<ConversionRegistrationDetails> actual = underTest.createExtraBonuses(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_CLICK_ID,
                testShopeeItem, testShopeeOrder, emptyBrandItemConversions);

        // then
        assertExtraBonusResultIsEmpty(actual);
    }

    @Test
    public void testCreateExtraBonusesProductIdShouldReturnCorrectDataWhenCountryIsIndonesia() {
        // given
        String expectedIndonesiaBonusProductId = TEST_PRODUCT_ID + BONUS_SUFFIX;
        ShopeeItem validShopeeItem = createValidShopeeItemForExtraBonus();

        doReturn(INDONESIA_COUNTRY_CODE).when(underTest).getTargetCountryCode();

        // when
        String actual = underTest.createExtraBonusProductId(validShopeeItem,
                createValidShopeeOrderForExtraBonus(), TEST_PRODUCT_ID,
                TEST_ITEM_SELLER_COMMISSION);

        // then
        assertEquals(expectedIndonesiaBonusProductId, actual);
    }

    @Test
    public void testCreateExtraBonusesProductIdShouldReturnCorrectDataWhenCountryIsThailand() {
        // given
        String expectedThailandXtraCommProductId = TEST_PRODUCT_ID + XTRA_COMM_SUFFIX;
        ShopeeItem validShopeeItem = createValidShopeeItemForExtraBonus();

        doReturn(THAILAND_COUNTRY_CODE).when(underTest).getTargetCountryCode();

        // when
        String actual = underTest.createExtraBonusProductId(validShopeeItem,
                createValidShopeeOrderForExtraBonus(), TEST_PRODUCT_ID,
                TEST_ITEM_SELLER_COMMISSION);

        // then
        assertEquals(expectedThailandXtraCommProductId, actual);
    }

    @Test
    public void testCreateExtraBonusesProductIdShouldReturnCorrectDataWhenCountryIsPhilippines() {
        // given
        String expectedPhilippinesXtraCommProductId = TEST_PRODUCT_ID + XTRA_COMM_SUFFIX;
        ShopeeItem validShopeeItem = createValidShopeeItemForExtraBonus();

        doReturn(PHILIPPINES_COUNTRY_CODE).when(underTest).getTargetCountryCode();

        // when
        String actual = underTest.createExtraBonusProductId(validShopeeItem,
                createValidShopeeOrderForExtraBonus(), TEST_PRODUCT_ID,
                TEST_ITEM_SELLER_COMMISSION);

        // then
        assertEquals(expectedPhilippinesXtraCommProductId, actual);
    }

    @Test
    public void testCreateExtraBonusesProductIdShouldReturnCorrectDataWhenCountryIsMalaysia() {
        // given
        ShopeeItem validShopeeItem = createValidShopeeItemForExtraBonus();
        String expectedMalaysiaProductId = TEST_EXPECTED_PRODUCT_ID;

        doReturn(MALAYSIA_COUNTRY_CODE).when(underTest).getTargetCountryCode();
        ShopeeOrder validShopeeOrder = createValidShopeeOrderForExtraBonus();
        doReturn(expectedMalaysiaProductId).when(underTest)
                .getProductId(validShopeeItem, validShopeeOrder.getOrderId(),
                        TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION, XTRA_COMM_KEYWORD);

        // when
        String actual = underTest.createExtraBonusProductId(validShopeeItem,
                validShopeeOrder, TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION);

        // then
        assertEquals(expectedMalaysiaProductId, actual);
        verify(underTest).getProductId(validShopeeItem, validShopeeOrder.getOrderId(),
                TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION, XTRA_COMM_KEYWORD);
    }

    @Test
    public void testCreateExtraBonusesProductIdShouldReturnCorrectDataWhenCountryIsSingapore() {
        // given
        ShopeeItem validShopeeItem = createValidShopeeItemForExtraBonus();
        doReturn(SINGAPORE_COUNTRY_CODE).when(underTest).getTargetCountryCode();
        ShopeeOrder validShopeeOrder = createValidShopeeOrderForExtraBonus();
        doReturn(TEST_EXPECTED_PRODUCT_ID).when(underTest)
                .getProductId(validShopeeItem, validShopeeOrder.getOrderId(),
                        TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION, XTRA_COMM_KEYWORD);

        // when
        String actual = underTest.createExtraBonusProductId(validShopeeItem,
                validShopeeOrder, TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION);

        // then
        assertEquals(TEST_EXPECTED_PRODUCT_ID, actual);
        verify(underTest).getProductId(validShopeeItem, validShopeeOrder.getOrderId(),
                TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION, XTRA_COMM_KEYWORD);
    }

    @Test
    public void testCreateExtraBonusesProductIdShouldReturnCorrectDataWhenCountryIsTaiwan() {
        // given
        ShopeeItem validShopeeItem = createValidShopeeItemForExtraBonus();

        doReturn(TAIWAN_COUNTRY_CODE).when(underTest).getTargetCountryCode();
        ShopeeOrder validShopeeOrder = createValidShopeeOrderForExtraBonus();
        doReturn(TEST_EXPECTED_PRODUCT_ID).when(underTest)
                .getProductId(validShopeeItem, validShopeeOrder.getOrderId(),
                        TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION, XTRA_COMM_KEYWORD);

        // when
        String actual = underTest.createExtraBonusProductId(validShopeeItem,
                validShopeeOrder, TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION);

        // then
        assertEquals(TEST_EXPECTED_PRODUCT_ID, actual);
        verify(underTest).getProductId(validShopeeItem, validShopeeOrder.getOrderId(),
                TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION, XTRA_COMM_KEYWORD);
    }

    @Test
    public void testCreateExtraBonusesProductIdShouldReturnCorrectDataWhenCountryCodeIsNull() {
        // given
        ShopeeItem validShopeeItem = createValidShopeeItemForExtraBonus();

        doReturn(null).when(underTest).getTargetCountryCode();
        ShopeeOrder validShopeeOrder = createValidShopeeOrderForExtraBonus();
        doReturn(TEST_EXPECTED_PRODUCT_ID).when(underTest)
                .getProductId(validShopeeItem, validShopeeOrder.getOrderId(),
                        TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION, XTRA_COMM_KEYWORD);

        // when
        String actual = underTest.createExtraBonusProductId(validShopeeItem,
                validShopeeOrder, TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION);

        // then
        assertEquals(TEST_EXPECTED_PRODUCT_ID, actual);
        verify(underTest).getProductId(validShopeeItem, validShopeeOrder.getOrderId(),
                TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION, XTRA_COMM_KEYWORD);
    }

    @Test
    public void testGetRawShouldReturnCorrectDataWhenScrollIdIsEmpty() throws Exception {
        // when
        String raw = underTest.getRaw(PURCHASE_TIME_START, PURCHASE_TIME_END);

        // then
        assertEquals(
                String.format(RAW_FORMAT, PURCHASE_TIME_START, PURCHASE_TIME_END, EMPTY),
                raw);
    }

    @Test
    public void testGetRawShouldReturnCorrectDataWhenScrollIdIsNotEmpty()
            throws Exception {
        // when
        String scrollId = "scrollId";
        doReturn(scrollId).when(underTest).getScrollId();
        String raw = underTest.getRaw(PURCHASE_TIME_START, PURCHASE_TIME_END);

        // then
        assertEquals(String.format(RAW_FORMAT, PURCHASE_TIME_START, PURCHASE_TIME_END,
                ", scrollId:\\\"scrollId\\\""), raw);
    }

    @Test
    public void testGetResponseShouldReturnCorrectResponseDataWhenCalled()
            throws Exception {
        // given
        doReturn("ID").when(underTest).getTargetCountryCode();
        ZoneId zoneId = ZoneId.of("Asia/Jakarta");
        LocalDateTime scanTimeFrom = LocalDateTime.of(2023, 10, 5, 0, 0);
        doReturn(scanTimeFrom).when(underTest).getScanTimeFrom();
        LocalDateTime scanTimeTo = LocalDateTime.of(2023, 10, 5, 23, 59);
        doReturn(scanTimeTo).when(underTest).getScanTimeTo();
        long purchaseTimeStart = scanTimeFrom.atZone(zoneId).toEpochSecond();
        long purchaseTimeEnd = scanTimeTo.atZone(zoneId).toEpochSecond();
        String raw = "raw";
        doReturn(raw).when(underTest).getRaw(purchaseTimeStart, purchaseTimeEnd);
        String signature = "signature";
        long now = 100L;
        doReturn(now).when(underTest).getEpochSecondNow();
        doReturn(signature).when(underTest).hash256(APP_ID + now + raw + SECRET);
        CloseableHttpClient httpClient = mock(CloseableHttpClient.class);
        doReturn(httpClient).when(underTest).createHttpClient();
        HttpPost httpPost = mock(HttpPost.class);
        doReturn(httpPost).when(underTest).createHttpPost();
        StringEntity entity = new StringEntity(raw);
        doReturn(entity).when(underTest).createEntityBy(raw);
        CloseableHttpResponse response = mock(CloseableHttpResponse.class);
        when(httpClient.execute(httpPost)).thenReturn(response);
        StringEntity responseEntity = new StringEntity("response");
        when(response.getEntity()).thenReturn(responseEntity);

        // when
        String actual = underTest.getResponse(APP_ID, SECRET);

        // then
        assertEquals("response", actual);
        verify(httpPost).setHeader(AUTHORIZATION, "SHA256 Credential=" + APP_ID
                + ",Timestamp=" + now + ",Signature=" + signature);
        verify(httpPost).setHeader(CONTENT_TYPE, "application/json");
        verify(httpPost).setEntity(entity);
    }

    @Test
    public void testGetResponseBodyShouldReturnNullAndRanTwentyTimesWhenResponseBodyIsError()
            throws Exception {
        // given
        SecretsManagerClient client = mock(SecretsManagerClient.class);
        doReturn(client).when(underTest).getSecretsManagerClient();
        doReturn(SECRET_NAME).when(underTest).getSecretName();
        when(client.getSecretValue(SECRET_NAME)).thenReturn(SECRET_VALUE);
        String responseBody = "{\"errors\":{}}";
        doReturn(responseBody).when(underTest).getResponse(APP_ID, SECRET);
        doReturn(REQUEST_START_TIME, REQUEST_END_TIME).when(underTest)
                .getZonedDateTimeNow();
        doNothing().when(underTest).sleep(DURATION_TIME);

        // when
        String actual = underTest.getResponseBody();

        // then
        assertNull(actual);
        assertEquals(underTest.getEmptyTimes(), 20);
    }

    @Test
    public void testGetResponseBodyShouldReturnNullAndRanTwentyTimesWhenResponseScrollidIsEmpty()
            throws Exception {
        // given
        SecretsManagerClient client = mock(SecretsManagerClient.class);
        doReturn(client).when(underTest).getSecretsManagerClient();
        doReturn(SECRET_NAME).when(underTest).getSecretName();
        when(client.getSecretValue(SECRET_NAME)).thenReturn(SECRET_VALUE);
        String responseBody = "{\"data\":{\"conversionReport\":"
                + "{\"nodes\":[],\"pageInfo\":{\"limit\":1,\"hasNextPage\":"
                + "false,\"scrollId\":\"\"}}}}";
        JsonObject obj = new JsonParser().parse(responseBody).getAsJsonObject();
        ShopeeConversionReport conversionReport = new ShopeeConversionReport(emptyList(),
                new ShopeePageInfo(1, false, EMPTY));
        doReturn(conversionReport).when(underTest).parseConversionReport(obj);
        doReturn(responseBody).when(underTest).getResponse(APP_ID, SECRET);
        doReturn(REQUEST_START_TIME, REQUEST_END_TIME).when(underTest)
                .getZonedDateTimeNow();
        doNothing().when(underTest).sleep(DURATION_TIME);

        // when
        String actual = underTest.getResponseBody();

        // then
        assertNull(actual);
        assertEquals(underTest.getEmptyTimes(), 20);
    }

    @Test
    public void testGetResponseBodyShouldReturnCorrectResponseBodyWhenCalled()
            throws Exception {
        // given
        SecretsManagerClient client = mock(SecretsManagerClient.class);
        doReturn(client).when(underTest).getSecretsManagerClient();
        doReturn(SECRET_NAME).when(underTest).getSecretName();
        when(client.getSecretValue(SECRET_NAME)).thenReturn(SECRET_VALUE);
        String responseBody = "{\"data\":{\"conversionReport\":"
                + "{\"nodes\":[],\"pageInfo\":{\"limit\":1,\"hasNextPage\":"
                + "false,\"scrollId\":\"\"}}}}";
        JsonObject obj = new JsonParser().parse(responseBody).getAsJsonObject();
        String scrollId = "scrollId";
        ShopeeConversionReport conversionReport = new ShopeeConversionReport(emptyList(),
                new ShopeePageInfo(1, false, scrollId));
        doReturn(conversionReport).when(underTest).parseConversionReport(obj);
        doReturn(responseBody).when(underTest).getResponse(APP_ID, SECRET);
        doReturn(REQUEST_START_TIME, REQUEST_END_TIME).when(underTest)
                .getZonedDateTimeNow();
        doReturn(5).when(underTest)
                .getLimitTimeCallApi();
        doNothing().when(underTest).sleep(DURATION_TIME);

        // when
        String actual = underTest.getResponseBody();

        // then
        assertEquals(responseBody, actual);
        assertEquals(underTest.getScrollId(), scrollId);
        assertEquals(underTest.getEmptyTimes(), 0);
    }

    @Test
    public void testGetResponseBodyShouldSetScrollIdIsEmptyWhenDurationTimeCallApiShopeeIsGreaterThanLimitTimeCallApi()
            throws Exception {
        // given
        SecretsManagerClient client = mock(SecretsManagerClient.class);
        doReturn(client).when(underTest).getSecretsManagerClient();
        doReturn(SECRET_NAME).when(underTest).getSecretName();
        when(client.getSecretValue(SECRET_NAME)).thenReturn(SECRET_VALUE);
        String responseBody = "{\"data\":{\"conversionReport\":"
                              + "{\"nodes\":[],\"pageInfo\":{\"limit\":1,\"hasNextPage\":"
                              + "false,\"scrollId\":\"\"}}}}";
        JsonObject obj = new JsonParser().parse(responseBody).getAsJsonObject();
        String scrollId = "scrollId";
        ShopeeConversionReport conversionReport = new ShopeeConversionReport(emptyList(),
                new ShopeePageInfo(1, false, scrollId));
        doReturn(conversionReport).when(underTest).parseConversionReport(obj);
        doReturn(responseBody).when(underTest).getResponse(APP_ID, SECRET);
        doReturn(REQUEST_START_TIME, REQUEST_END_TIME).when(underTest)
                .getZonedDateTimeNow();
        doReturn(0).when(underTest).getLimitTimeCallApi();
        doNothing().when(underTest).sleep(DURATION_TIME);

        // when
        String actual = underTest.getResponseBody();

        // then
        assertEquals(responseBody, actual);
        assertEquals(underTest.getScrollId(), EMPTY);
        assertEquals(underTest.getEmptyTimes(), 0);
    }

    @Test
    public void testGetConversionDataShouldReturnCorrectDataWhenIsRequestTimeOutIsFalse()
            throws Exception {
        // given
        String responseBody = "responseBody";
        doReturn(responseBody).when(underTest).getResponseBody();
        List<ConversionRegistrationDetails> details1 = Arrays
                .asList(mock(ConversionRegistrationDetails.class));
        List<ConversionRegistrationDetails> details2 = Arrays
                .asList(mock(ConversionRegistrationDetails.class));
        List<ConversionRegistrationDetails> parsed = Stream.of(details1, details2)
                .flatMap(x -> x.stream()).collect(Collectors.toList());
        doReturn(parsed).when(underTest).parse(responseBody);
        doReturn(false).when(underTest).isRequestTimeOut();
        when(underTest.getResetCount()).thenReturn(1);
        when(underTest.getLimitResetTimeCount()).thenReturn(3);
        when(underTest.hasNext()).thenReturn( false);

        // when
        List<ConversionRegistrationDetails> actual = underTest.getConversionData();

        // then
        assertEquals(parsed, actual);
    }

    @Test
    public void testGetConversionDataShouldClearAllDataWhenIsRequestTimeOutIsTrue()
            throws Exception {
        // given
        String responseBody = "responseBody";
        doReturn(responseBody).when(underTest).getResponseBody();
        List<ConversionRegistrationDetails> details1 = Arrays
                .asList(mock(ConversionRegistrationDetails.class));
        List<ConversionRegistrationDetails> details2 = Arrays
                .asList(mock(ConversionRegistrationDetails.class));
        List<ConversionRegistrationDetails> parsed = Stream.of(details1, details2)
                .flatMap(x -> x.stream()).collect(Collectors.toList());
        doReturn(parsed).when(underTest).parse(responseBody);
        doReturn(true).when(underTest).isRequestTimeOut();
        when(underTest.getResetCount()).thenReturn(1);
        when(underTest.getLimitResetTimeCount()).thenReturn(3);
        when(underTest.hasNext()).thenReturn( true, false);

        // when
        List<ConversionRegistrationDetails> actual = underTest.getConversionData();

        // then
        assertEquals(parsed, actual);
    }

    @Test
    public void testGetConversionDataShouldReturnEmptyListWhenIsRequestTimeOutFalse()
            throws Exception {
        // given
        String responseBody = "responseBody";
        doReturn(responseBody).when(underTest).getResponseBody();
        List<ConversionRegistrationDetails> parsed = emptyList();
        doReturn(parsed).when(underTest).parse(responseBody);
        doReturn(false).when(underTest).isRequestTimeOut();
        when(underTest.getResetCount()).thenReturn(1);
        when(underTest.getLimitResetTimeCount()).thenReturn(3);
        when(underTest.hasNext()).thenReturn( true, false);

        // when
        List<ConversionRegistrationDetails> actual = underTest.getConversionData();

        // then
        assertEquals(parsed, actual);
    }

    @Test
    public void testIsCrossBrandSubConversionsShouldReturnTrueWhenBrandCampaignAndDifferentShopCampaign() {
        // given
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(CAMPAIGN_BAND_ID).when(underTest).findCampaignBy(TEST_SHOP_ID);

        // when
        boolean actual = underTest.isCrossBrandSubConversions(BRAND, TEST_SHOP_ID);

        // then
        assertTrue(actual);
        verify(underTest).findCampaignBy(TEST_SHOP_ID);
    }

    @Test
    public void testIsCrossBrandSubConversionsShouldReturnFalseWhenNotBrandCampaign() {
        // given
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();

        // when
        boolean actual = underTest.isCrossBrandSubConversions(REGULAR, TEST_SHOP_ID);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsCrossBrandSubConversionsShouldReturnFalseWhenSameShopCampaign() {
        // given
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(CAMPAIGN_ID).when(underTest).findCampaignBy(TEST_SHOP_ID);

        // when
        boolean actual = underTest.isCrossBrandSubConversions(BRAND, TEST_SHOP_ID);

        // then
        assertFalse(actual);
        verify(underTest).findCampaignBy(TEST_SHOP_ID);
    }

    @Test
    public void testIsCrossBrandSubConversionsShouldReturnFalseWhenShopCampaignNotFound() {
        // given
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(null).when(underTest).findCampaignBy(TEST_SHOP_ID);

        // when
        boolean actual = underTest.isCrossBrandSubConversions(BRAND, TEST_SHOP_ID);

        // then
        assertFalse(actual);
        verify(underTest).findCampaignBy(TEST_SHOP_ID);
    }

    @Test
    public void testCreateNonBrandConversionsShouldReturnSubConversionWhenCampaignTypeIsBrandAndHasParent() {
        // given
        ConversionRegistrationDetails mainConversion = createTestMainConversion();
        doReturn(TEST_MAIN_CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(PARENT_CAMPAIGN_ID).when(underTest)
                .getAdvParentCampaignId(TEST_MAIN_CAMPAIGN_ID);

        // when
        List<ConversionRegistrationDetails> actual = underTest.createNonBrandConversions(
                mainConversion, BRAND, TEST_CONVERSION_TIME, TEST_IDENTIFIER,
                TEST_RESULT_ID, TEST_CUSTOMER_TYPE, TEST_CATEGORY_ID, TEST_PRODUCT_ID,
                TEST_ACTUAL_AMOUNT, TEST_CLICK_ID);

        // then
        assertEquals(1, actual.size());

        assertTrue(actual.get(0) instanceof ClickConversionRegistrationDetails);
        ClickConversionRegistrationDetails subConversion =
                (ClickConversionRegistrationDetails) actual.get(0);
        assertEquals(PARENT_CAMPAIGN_ID, subConversion.getSubCampaignId());
        assertEquals(TEST_CONVERSION_TIME, subConversion.getConversionTime());
        assertEquals(TEST_IDENTIFIER, subConversion.getTransactionId());
    }

    @Test
    public void testCreateNonBrandConversionsShouldReturnMainConversionWhenCampaignTypeIsNotBrand() {
        // given
        ConversionRegistrationDetails mainConversion = createTestMainConversion();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createNonBrandConversions(
                mainConversion, REGULAR, TEST_CONVERSION_TIME, TEST_IDENTIFIER,
                TEST_RESULT_ID, TEST_CUSTOMER_TYPE, TEST_CATEGORY_ID, TEST_PRODUCT_ID,
                TEST_ACTUAL_AMOUNT, TEST_CLICK_ID);

        // then
        assertEquals(1, actual.size());
        assertEquals(mainConversion, actual.get(0));
    }

    @Test
    public void testGetBrandSubCampaignIdFromShouldReturnParentIdWhenCampaignTypeIsBrand() {
        // given
        Long parentId = 456L;
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(parentId).when(underTest).getAdvParentCampaignId(CAMPAIGN_ID);

        // when
        Long actual = underTest.getBrandSubCampaignIdFrom(BRAND, TEST_SHOP_ID);

        // then
        assertEquals(parentId, actual);
        verify(underTest).getAdvParentCampaignId(CAMPAIGN_ID);
    }

    @Test
    public void testGetBrandSubCampaignIdFromShouldReturnDefaultWhenCampaignTypeIsBrandAndNoParent() {
        // given
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(null).when(underTest).getAdvParentCampaignId(CAMPAIGN_ID);

        // when
        Long actual = underTest.getBrandSubCampaignIdFrom(BRAND, TEST_SHOP_ID);

        // then
        assertEquals(DEFAULT_CAMPAIGN_ID, actual);
        verify(underTest).getAdvParentCampaignId(CAMPAIGN_ID);
    }

    @Test
    public void testGetBrandSubCampaignIdFromShouldReturnBrandIdWhenCampaignTypeIsNotBrand() {
        // given
        Long brandId = 789L;
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(brandId).when(underTest).findBrandCampaignByShopId(TEST_SHOP_ID, CAMPAIGN_ID);

        // when
        Long actual = underTest.getBrandSubCampaignIdFrom(REGULAR, TEST_SHOP_ID);

        // then
        assertEquals(brandId, actual);
        verify(underTest).findBrandCampaignByShopId(TEST_SHOP_ID, CAMPAIGN_ID);
    }

    @Test
    public void testGetBrandSubCampaignIdFromShouldReturnDefaultWhenCampaignTypeIsNotBrandAndNoBrandFound() {
        // given
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(null).when(underTest).findBrandCampaignByShopId(TEST_SHOP_ID, CAMPAIGN_ID);

        // when
        Long actual = underTest.getBrandSubCampaignIdFrom(REGULAR, TEST_SHOP_ID);

        // then
        assertEquals(DEFAULT_CAMPAIGN_ID, actual);
        verify(underTest).findBrandCampaignByShopId(TEST_SHOP_ID, CAMPAIGN_ID);
    }

    @Test
    public void testGetConversionDataShouldReturnEmptyDataWhenResponseBodyEmpty()
            throws Exception {
        // given
        String responseBody = EMPTY;
        doReturn(responseBody).when(underTest).getResponseBody();
        List<ConversionRegistrationDetails> parsed = emptyList();
        doReturn(parsed).when(underTest).parse(responseBody);
        doReturn(false).when(underTest).isRequestTimeOut();
        when(underTest.getResetCount()).thenReturn(1);
        when(underTest.getLimitResetTimeCount()).thenReturn(3);
        when(underTest.hasNext()).thenReturn( false);

        // when
        List<ConversionRegistrationDetails> actual = underTest.getConversionData();

        // then
        assertEquals(parsed, actual);
    }

    @Test
    public void testIsTargetCampaignIdShouldReturnFalseWhenUtmContentDoesNotContainTargetMcnId()
            throws Exception {
        // give
        String[] utmContent = new String[] { "12345", "RKVALUEQQQ", "url" };

        // then
        boolean actual = underTest.isTargetCampaignId(utmContent, MCN_ID_DEFAULT);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsTargetCampaignIdShouldReturnTrueWhenThirdIndexOfUtmContentIsTargetCampaignId()
            throws Exception {
        // give
        String[] utmContent = new String[] { "12345", "RKVALUEQQQ", "url", MCN_ID_DEFAULT };

        // then
        boolean actual = underTest.isTargetCampaignId(utmContent, MCN_ID_DEFAULT);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsTargetCampaignIdShouldReturntrueWhenSecondIndexOfUtmContentIsTargetCampaignId()
            throws Exception {
        // give
        String[] utmContent = new String[] { "12345", "RKVALUEQQQ", MCN_ID_DEFAULT };

        // then
        boolean actual = underTest.isTargetCampaignId(utmContent, MCN_ID_DEFAULT);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsTargetCampaignIdShouldReturnFalseWhenLengthOfUtmContentLessThanFourAndSecondIndexOfUtmContentIsEmpty()
            throws Exception {
        // give
        String[] utmContent = new String[] { "12345", "RKVALUEQQQ", "" };

        // then
        boolean actual = underTest.isTargetCampaignId(utmContent, MCN_ID_DEFAULT);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsTargetCampaignIdShouldReturnFalseWhenLengthOfUtmContentEqualFourAndThirdIndexOfUtmContentIsEmptyAndSecondIndexOfUtmContentIsNotMcnId()
            throws Exception {
        // give
        String[] utmContent = new String[] { "12345", "RKVALUEQQQ", "url", "" };

        // then
        boolean actual = underTest.isTargetCampaignId(utmContent, MCN_ID_DEFAULT);

        // then
        assertFalse(actual);
    }

    @Test
    public void testGetClickIdFromShouldReturnFirstDataWhenSecondDataLengthLessThanEight()
            throws Exception {
        // given
        String[] utmContent = new String[] { "1", "2" };

        // when
        String actual = underTest.getClickIdFrom(utmContent);

        // then
        assertEquals("1", actual);
    }

    @Test
    public void testGetClickIdFromShouldReturnSecondDataWhenSecondDataLengthGreaterThanSeven()
            throws Exception {
        // given
        String[] utmContent = new String[] { "1", "22222222" };

        // when
        String actual = underTest.getClickIdFrom(utmContent);

        // then
        assertEquals("22222222", actual);
    }

    @Test
    public void testGetClickIdFromShouldReturnEmptyWhenSingleData() throws Exception {
        // given
        String[] utmContent = new String[] { "1" };

        // when
        String actual = underTest.getClickIdFrom(utmContent);

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testGetItemIdShouldReturnIdWhenItemFrequencyLessThanZero() throws Exception {
        // given
        List<String> items = Arrays.asList("1111","22222");
        String id = "xxx";

        // when
        String actual = underTest.getItemId(items, id);

        // then
        assertEquals(id,actual);
    }

    @Test
    public void testGetItemIdShouldReturnIdWhenItemFrequencyGreaterThanZero() throws Exception {
        // given
        List<String> items = Arrays.asList("tesst-11111","test-22222");
        String id = "xxx";

        // when
        String actual = underTest.getItemId(items, id);

        // then
        assertEquals(id,actual);
    }

    @Test
    public void testGetProductIdShouldReturnCorrectProductIdWhenItemCommissionGreaterThanZero() throws Exception {
        // given
        String expected = "orderId_shopId_id_modelId_globalCategoryLv3Name_ShopeeComm";
        String orderId = "orderId";
        ShopeeItem shopeeItem = new ShopeeItem(ITEM_ID, SHOP_ID, null, 1, ITEM_PRICE, null,
                ITEM_COMMISSION, null, null,
                MODEL_ID, GLOBAL_CATEGORY_LV1_NAME, null,
                GLOBAL_CATEGORY_LV_3_NAME, ORDERED_IN_SAME_SHOP);
        String defaultProductId = "productId";

        // when
        String actual = underTest.getProductId(shopeeItem, orderId, defaultProductId,
                ITEM_COMMISSION, SHOPEE_COMM);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetProductIdShouldReturnIdWhenItemFrequencyGreaterThanZero() throws Exception {
        // given
        String expected = "orderId_shopId_id_modelId_globalCategoryLv3Name_XTRAComm";
        String orderId = "orderId";
        ShopeeItem shopeeItem = new ShopeeItem(ITEM_ID, SHOP_ID, null, 1, ITEM_PRICE, null,
                null, null, ITEM_SELLER_COMMISSION,
                MODEL_ID, GLOBAL_CATEGORY_LV1_NAME, null,
                GLOBAL_CATEGORY_LV_3_NAME, ORDERED_IN_SAME_SHOP);
        String defaultProductId = "productId";

        // when
        String actual = underTest.getProductId(shopeeItem, orderId, defaultProductId,
                ITEM_SELLER_COMMISSION, XTRA_COMM);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testIsDuplicateConversionShouldReturnTrueWhenKeyExist() {
        // given
        String key = "testKey";
        when(merchantIntegrationHistoryMapper.findKeyExistingBy(key)).thenReturn(1);

        // when
        boolean actual = underTest.isDuplicateConversion(key);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsDuplicateConversionShouldReturnFalseWhenKeyDoesNotExists() {
        // given
        String key = "testKey";
        when(merchantIntegrationHistoryMapper.findKeyExistingBy(key))
                .thenReturn(0);

        // when
        boolean actual = underTest.isDuplicateConversion(key);

        // then
        assertFalse(actual);
    }

    @Test
    public void testInsertDataProceededShouldInsertDataWhenCalled() {
        // given
        MerchantIntegrationHistoryInsertRequest insertRequest =
                mock(MerchantIntegrationHistoryInsertRequest.class);

        // when
        underTest.insertDataProceeded(insertRequest);

        // then
        verify(merchantIntegrationHistoryMapper).insert(insertRequest);
    }

    @Test
    public void testInsertDataProceededShouldReturnCorrectErrorMessageWhenInsertFailed() {
        // given
        MerchantIntegrationHistoryInsertRequest insertRequest =
                new MerchantIntegrationHistoryInsertRequest("testKey", 100L,
                        LocalDateTime.of(2019, 3, 5, 0, 0), SHOPEE_MERCHANT.name(),
                        "data");
        RuntimeException exception = new RuntimeException();
        doThrow(exception).when(merchantIntegrationHistoryMapper).insert(insertRequest);
        doReturn(logger).when(underTest).getLogger();

        // when
        underTest.insertDataProceeded(insertRequest);

        // then
        verify(logger).error(FAIL_TO_INSERT_DATA, insertRequest.getKey());
    }

    @Test
    public void testCreateConversionProceededShouldReturnCorrectDataWhenCalled() {
        // given
        ConversionRegistrationDetails detail = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, RESULT_ID, "customerType", null,
                "orderId_id3_modelId-bonus", GROSS_BRAND_COMMISSION, CLICK_ID);
        List<ConversionRegistrationDetails> details = Arrays.asList(detail);
        String key = "966-20240101-157748364206437-240101NRXYPX4T-22747422164";
        long campaignId = 966L;
        String data = "[{\"conversionTime\":{\"dateTime\":{\"date\":{\"year\":2023,"
                      + "\"month\":7,\"day\":17},\"time\":{\"hour\":23,\"minute\":58,"
                      + "\"second\":20,\"nano\":0}},\"offset\":{\"totalSeconds\":25200},"
                      + "\"zone\":{\"id\":\"Asia/Jakarta\"}},"
                      + "\"transactionId\":\"checkoutId\",\"resultId\":3,"
                      + "\"customerType\":\"customerType\","
                      + "\"productId\":\"orderId_id3_modelId-bonus\","
                      + "\"productQuantity\":1,\"productUnitPrice\":20,"
                      + "\"discount\":0,\"status\":\"PENDING\",\"clickId\":\"clickId\"}]";
        when(underTest.getCampaignId()).thenReturn(campaignId);
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        LocalDateTime conversionOccursDate = LocalDateTime.of(2023, 11, 5, 0,
                0, 1, 0);
        // when
        MerchantIntegrationHistoryInsertRequest actual = underTest
                .createConversionProceeded(CONVERSION_TIME, details, key);

        // then
        assertEquals(key, actual.getKey());
        assertEquals(campaignId, actual.getCampaignId());
        assertEquals(conversionOccursDate, actual.getConversionOccursDate());
        assertEquals(data, actual.getData());
    }

    @Test
    public void testGetSkuFromShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        String expected = "id_modelId";
        ShopeeItem shopeeItem = new ShopeeItem(ITEM_ID, null, null, 1, ITEM_PRICE, null,
                ITEM_COMMISSION, null, null, MODEL_ID, GLOBAL_CATEGORY_LV1_NAME, null,
                null, ORDERED_IN_SAME_SHOP);

        // when
        String actual = underTest.getSkuFrom(shopeeItem);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCalculateProductUnitPriceShouldReturnCorrectDataWhenCalled()
            throws Exception {
        // given
        CurrencyRate currencyRate = new CurrencyRate(CURRENCY, BigDecimal.ONE);

        doReturn(CURRENCY).when(underTest)
                .getReceivedCurrency();
        doReturn(CAMPAIGN_ID).when(underTest)
                .getCampaignId();
        doReturn(currencyRate).when(underTest)
                .getCurrencyRateFrom(CURRENCY, CAMPAIGN_ID, CONVERSION_MONTH);

        // when
        BigDecimal actual = underTest.calculateProductUnitPrice(UNIT_PRICE,
                TIME1);

        // then
        assertEquals(UNIT_PRICE, actual);
    }

    @Test
    public void testGetCurrencyRateFromShouldReturnCorrectCurrencyRateWhenCurrencyIsEmpty()
            throws ExecutionException {
        // given
        CurrencyRate currencyRate = mock(CurrencyRate.class);
        when(currencyMapper.findCampaignCurrencyRate(CAMPAIGN_ID, CONVERSION_MONTH))
                .thenReturn(currencyRate);

        // when
        CurrencyRate actual = underTest.getCurrencyRateFrom(EMPTY, CAMPAIGN_ID,
                CONVERSION_MONTH);

        // then
        assertSame(currencyRate, actual);
        verify(currencyMapper, never()).findCurrencyRate(anyString(), anyLong(),
                any(YearMonth.class));
    }

    @Test
    public void testGetCurrencyRateFromShouldReturnCorrectCurrencyRateWhenGivenCurrencyIsNotEmptyAndCurrencyRateIsNull()
            throws ExecutionException {
        // given
        CurrencyRate currencyRate = new CurrencyRate(null, CURRENCY_RATE);
        when(currencyMapper.findCurrencyRate(CURRENCY, CAMPAIGN_ID, CONVERSION_MONTH))
                .thenReturn(currencyRate);

        // when
        CurrencyRate actual = underTest.getCurrencyRateFrom(CURRENCY, CAMPAIGN_ID,
                CONVERSION_MONTH);

        // then
        assertSame(currencyRate, actual);
        verify(currencyMapper, never()).findCampaignCurrencyRate(anyLong(), any(
                YearMonth.class));
    }

    @Test
    public void testGetCurrencyRateFromShouldReturnCorrectCurrencyRateWhenGivenCurrencyIsNotEmptyAndCurrencyRateIsNotNull()
            throws ExecutionException {
        // given
        CurrencyRate currencyRate = new CurrencyRate(CURRENCY, CURRENCY_RATE);
        when(currencyMapper.findCurrencyRate(CURRENCY, CAMPAIGN_ID, CONVERSION_MONTH))
                .thenReturn(currencyRate);

        // when
        CurrencyRate actual = underTest.getCurrencyRateFrom(CURRENCY, CAMPAIGN_ID,
                CONVERSION_MONTH);

        // then
        assertSame(currencyRate, actual);
        verify(currencyMapper, never()).findCampaignCurrencyRate(anyLong(),
                any(YearMonth.class));
    }

    @Test
    public void testGetCurrencyRateFromShouldThrowRuntimeExceptionAndSendToSlackMessageWhenCurrencyRateResponseIsNullAndIsShouldSendNotificationToSlackIsTrue()
            throws ExecutionException {
        // given
        doReturn(true).when(underTest).isShouldSendNotificationToSlack();
        doReturn(CURRENCY).when(underTest).getReceivedCurrency();
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();

        // when
        try {
            underTest.getCurrencyRateFrom(CURRENCY, CAMPAIGN_ID,
                    CONVERSION_MONTH);
            fail();

        // then
        } catch (TaekkyeonException ex) {
            assertEquals(ERROR_MESSAGE_CURRENCY_RATE, ex.getMessage());
            verify(slackService).send(ERROR_MESSAGE_FORMAT, "2023-07",
                    "3", CURRENCY);
        }
    }

    @Test
    public void testGetCurrencyRateFromShouldThrowRuntimeExceptionAndNotSendToSlackMessageWhenCurrencyRateResponseIsNullAndIsShouldSendNotificationToSlackIsFalse()
            throws ExecutionException {
        // given
        doReturn(false).when(underTest).isShouldSendNotificationToSlack();
        doReturn(CURRENCY).when(underTest).getReceivedCurrency();
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();

        // when
        try {
            underTest.getCurrencyRateFrom(CURRENCY, CAMPAIGN_ID,
                    CONVERSION_MONTH);
            fail();

            // then
        } catch (TaekkyeonException ex) {
            assertEquals(ERROR_MESSAGE_CURRENCY_RATE, ex.getMessage());
            verify(slackService, never()).send(anyString(), anyString(), anyString());
        }
    }

    @Test
    public void testGetCheckoutOrConversionIdShouldReturnCheckoutIdWhenResponseReturnCheckoutId()
            throws ExecutionException {
        // given
        ShopeeOrder shopeeOrder = mock(ShopeeOrder.class);
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, null,
                GROSS_COMMISSION, PURCHASE_TIME2, CAPPED_COMMISSION,
                TOTAL_BRAND_COMMISSION, ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE,
                UTM_CONTENT, null, null, asList(shopeeOrder));

        // when
        String actual = underTest.getCheckoutOrConversionId(shopeeNode);

        // then
        assertEquals(CHECKOUT_ID, actual);
    }

    @Test
    public void testGetCheckoutOrConversionIdShouldReturnConversionIdWhenCheckoutIdNull()
            throws ExecutionException {
        // given
        ShopeeOrder shopeeOrder = mock(ShopeeOrder.class);
        ShopeeNode shopeeNode = new ShopeeNode(null, CONVERSION_ID,
                GROSS_COMMISSION, PURCHASE_TIME2, CAPPED_COMMISSION,
                TOTAL_BRAND_COMMISSION, ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE,
                UTM_CONTENT, null, null, asList(shopeeOrder));

        // when
        String actual = underTest.getCheckoutOrConversionId(shopeeNode);

        // then
        assertEquals(CONVERSION_ID, actual);
    }

    @Test
    public void testGetNewCustomerTypeSuffixShouldReturnCorrectCustomerTypeWhenGivenAttributionTypeIsOrderedInSameShop()
            throws Exception {
        // given
        String expected = "customerType-direct";
        String customerType = "customerType";

        // when
        String actual = underTest.getNewCustomerTypeSuffix(customerType,ORDERED_IN_SAME_SHOP);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetNewCustomerTypeSuffixShouldReturnCorrectCustomerTypeWhenGivenAttributionTypeIsOrderedInDifferentShop()
            throws Exception {
        // given
        String expected = "customerType-indirect";
        String customerType = "customerType";

        // when
        String actual = underTest.getNewCustomerTypeSuffix(customerType, ORDERED_IN_DIFFERENT_SHOP);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testValidateDateTimeAfterShouldReturnTrueWhenGivenConversionTimeIsAfterDateTime()
            throws Exception {
        // given
        ZonedDateTime conversionTime = of(2024, 8, 1, 0, 0, 0, 0, ZONE_ID);
        doReturn(ZONE_ID).when(underTest).getZoneId();

        // when
        boolean actual = underTest.validateDateTimeAfter(conversionTime);

        // then
        assertTrue(actual);
    }

    @Test
    public void testValidateDateTimeAfterShouldReturnFalseWhenGivenConversionTimeIsBeforeDateTime()
            throws Exception {
        // given
        ZonedDateTime conversionTime = of(2024, 7, 31, 23, 59, 59, 0, ZONE_ID);
        doReturn(ZONE_ID).when(underTest).getZoneId();

        // when
        boolean actual = underTest.validateDateTimeAfter(conversionTime);

        // then
        assertFalse(actual);
    }

    @Test
    public void testValidateDateTimeAfterShouldReturnFalseWhenGivenConversionTimeIsAfterDateTime()
            throws Exception {
        // given
        ZonedDateTime conversionTime = of(2024, 8, 1, 0, 0, 0, 0, ZONE_ID);
        doReturn(ZONE_ID).when(underTest).getZoneId();

        // when
        boolean actual = underTest.validateDateTimeAfter(conversionTime, AFTER_DATE_TIME);

        // then
        assertFalse(actual);
    }

    @Test
    public void testValidateDateTimeAfterShouldReturnTrueWhenGivenConversionTimeIsBeforeDateTime()
            throws Exception {
        // given
        ZonedDateTime conversionTime = of(2024, 9, 1, 0, 0, 0, 0, ZONE_ID);
        doReturn(ZONE_ID).when(underTest).getZoneId();

        // when
        boolean actual = underTest.validateDateTimeAfter(conversionTime, AFTER_DATE_TIME);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsBrandItemShouldReturnTrueWhenGivenShopIdIsNotNullAndEmptyAndBrandCampaignIdExist() {
        // given
        when(apiKeyNameMapper.isBrandItem(SHOP_ID))
                .thenReturn(true);

        // when
        boolean actual = underTest.isBrandItem(SHOP_ID);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsBrandItemShouldReturnFalseWhenGivenShopIdIsNotNullAndEmptyAndBrandCampaignIdDoesNotExist() {
        // given
        when(apiKeyNameMapper.findBrandCampaignId(SHOP_ID, CAMPAIGN_ID))
                .thenReturn(null);

        // when
        boolean actual = underTest.isBrandItem(SHOP_ID);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsBrandItemShouldReturnFalseWhenShopIdIsNull() {
        // when
        boolean actual = underTest.isBrandItem(null);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsBrandItemShouldReturnFalseWhenShopIdIsEmpty() {
        // when
        boolean actual = underTest.isBrandItem(EMPTY);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsBrandItemShouldReturnFalseWhenGivenShopIdIsNotNullAndEmptyAndCampaignIdIsNull() {
        // when
        boolean actual = underTest.isBrandItem(SHOP_ID);

        // then
        assertFalse(actual);
        verify(apiKeyNameMapper, never()).findBrandCampaignId(anyString(), anyLong());
    }

    @Test
    public void findCampaignTypeShouldReturnCorrectTypeWhenCampaignIdExists() {
        // given
        CampaignAdvDetails campaignAdvDetails = new CampaignAdvDetails(CAMPAIGN_ID,
                BRAND);
        when(apiKeyNameMapper.findAdvCampaignBy(CAMPAIGN_ID))
                .thenReturn(campaignAdvDetails);

        // when
        CampaignAdvType actual = underTest.findCampaignType(CAMPAIGN_ID);

        // then
        assertEquals(BRAND, actual);
    }

    @Test
    public void testGetAdvParentCampaignIdShouldReturnCorrectCampaignIdWhenParentCampaignIdExists() {
        // given
        Long expected = 123L;

        when(apiKeyNameMapper.findParentCampaignIdBy(BRAND.name(), BRAND_CAMPAIGN_ID))
                .thenReturn(expected);

        // when
        Long actual = underTest.getAdvParentCampaignId(BRAND_CAMPAIGN_ID);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetAdvParentCampaignIdShouldReturnDefaultCampaignIdWhenParentCampaignIdDoesNotExist() {
        // given
        when(apiKeyNameMapper.findParentCampaignIdBy(BRAND.name(), BRAND_CAMPAIGN_ID))
                .thenReturn(null);


        // when
        Long actual = underTest.getAdvParentCampaignId(BRAND_CAMPAIGN_ID);

        // then
        assertEquals(DEFAULT_CAMPAIGN_ID, actual);
    }

    @Test
    public void testFindBrandCampaignByShopIdShouldReturnCorrectCampaignIdWhenBrandCampaignIdExists() {
        // given
        Long expected = 789L;

        when(apiKeyNameMapper.findBrandCampaignId(SHOP_ID, PARENT_CAMPAIGN_ID))
                .thenReturn(expected);

        // when
        Long actual = underTest.findBrandCampaignByShopId(SHOP_ID, PARENT_CAMPAIGN_ID);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testFindBrandCampaignByShopIdShouldReturnZeroWhenShopIdIsNull() {
        // when
        Long actual = underTest.findBrandCampaignByShopId(null, PARENT_CAMPAIGN_ID);

        // then
        assertEquals(DEFAULT_CAMPAIGN_ID, actual);
    }

    @Test
    public void testFindBrandCampaignByShopIdShouldReturnZeroWhenShopIdIsEmpty() {
        // given
        Long parentCampaignId = 456L;

        // when
        Long actual = underTest.findBrandCampaignByShopId(EMPTY, parentCampaignId);

        // then
        assertEquals(DEFAULT_CAMPAIGN_ID, actual);
    }

    @Test
    public void testFindBrandCampaignByShopIdShouldReturnDefaultCampaignIdWhenBrandCampaignIdDoesNotExist() {
        // given
        when(apiKeyNameMapper.findBrandCampaignId(SHOP_ID, PARENT_CAMPAIGN_ID))
                .thenReturn(null);

        // when
        Long actual = underTest.findBrandCampaignByShopId(SHOP_ID, PARENT_CAMPAIGN_ID);

        // then
        assertEquals(DEFAULT_CAMPAIGN_ID, actual);
    }

    @Test
    public void testFindCampaignByShouldReturnDefaultCampaignIdWhenShopIdIsEmpty() {
        // when
        Long actual = underTest.findCampaignBy(EMPTY);

        // then
        assertEquals(DEFAULT_CAMPAIGN_ID, actual);
    }

    @Test
    public void testFindCampaignByShouldReturnCampaignIdFromMapperWhenShopIdIsValid() {
        // given
        Long expectedCampaignId = 456L;
        when(apiKeyNameMapper.findCampaignBy(SHOP_ID)).thenReturn(expectedCampaignId);

        // when
        Long actual = underTest.findCampaignBy(SHOP_ID);

        // then
        assertEquals(expectedCampaignId, actual);
    }

    @Test
    public void testIsBrandCampaignShouldReturnTrueWhenCampaignIdIsNotNullAndZeroAndCampaignTypeIsBrand() {
        // given
        doReturn(BRAND).when(underTest).findCampaignType(CAMPAIGN_ID);

        // when
        boolean actual = underTest.isBrandCampaign(CAMPAIGN_ID);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsBrandCampaignShouldReturnFalseWhenCampaignIdIsNotNullAndZeroAndCampaignTypeIssRegular() {
        // given
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);

        // when
        boolean actual = underTest.isBrandCampaign(CAMPAIGN_ID);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsBrandCampaignShouldReturnFalseWhenCampaignIdIsNotNullAndZeroAndCampaignTypeIssKol() {
        // given
        doReturn(KOL).when(underTest).findCampaignType(CAMPAIGN_ID);

        // when
        boolean actual = underTest.isBrandCampaign(CAMPAIGN_ID);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsBrandCampaignShouldReturnFalseWhenCampaignIdIsNull() {
        // when
        boolean actual = underTest.isBrandCampaign(null);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsBrandCampaignShouldReturnFalseWhenCampaignIdIsZero() {
        // when
        boolean actual = underTest.isBrandCampaign(DEFAULT_CAMPAIGN_ID);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsBrandCampaignShouldReturnFalseWhenCampaignIdIsNegative() {
        // when
        boolean actual = underTest.isBrandCampaign(-1L);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsBrandItemWithShopIdShouldReturnTrueWhenShopIdIsValid() {
        // given
        when(apiKeyNameMapper.isBrandItem(SHOP_ID)).thenReturn(true);

        // when
        boolean actual = underTest.isBrandItem(SHOP_ID);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsBrandItemWithShopIdShouldReturnFalseWhenShopIdIsValid() {
        // given
        when(apiKeyNameMapper.isBrandItem(SHOP_ID)).thenReturn(false);

        // when
        boolean actual = underTest.isBrandItem(SHOP_ID);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsBrandItemWithShopIdShouldReturnFalseWhenShopIdIsNull() {
        // when
        boolean actual = underTest.isBrandItem(null);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsBrandItemWithShopIdShouldReturnFalseWhenShopIdIsEmpty() {
        // when
        boolean actual = underTest.isBrandItem(EMPTY);

        // then
        assertFalse(actual);
        verify(apiKeyNameMapper, never()).isBrandItem(anyString());
    }

    @Test
    public void testIsValidCommissionShouldReturnFalseWhenCommissionIsNull() {
        // when
        boolean actual = underTest.isValidCommission(null);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsValidCommissionShouldReturnFalseWhenCommissionIsZero() {
        // when
        boolean actual = underTest.isValidCommission(ZERO);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsValidCommissionShouldReturnFalseWhenCommissionIsGreaterThanZero() {
        // when
        boolean actual = underTest.isValidCommission(BigDecimal.valueOf(1.01));

        // then
        assertTrue(actual);
    }

    @Test
    public void testCreateExtraBonusesShouldCreateOnlySubConversionBonusWhenMainCampaignIsBrandAndSubCampaignIsKol() {
        // given
        ShopeeItem testShopeeItem = createTestShopeeItem(TEST_ITEM_SELLER_COMMISSION,
                TEST_GROSS_BRAND_COMMISSION);
        ShopeeOrder testShopeeOrder = createTestShopeeOrder(testShopeeItem);
        List<ConversionRegistrationDetails> brandItemConversions =
                createBrandItemConversionsWithKolSubConversion();

        setupCommonExtraBonusesMocking(testShopeeItem, testShopeeOrder);
        setupMainCampaignMocking(true);
        setupSubCampaignMocking(TEST_KOL_SUB_CAMPAIGN_ID, false);

        // when
        List<ConversionRegistrationDetails> actual = underTest.createExtraBonuses(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_CLICK_ID,
                testShopeeItem, testShopeeOrder, brandItemConversions);

        // then
        assertExtraBonusResultContainsOnlySubConversion(actual, TEST_KOL_SUB_CAMPAIGN_ID);
    }

    @Test
    public void testCreateExtraBonusesShouldReturnEmptyWhenBothMainCampaignAndSubCampaignAreBrand() {
        // given
        ShopeeItem testShopeeItem = createTestShopeeItem(TEST_ITEM_SELLER_COMMISSION,
                TEST_GROSS_BRAND_COMMISSION);
        ShopeeOrder testShopeeOrder = createTestShopeeOrder(testShopeeItem);
        List<ConversionRegistrationDetails> brandItemConversions =
                createBrandItemConversionsWithBrandSubConversion();
        doReturn(true).when(underTest)
                .isValidCommission(TEST_ITEM_SELLER_COMMISSION);
        doReturn(TEST_MAIN_CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(true).when(underTest).isBrandCampaign(TEST_MAIN_CAMPAIGN_ID);
        doReturn(true).when(underTest).isBrandCampaign(TEST_BRAND_SUB_CAMPAIGN_ID);
        doReturn(TEST_EXPECTED_PRODUCT_ID).when(underTest)
                .createExtraBonusProductId(testShopeeItem, testShopeeOrder,
                        TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION);
        doReturn(INDONESIA_COUNTRY_CODE).when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createExtraBonuses(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_CLICK_ID,
                testShopeeItem, testShopeeOrder, brandItemConversions);

        // then
        assertExtraBonusResultIsEmpty(actual);
    }

    @Test
    public void testCreateExtraBonusesShouldCreateOnlyMainConversionBonusWhenMainCampaignIsKolAndSubCampaignIsBrand() {
        // given
        ShopeeItem testShopeeItem = createTestShopeeItem(TEST_ITEM_SELLER_COMMISSION,
                TEST_GROSS_BRAND_COMMISSION);
        ShopeeOrder testShopeeOrder = createTestShopeeOrder(testShopeeItem);
        List<ConversionRegistrationDetails> brandItemConversions =
                createBrandItemConversionsWithBrandSubConversion();

        setupCommonExtraBonusesMocking(testShopeeItem, testShopeeOrder);
        setupMainCampaignMocking(false);
        setupSubCampaignMocking(TEST_BRAND_SUB_CAMPAIGN_ID, true);

        // when
        List<ConversionRegistrationDetails> actual = underTest.createExtraBonuses(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_CLICK_ID,
                testShopeeItem, testShopeeOrder, brandItemConversions);

        // then
        assertExtraBonusResultContainsOnlyMainConversion(actual);
    }

    @Test
    public void testCreateExtraBonusesShouldReturnEmptyListWhenCommissionIsInvalid() {
        // given
        ShopeeItem testShopeeItem = createTestShopeeItem(BigDecimal.ZERO,
                BigDecimal.ZERO);
        ShopeeOrder testShopeeOrder = createTestShopeeOrder(testShopeeItem);
        List<ConversionRegistrationDetails> brandItemConversions =
                createBrandItemConversionsWithKolSubConversion();
        doReturn(false).when(underTest).isValidCommission(BigDecimal.ZERO);
        doReturn(INDONESIA_COUNTRY_CODE).when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createExtraBonuses(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_CLICK_ID,
                testShopeeItem, testShopeeOrder, brandItemConversions);

        // then
        assertExtraBonusResultIsEmpty(actual);
    }

    @Test
    public void testCreateExtraBonusesShouldCreateOnlyMainConversionBonusWhenSubConversionsListIsEmpty() {
        // given
        ShopeeItem testShopeeItem = createTestShopeeItem(TEST_ITEM_SELLER_COMMISSION, TEST_GROSS_BRAND_COMMISSION);
        ShopeeOrder testShopeeOrder = createTestShopeeOrder(testShopeeItem);
        List<ConversionRegistrationDetails> emptyBrandItemConversions = new ArrayList<>();
        doReturn(true).when(underTest).isValidCommission(TEST_ITEM_SELLER_COMMISSION);
        doReturn(TEST_MAIN_CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(false).when(underTest).isBrandCampaign(TEST_MAIN_CAMPAIGN_ID);
        doReturn(TEST_EXPECTED_PRODUCT_ID).when(underTest)
                .createExtraBonusProductId(testShopeeItem, testShopeeOrder, TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION);
        doReturn(INDONESIA_COUNTRY_CODE).when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createExtraBonuses(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_CLICK_ID,
                testShopeeItem, testShopeeOrder, emptyBrandItemConversions);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        ConversionRegistrationDetails result = actual.get(0);
        assertFalse(result instanceof ClickConversionRegistrationDetails);
        assertExtraBonusCommonDetails(result);
    }

    @Test
    public void testCreateExtraBonusesShouldFilterBrandSubConversionsWhenMultipleSubConversionsWithMixedCampaignTypes() {
        // given
        ShopeeItem testShopeeItem = createTestShopeeItem(TEST_ITEM_SELLER_COMMISSION,
                TEST_GROSS_BRAND_COMMISSION);
        ShopeeOrder testShopeeOrder = createTestShopeeOrder(testShopeeItem);
        List<ConversionRegistrationDetails> brandItemConversions =
                createMultipleSubConversions();
        doReturn(true).when(underTest).isValidCommission(TEST_ITEM_SELLER_COMMISSION);
        doReturn(TEST_MAIN_CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(false).when(underTest).isBrandCampaign(TEST_MAIN_CAMPAIGN_ID);
        doReturn(false).when(underTest).isBrandCampaign(TEST_KOL_SUB_CAMPAIGN_ID);
        doReturn(true).when(underTest).isBrandCampaign(TEST_BRAND_SUB_CAMPAIGN_ID);
        doReturn(TEST_EXPECTED_PRODUCT_ID).when(underTest)
                .createExtraBonusProductId(testShopeeItem, testShopeeOrder,
                        TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION);
        doReturn(INDONESIA_COUNTRY_CODE).when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createExtraBonuses(
                TEST_CONVERSION_TIME, TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID, TEST_CLICK_ID,
                testShopeeItem, testShopeeOrder, brandItemConversions);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());

        ConversionRegistrationDetails mainResult = actual.get(0);
        assertFalse(mainResult instanceof ClickConversionRegistrationDetails);
        ConversionRegistrationDetails subResult = actual.get(1);
        assertTrue(subResult instanceof ClickConversionRegistrationDetails);
        ClickConversionRegistrationDetails clickSubResult =
                (ClickConversionRegistrationDetails) subResult;
        assertEquals(TEST_KOL_SUB_CAMPAIGN_ID, clickSubResult.getSubCampaignId());
    }

    @Test
    public void testFindCampaignByShouldReturnDefaultCampaignIdWhenShopIdIsNull() {
        // when
        Long actual = underTest.findCampaignBy(null);

        // then
        assertEquals(DEFAULT_CAMPAIGN_ID, actual);
    }

    @Test
    public void testCreateBrandSubConversionsShouldReturnMainAndSubConversionsWhenValidSubCampaignId() {
        // given
        ConversionRegistrationDetails mainConversion = createTestMainConversion();
        doReturn(SUB_CAMPAIGN_ID).when(underTest)
                .getBrandSubCampaignIdFrom(BRAND, SHOP_ID);

        // when
        List<ConversionRegistrationDetails> actual = underTest.createBrandSubConversions(
                mainConversion, BRAND, SHOP_ID);

        // then
        assertEquals(2, actual.size());
        assertEquals(mainConversion, actual.get(0));

        assertTrue(actual.get(1) instanceof ClickConversionRegistrationDetails);
        ClickConversionRegistrationDetails subConversion =
                (ClickConversionRegistrationDetails) actual.get(1);
        assertEquals(SUB_CAMPAIGN_ID, subConversion.getSubCampaignId());

        verify(underTest).getBrandSubCampaignIdFrom(BRAND, SHOP_ID);
    }

    @Test
    public void testCreateBrandSubConversionsShouldReturnOnlyMainConversionWhenSubCampaignIdIsNull() {
        // given
        ConversionRegistrationDetails mainConversion = createTestMainConversion();
        doReturn(null).when(underTest).getBrandSubCampaignIdFrom(BRAND, SHOP_ID);

        // when
        List<ConversionRegistrationDetails> actual = underTest.createBrandSubConversions(
                mainConversion, BRAND, SHOP_ID);

        // then
        assertEquals(1, actual.size());
        assertEquals(mainConversion, actual.get(0));
        verify(underTest).getBrandSubCampaignIdFrom(BRAND, SHOP_ID);
    }

    @Test
    public void testCreateBrandSubConversionsShouldReturnOnlyMainConversionWhenSubCampaignIdIsZero() {
        // given
        ConversionRegistrationDetails mainConversion = createTestMainConversion();
        doReturn(0L).when(underTest).getBrandSubCampaignIdFrom(BRAND, SHOP_ID);

        // when
        List<ConversionRegistrationDetails> actual = underTest.createBrandSubConversions(
                mainConversion, BRAND, SHOP_ID);

        // then
        assertEquals(1, actual.size());
        assertEquals(mainConversion, actual.get(0));
        verify(underTest).getBrandSubCampaignIdFrom(BRAND, SHOP_ID);
    }

    @Test
    public void testCreateCrossBrandSubConversionsShouldReturnBothTargetBrandAndKolConversionsWhenBothExist() {
        // given
        ConversionRegistrationDetails mainConversion = createTestMainConversion();
        Long targetBrandCampaignId = 456L;
        Long kolCampaignId = 789L;

        doReturn(targetBrandCampaignId).when(underTest).findCampaignBy(SHOP_ID);
        doReturn(kolCampaignId).when(underTest)
                .getAdvParentCampaignId(targetBrandCampaignId);

        // when
        List<ConversionRegistrationDetails> actual = underTest
                .createCrossBrandSubConversions(mainConversion, SHOP_ID);

        // then
        assertEquals(2, actual.size());

        assertTrue(actual.get(0) instanceof ClickConversionRegistrationDetails);
        ClickConversionRegistrationDetails targetBrandConversion =
                (ClickConversionRegistrationDetails) actual.get(0);
        assertEquals(targetBrandCampaignId, targetBrandConversion.getSubCampaignId());
        assertTrue(actual.get(1) instanceof ClickConversionRegistrationDetails);
        ClickConversionRegistrationDetails kolConversion =
                (ClickConversionRegistrationDetails) actual.get(1);
        assertEquals(kolCampaignId, kolConversion.getSubCampaignId());
    }

    @Test
    public void testCreateCrossBrandSubConversionsShouldReturnEmptyWhenTargetBrandCampaignIdIsNull() {
        // given
        ConversionRegistrationDetails mainConversion = createTestMainConversion();
        doReturn(null).when(underTest).findCampaignBy(SHOP_ID);

        // when
        List<ConversionRegistrationDetails> actual = underTest.createCrossBrandSubConversions(
                mainConversion, SHOP_ID);

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testCreateCrossBrandSubConversionsShouldReturnEmptyWhenTargetBrandCampaignIdIsZero() {
        // given
        ConversionRegistrationDetails mainConversion = createTestMainConversion();
        doReturn(0L).when(underTest).findCampaignBy(SHOP_ID);

        // when
        List<ConversionRegistrationDetails> actual = underTest
                .createCrossBrandSubConversions(mainConversion, SHOP_ID);

        // then
        assertTrue(actual.isEmpty());

        verify(underTest).findCampaignBy(SHOP_ID);
    }

    private ShopeeItem createTestShopeeItem(BigDecimal itemSellerCommission,
            BigDecimal grossBrandCommission) {
        return new ShopeeItem(TEST_ITEM_ID, SHOP_ID, null, 1,
                ITEM_PRICE, ITEM_PRICE, ITEM_COMMISSION, grossBrandCommission,
                itemSellerCommission, TEST_MODEL_ID, TEST_CATEGORY_NAME, null,
                TEST_SUBCATEGORY_NAME, ORDERED_IN_SAME_SHOP);
    }

    private ShopeeOrder createTestShopeeOrder(ShopeeItem shopeeItem) {
        return new ShopeeOrder(TEST_ORDER_ID, TEST_SHOP_TYPE, Arrays.asList(shopeeItem));
    }

    private List<ConversionRegistrationDetails> createBrandItemConversionsWithSubConversion() {
        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>();
        ClickConversionRegistrationDetails subConversionDetails =
                new ClickConversionRegistrationDetails(TEST_CONVERSION_TIME,
                        TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                        TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID,
                        TEST_SUB_CONVERSION_AMOUNT, TEST_CLICK_ID, TEST_SUB_CAMPAIGN_ID);
        brandItemConversions.add(subConversionDetails);
        return brandItemConversions;
    }

    private List<ConversionRegistrationDetails> createBrandItemConversionsWithKolSubConversion() {
        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>();
        ClickConversionRegistrationDetails kolSubConversion =
                new ClickConversionRegistrationDetails(TEST_CONVERSION_TIME,
                        TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                        TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID,
                        TEST_SUB_CONVERSION_AMOUNT, TEST_CLICK_ID, TEST_KOL_SUB_CAMPAIGN_ID);
        brandItemConversions.add(kolSubConversion);
        return brandItemConversions;
    }

    private List<ConversionRegistrationDetails> createBrandItemConversionsWithBrandSubConversion() {
        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>();
        ClickConversionRegistrationDetails brandSubConversion =
                new ClickConversionRegistrationDetails(TEST_CONVERSION_TIME,
                        TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                        TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID,
                        TEST_SUB_CONVERSION_AMOUNT, TEST_CLICK_ID, TEST_BRAND_SUB_CAMPAIGN_ID);
        brandItemConversions.add(brandSubConversion);
        return brandItemConversions;
    }

    private List<ConversionRegistrationDetails> createMultipleSubConversions() {
        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>();
        ClickConversionRegistrationDetails kolSubConversion =
                new ClickConversionRegistrationDetails(TEST_CONVERSION_TIME,
                        TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                        TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID,
                        TEST_SUB_CONVERSION_AMOUNT, TEST_CLICK_ID, TEST_KOL_SUB_CAMPAIGN_ID);
        brandItemConversions.add(kolSubConversion);
        ClickConversionRegistrationDetails brandSubConversion =
                new ClickConversionRegistrationDetails(TEST_CONVERSION_TIME,
                        TEST_IDENTIFIER, TEST_RESULT_ID, TEST_CUSTOMER_TYPE,
                        TEST_BRAND_COMMISSION_CATEGORY, TEST_PRODUCT_ID,
                        TEST_SUB_CONVERSION_AMOUNT, TEST_CLICK_ID, TEST_BRAND_SUB_CAMPAIGN_ID);
        brandItemConversions.add(brandSubConversion);

        return brandItemConversions;
    }

    private void assertExtraBonusCommonDetails(ConversionRegistrationDetails actual) {
        assertEquals(TEST_CONVERSION_TIME, actual.getConversionTime());
        assertEquals(TEST_IDENTIFIER, actual.getTransactionId());
        assertEquals(TEST_RESULT_ID, actual.getResultId());
        assertEquals(TEST_CUSTOMER_TYPE, actual.getCustomerType());
        assertEquals(TEST_BRAND_COMMISSION_CATEGORY, actual.getProductCategoryId());
        assertEquals(TEST_CLICK_ID, actual.getClickId());
    }

    private ShopeeItem createValidShopeeItemForExtraBonus() {
        return createShopeeItemWithCommissions(TEST_ITEM_SELLER_COMMISSION,
                TEST_GROSS_BRAND_COMMISSION);
    }

    private ShopeeItem createShopeeItemWithCommissions(BigDecimal itemSellerCommission,
            BigDecimal grossBrandCommission) {
        return new ShopeeItem(TEST_ITEM_ID, SHOP_ID, null, 1,
                ITEM_PRICE, ITEM_PRICE, ITEM_COMMISSION, grossBrandCommission,
                itemSellerCommission, TEST_MODEL_ID, TEST_CATEGORY_NAME, null,
                TEST_SUBCATEGORY_NAME, ORDERED_IN_SAME_SHOP);
    }

    private ShopeeOrder createValidShopeeOrderForExtraBonus() {
        return new ShopeeOrder(TEST_ORDER_ID, TEST_SHOP_TYPE,
                Arrays.asList(createValidShopeeItemForExtraBonus()));
    }

    private void assertExtraBonusResultIsEmpty(
            List<ConversionRegistrationDetails> actual) {
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
    }

    private void assertExtraBonusResultContainsOnlyMainConversion(
            List<ConversionRegistrationDetails> actual) {
        assertNotNull(actual);
        assertEquals(1, actual.size());
        ConversionRegistrationDetails result = actual.get(0);
        assertFalse(result instanceof ClickConversionRegistrationDetails);
        assertExtraBonusCommonDetails(result);
    }

    private void assertExtraBonusResultContainsOnlySubConversion(
            List<ConversionRegistrationDetails> actual,
            Long expectedSubCampaignId) {
        assertNotNull(actual);
        assertEquals(1, actual.size());
        ConversionRegistrationDetails result = actual.get(0);
        assertTrue(result instanceof ClickConversionRegistrationDetails);
        ClickConversionRegistrationDetails clickResult =
                (ClickConversionRegistrationDetails) result;
        assertEquals(expectedSubCampaignId, clickResult.getSubCampaignId());
        assertExtraBonusCommonDetails(result);
    }

    private void assertExtraBonusResultContainsMainAndSubConversions(
            List<ConversionRegistrationDetails> actual, Long expectedSubCampaignId) {
        assertNotNull(actual);
        assertEquals(2, actual.size());

        ConversionRegistrationDetails mainResult = actual.get(0);
        assertFalse(mainResult instanceof ClickConversionRegistrationDetails);
        assertExtraBonusCommonDetails(mainResult);

        ConversionRegistrationDetails subResult = actual.get(1);
        assertTrue(subResult instanceof ClickConversionRegistrationDetails);
        ClickConversionRegistrationDetails clickSubResult =
                (ClickConversionRegistrationDetails) subResult;
        assertEquals(expectedSubCampaignId, clickSubResult.getSubCampaignId());
        assertExtraBonusCommonDetails(subResult);
    }

    private void setupCommonExtraBonusesMocking(ShopeeItem testShopeeItem, ShopeeOrder testShopeeOrder) {
        doReturn(true).when(underTest).isValidCommission(TEST_ITEM_SELLER_COMMISSION);
        doReturn(TEST_EXPECTED_PRODUCT_ID).when(underTest)
                .createExtraBonusProductId(testShopeeItem, testShopeeOrder, TEST_PRODUCT_ID, TEST_ITEM_SELLER_COMMISSION);
        doReturn(INDONESIA_COUNTRY_CODE).when(underTest).getTargetCountryCode();
    }

    private void setupMainCampaignMocking(boolean isBrandCampaign) {
        doReturn(TEST_MAIN_CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(isBrandCampaign).when(underTest).isBrandCampaign(TEST_MAIN_CAMPAIGN_ID);
    }

    private void setupSubCampaignMocking(Long subCampaignId, boolean isBrandCampaign) {
        doReturn(isBrandCampaign).when(underTest).isBrandCampaign(subCampaignId);
    }

    private ConversionRegistrationDetails createTestMainConversion() {
        return new ConversionRegistrationDetails(TEST_CONVERSION_TIME, TEST_IDENTIFIER,
                TEST_RESULT_ID, TEST_CUSTOMER_TYPE, TEST_BRAND_COMMISSION_CATEGORY,
                TEST_PRODUCT_ID, TEST_ACTUAL_AMOUNT, TEST_CLICK_ID);
    }

    private ClickConversionRegistrationDetails createTestClickConversion(Long subCampaignId) {
        return new ClickConversionRegistrationDetails(TEST_CONVERSION_TIME, TEST_IDENTIFIER,
                TEST_RESULT_ID, TEST_CUSTOMER_TYPE, TEST_BRAND_COMMISSION_CATEGORY,
                TEST_PRODUCT_ID, TEST_ACTUAL_AMOUNT, TEST_CLICK_ID, subCampaignId);
    }
}
