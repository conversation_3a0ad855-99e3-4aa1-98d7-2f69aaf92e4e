/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.BonusReportDetail;
import jp.ne.interspace.taekkyeon.model.BonusReportExportRequest;
import jp.ne.interspace.taekkyeon.model.BonusStatus;
import jp.ne.interspace.taekkyeon.model.ConversionBonusReportDetail;
import jp.ne.interspace.taekkyeon.model.ReportExportPagedDetails;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.BonusMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionMapper;

import static jp.ne.interspace.taekkyeon.model.BonusStatus.APPROVED;
import static jp.ne.interspace.taekkyeon.model.BonusStatus.PENDING;
import static jp.ne.interspace.taekkyeon.model.BonusType.CONVERSION;
import static jp.ne.interspace.taekkyeon.model.BonusType.FIXED;
import static jp.ne.interspace.taekkyeon.model.ReportQueryPeriodBase.CONFIRMATION_DATE;
import static jp.ne.interspace.taekkyeon.model.ReportQueryPeriodBase.OCCURRENCE_DATE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anySetOf;
import static org.mockito.Matchers.anyListOf;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link BonusReportExporter}.
 *
 * <AUTHOR> Nguyen
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class BonusReportExporterTest {

    private static final String COUNTRY_CODE = "VN";
    private static final ZonedDateTime FROM_DATE = ZonedDateTime
            .parse("2022-10-10T15:00:00.000+07:00");
    private static final ZonedDateTime TO_DATE = ZonedDateTime
            .parse("2022-11-11T18:00:00.000+07:00");
    private static final Set<Long> CAMPAIGN_IDS = Sets.newHashSet(1L, 2L, 3L);
    private static final Set<Long> SITE_IDS = Sets.newHashSet(1L, 2L, 3L);
    private static final Set<Long> BONUS_SETTING_IDS = Sets.newHashSet(1L, 2L, 3L);
    private static final List<BonusStatus> BONUS_STATUSES = Arrays
            .asList(PENDING, APPROVED);
    private static final int ORACLE_IN_LIMIT = 10;

    private static final LocalDateTime UTC_FROM_DATE = ZonedDateTime
            .parse("2022-10-09T17:00:00.000+07:00").toLocalDateTime();
    private static final LocalDateTime UTC_TO_DATE = ZonedDateTime
            .parse("2022-11-11T16:59:59.000+07:00").toLocalDateTime();
    private static final LocalDateTime FROM_LOCAL_DATE_TIME = LocalDateTime
            .of(2022, 10, 10, 0, 0, 0);
    private static final LocalDateTime TO_LOCAL_DATE_TIME = LocalDateTime
            .of(2022, 11, 11, 23, 59, 59);

    @Spy @InjectMocks
    private BonusReportExporter underTest;

    @Mock
    private BonusMapper bonusMapper;

    @Mock
    private ConversionMapper conversionMapper;

    @Test
    public void testFindReportDataShouldCallFindFixedBonusReportWhenCalled() {
        // given
        BonusReportExportRequest request = new BonusReportExportRequest(COUNTRY_CODE,
                FROM_DATE, TO_DATE, SITE_IDS, CAMPAIGN_IDS, CONFIRMATION_DATE,
                BONUS_SETTING_IDS, BONUS_STATUSES, FIXED);
        doReturn(ORACLE_IN_LIMIT).when(underTest).getOracleInLimit();
        Integer pageNumber = 10;
        List<ConversionBonusReportDetail> expectedReportItems = Collections
                .emptyList();

        doReturn(expectedReportItems).when(underTest)
                .findFixedBonus(COUNTRY_CODE, CONFIRMATION_DATE, SITE_IDS, CAMPAIGN_IDS,
                        BONUS_SETTING_IDS, BONUS_STATUSES, FROM_DATE, TO_DATE, pageNumber,
                        ORACLE_IN_LIMIT);

        // when
        ReportExportPagedDetails<ConversionBonusReportDetail, Integer> actual = underTest
                .findReportData(request, pageNumber);

        // then
        assertNotNull(actual);
        assertEquals(expectedReportItems, actual.getReportItems());
        assertEquals(11, actual.getPaginationData().intValue());
        verify(underTest, never())
                .findConversionBonus(any(), any(), any(), any(), any(), any(), any(),
                        any(), anyInt(), anyInt());
    }

    @Test
    public void testFindReportDataShouldCallFindConversionBonusWhenCalled() {
        // given
        BonusReportExportRequest request = new BonusReportExportRequest(COUNTRY_CODE,
                FROM_DATE, TO_DATE, SITE_IDS, CAMPAIGN_IDS, CONFIRMATION_DATE,
                BONUS_SETTING_IDS, BONUS_STATUSES, CONVERSION);
        doReturn(ORACLE_IN_LIMIT).when(underTest).getOracleInLimit();
        Integer pageNumber = 10;
        List<ConversionBonusReportDetail> expectedReportItems = Collections
                .emptyList();

        doReturn(expectedReportItems).when(underTest).findConversionBonus(COUNTRY_CODE,
                CONFIRMATION_DATE, SITE_IDS, CAMPAIGN_IDS, BONUS_SETTING_IDS,
                BONUS_STATUSES, FROM_LOCAL_DATE_TIME, TO_LOCAL_DATE_TIME, pageNumber,
                ORACLE_IN_LIMIT);

        // when
        ReportExportPagedDetails<ConversionBonusReportDetail, Integer> actual = underTest
                .findReportData(request, pageNumber);

        // then
        assertNotNull(actual);
        assertEquals(expectedReportItems, actual.getReportItems());
        assertEquals(11, actual.getPaginationData().intValue());
        verify(underTest, never())
                .findFixedBonus(any(), any(), any(), any(), any(), any(), any(),
                        any(), anyInt(), anyInt());
    }

    @Test
    public void testFindFixedBonusShouldReturnCorrectDataWhenGivenPeriodBaseIsConfirmationDate() {
        // given
        List<BonusReportDetail> expected = Collections
                .singletonList(mock(BonusReportDetail.class));
        when(underTest.createFromDateTimeBy(FROM_DATE)).thenReturn(UTC_FROM_DATE);
        when(underTest.createToDateTimeBy(TO_DATE)).thenReturn(UTC_TO_DATE);
        Integer pageNumber = 10;
        when(bonusMapper
                .findFixedBonusByConfirmationDate(COUNTRY_CODE, SITE_IDS, CAMPAIGN_IDS,
                        BONUS_SETTING_IDS, BONUS_STATUSES, UTC_FROM_DATE, UTC_TO_DATE,
                        pageNumber, ORACLE_IN_LIMIT)).thenReturn(expected);
        // when
        List<BonusReportDetail> actual = underTest
                .findFixedBonus(COUNTRY_CODE, CONFIRMATION_DATE, SITE_IDS, CAMPAIGN_IDS,
                        BONUS_SETTING_IDS, BONUS_STATUSES, FROM_DATE, TO_DATE, pageNumber,
                        ORACLE_IN_LIMIT);

        // then
        assertSame(expected, actual);
        verify(bonusMapper, never()).findFixedBonusByCreatedOn(any(String.class),
                anySetOf(Long.class), anySetOf(Long.class), anySetOf(Long.class),
                anyListOf(BonusStatus.class), any(LocalDateTime.class),
                any(LocalDateTime.class), any(int.class), any(int.class));
    }

    @Test
    public void testFindFixedBonusShouldReturnCorrectDataWhenGivenPeriodBaseIsOccurrenceDate() {
        // given
        List<BonusReportDetail> expected = Collections
                .singletonList(mock(BonusReportDetail.class));
        when(underTest.createFromDateTimeUtcBy(FROM_DATE)).thenReturn(UTC_FROM_DATE);
        when(underTest.createToDateTimeUTCBy(TO_DATE)).thenReturn(UTC_TO_DATE);
        Integer pageNumber = 10;
        when(bonusMapper
                .findFixedBonusByCreatedOn(COUNTRY_CODE, SITE_IDS, CAMPAIGN_IDS,
                        BONUS_SETTING_IDS, BONUS_STATUSES, UTC_FROM_DATE, UTC_TO_DATE, pageNumber,
                        ORACLE_IN_LIMIT)).thenReturn(expected);
        // when
        List<BonusReportDetail> actual = underTest
                .findFixedBonus(COUNTRY_CODE, OCCURRENCE_DATE, SITE_IDS, CAMPAIGN_IDS,
                        BONUS_SETTING_IDS, BONUS_STATUSES, FROM_DATE, TO_DATE, pageNumber,
                        ORACLE_IN_LIMIT);

        // then
        assertSame(expected, actual);
        verify(bonusMapper, never()).findFixedBonusByConfirmationDate(any(String.class),
                anySetOf(Long.class), anySetOf(Long.class), anySetOf(Long.class),
                anyListOf(BonusStatus.class), any(LocalDateTime.class),
                any(LocalDateTime.class), any(int.class),
                any(int.class));
    }

    @Test
    public void testFindConversionBonusShouldReturnCorrectDataWhenGivenPeriodBaseIsConfirmationDate() {
        // given
        List<ConversionBonusReportDetail> expected = Collections
                .singletonList(mock(ConversionBonusReportDetail.class));
        Integer pageNumber = 10;
        when(conversionMapper.findConversionBonusByConfirmationDate(COUNTRY_CODE,
                SITE_IDS, CAMPAIGN_IDS, BONUS_SETTING_IDS, BONUS_STATUSES,
                FROM_LOCAL_DATE_TIME, TO_LOCAL_DATE_TIME, pageNumber, ORACLE_IN_LIMIT))
                        .thenReturn(expected);
        // when
        List<ConversionBonusReportDetail> actual = underTest.findConversionBonus(
                COUNTRY_CODE, CONFIRMATION_DATE, SITE_IDS, CAMPAIGN_IDS,
                BONUS_SETTING_IDS, BONUS_STATUSES, FROM_LOCAL_DATE_TIME,
                TO_LOCAL_DATE_TIME, pageNumber, ORACLE_IN_LIMIT);

        // then
        assertSame(expected, actual);
        verify(conversionMapper, never()).findConversionBonusByConversionDate(
                any(String.class), anySetOf(Long.class), anySetOf(Long.class),
                anySetOf(Long.class), anyListOf(BonusStatus.class),
                any(LocalDateTime.class), any(LocalDateTime.class), any(int.class),
                any(int.class));
    }

    @Test
    public void testFindConversionBonusShouldReturnCorrectDataWhenGivenPeriodBaseIsOccurrenceDate() {
        // given
        List<ConversionBonusReportDetail> expected = Collections
                .singletonList(mock(ConversionBonusReportDetail.class));
        Integer pageNumber = 10;
        when(conversionMapper.findConversionBonusByConversionDate(COUNTRY_CODE, SITE_IDS,
                CAMPAIGN_IDS, BONUS_SETTING_IDS, BONUS_STATUSES, FROM_LOCAL_DATE_TIME,
                TO_LOCAL_DATE_TIME, pageNumber, ORACLE_IN_LIMIT)).thenReturn(expected);
        // when
        List<ConversionBonusReportDetail> actual = underTest.findConversionBonus(
                COUNTRY_CODE, OCCURRENCE_DATE, SITE_IDS, CAMPAIGN_IDS, BONUS_SETTING_IDS,
                BONUS_STATUSES, FROM_LOCAL_DATE_TIME, TO_LOCAL_DATE_TIME, pageNumber,
                ORACLE_IN_LIMIT);

        // then
        assertSame(expected, actual);
        verify(conversionMapper, never()).findConversionBonusByConfirmationDate(
                (any(String.class)), anySetOf(Long.class), anySetOf(Long.class),
                anySetOf(Long.class), anyListOf(BonusStatus.class),
                any(LocalDateTime.class), any(LocalDateTime.class), any(int.class),
                any(int.class));
    }

    @Test
    public void testCreateFromDateTimeByShouldReturnCorrectDataWhenGivenTheZoneDateTime() {
        // when
        LocalDateTime actual = underTest.createFromDateTimeBy(FROM_DATE);

        // then
        assertEquals(FROM_LOCAL_DATE_TIME, actual);
    }

    @Test
    public void testCreateToDateTimeByShouldReturnCorrectDataWhenGivenTheZoneDateTime() {
        // when
        LocalDateTime actual = underTest.createToDateTimeBy(TO_DATE);

        // then
        assertEquals(TO_LOCAL_DATE_TIME, actual);
    }

    @Test
    public void testCreateFromDateTimeUtcByShouldReturnCorrectDataWhenGivenTheLocalDate() {
        // when
        LocalDateTime actual = underTest.createFromDateTimeUtcBy(FROM_DATE);

        // then
        assertEquals(UTC_FROM_DATE, actual);
    }

    @Test
    public void testCreateToDateTimeUtcByShouldReturnCorrectDataWhenGivenTheLocalDate() {
        // when
        LocalDateTime actual = underTest.createToDateTimeUTCBy(TO_DATE);

        // then
        assertEquals(UTC_TO_DATE, actual);
    }
}
