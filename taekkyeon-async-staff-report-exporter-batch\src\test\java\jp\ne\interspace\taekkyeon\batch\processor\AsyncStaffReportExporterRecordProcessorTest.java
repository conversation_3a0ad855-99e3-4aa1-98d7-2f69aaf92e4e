/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.processor;

import java.io.IOException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.common.collect.Sets;
import com.google.gson.JsonSyntaxException;
import org.apache.ibatis.session.SqlSessionManager;
import org.easybatch.core.record.Header;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.BaseReportExportRequest;
import jp.ne.interspace.taekkyeon.model.CampaignReportExportRequest;
import jp.ne.interspace.taekkyeon.model.CampaignReportItem;
import jp.ne.interspace.taekkyeon.model.CampaignStatus;
import jp.ne.interspace.taekkyeon.model.CampaignType;
import jp.ne.interspace.taekkyeon.model.ConversionReport;
import jp.ne.interspace.taekkyeon.model.ConversionReportExportRequest;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.PublisherType;
import jp.ne.interspace.taekkyeon.model.ReportExportItem;
import jp.ne.interspace.taekkyeon.model.ReportExportPagedDetails;
import jp.ne.interspace.taekkyeon.model.ReportExportRecord;
import jp.ne.interspace.taekkyeon.model.ReportExportRecordPayload;
import jp.ne.interspace.taekkyeon.model.ReportExportRequest;
import jp.ne.interspace.taekkyeon.model.ReportExportType;
import jp.ne.interspace.taekkyeon.model.SiteType;
import jp.ne.interspace.taekkyeon.model.SqsRecord;
import jp.ne.interspace.taekkyeon.model.SqsRecordPayload;
import jp.ne.interspace.taekkyeon.service.BaseReportCsvExporter;
import jp.ne.interspace.taekkyeon.service.BaseReportExporter;
import jp.ne.interspace.taekkyeon.service.CampaignReportCsvExporter;
import jp.ne.interspace.taekkyeon.service.CampaignReportExporter;
import jp.ne.interspace.taekkyeon.service.ConversionReportCsvExporter;
import jp.ne.interspace.taekkyeon.service.ConversionReportExporter;
import jp.ne.interspace.taekkyeon.service.FileService;
import jp.ne.interspace.taekkyeon.service.JsonSerializerService;

import static java.util.Collections.emptyList;
import static jp.ne.interspace.taekkyeon.model.CampaignStatus.RUNNING;
import static jp.ne.interspace.taekkyeon.model.CampaignType.CPA;
import static jp.ne.interspace.taekkyeon.model.PublisherType.UNSPECIFIED;
import static jp.ne.interspace.taekkyeon.model.ReportExportType.CAMPAIGN;
import static jp.ne.interspace.taekkyeon.model.ReportQueryPeriodBase.CONFIRMATION_DATE;
import static jp.ne.interspace.taekkyeon.model.SiteType.EMPTY;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link AsyncStaffReportExporterRecordProcessor}.
 *
 * <AUTHOR> Mayur
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class AsyncStaffReportExporterRecordProcessorTest {

    private static final String RECEIPT_HANDLE = "receiptHandle";
    private static final String MESSAGE = "message";
    private static final String COUNTRY_CODE = "ID";
    private static final ZonedDateTime FROM_DATE = ZonedDateTime
            .parse("2017-02-10T00:00:00.000+07:00");
    private static final ZonedDateTime TO_DATE = ZonedDateTime
            .parse("2017-02-11T00:00:00.000+07:00");
    private static final ConversionStatus CONVERSION_STATUS = ConversionStatus.APPROVED;

    private static final Set<Long> CAMPAIGN_IDS = Sets.newHashSet(4L);
    private static final Set<Long> SITE_IDS = Sets.newHashSet(6L);
    private static final Set<String> VERIFICATION_IDS = Sets.newHashSet("12345678");
    private static final Set<String> PRODUCT_IDS = Sets.newHashSet("A111222");
    private static final Set<Long> CREATIVE_IDS = Sets.newHashSet(999999L);
    private static final Set<Long> RESULT_IDS = Sets.newHashSet(5L);
    private static final Set<Long> RANKS = Sets.newHashSet(6L);
    private static final List<DeviceType> DEVICE_TYPES = Arrays.asList(DeviceType.UNKNOWN);
    private static final Set<Long> CONVERSION_IDS = Sets.newHashSet(6534L);

    private static final String REPORT_TYPE_NAME = "CAMPAIGN";
    private static final String CONDITION = "condition";
    private static final long STAFF_ID = 100;
    private static final long MERCHANT_ID = 4;
    private static final long PUBLISHER_ID = 4;
    private static final String LOCALE = "en";
    private static final String DEFAULT_LOCALE = "vn";

    private static final String ERROR_MESSAGE = "Failed to parse conversion: message";
    private static final Date CREATION_DATE = new Date(1);
    private static final String SOURCE = "source";
    private static final String FILE_NAME = "filename";
    private static final int PAGE_NUMBER_TWO = 2;
    private static final long MAX_LATEST_CONVERSION_ID = 0;
    private static final Set<Long> MERCHANT_IDS = Sets.newHashSet(MERCHANT_ID);
    private static final Set<Long> PUBLISHER_IDS = new HashSet<>(Arrays.asList(1L));
    private static final PublisherType PUBLISHER_TYPE = UNSPECIFIED;
    private static final List<SiteType> SITE_TYPES = Arrays.asList(EMPTY);
    private static final List<Long> SITE_CATEGORIES = Arrays.asList(1L);
    private static final List<CampaignType> CAMPAIGN_TYPES = Arrays.asList(CPA);
    private static final List<Long> CAMPAIGN_CATEGORIES = Arrays.asList(1L);
    private static final List<CampaignStatus> CAMPAIGN_STATUS = Arrays.asList(RUNNING);
    private static final long PUBLISHER_AGENCY_ID = 1;
    private static final Boolean IS_PENDING_FOR_MERCHANT_PAYMENT = true;

    private static final String REPORT_EXPORT_PREFIX = getCurrentCountry().getCode() + "-async-report-exporter-";

    @InjectMocks @Spy
    private AsyncStaffReportExporterRecordProcessor underTest;

    @Mock
    private JsonSerializerService jsonSerializerService;

    @Mock
    private Map<ReportExportType, Class<CampaignReportExportRequest>> reportTypeMapping;

    @Mock
    private Map<Class<? extends BaseReportExportRequest>,
            BaseReportExporter<? extends BaseReportExportRequest,
                    ? extends ReportExportItem, ?>> reportExporterMapping;

    @Mock
    private Map<Class<? extends BaseReportExportRequest>,
            BaseReportCsvExporter<? extends ReportExportItem>> csvExporterMapping;

    @Mock
    private Logger logger;

    @Mock
    private CampaignReportExporter campaignReportExporter;

    @Mock
    private ConversionReportExporter conversionReportExporter;

    @Mock
    private CampaignReportCsvExporter campaignCsvExporter;

    @Mock
    private ConversionReportCsvExporter conversionCsvExporter;

    @Mock
    private FileService fileService;

    @Test
    public void testProcessRecordShouldParseRecordCorrectlyWhenSqsMessageIsInCorrectFormat()
            throws Exception {
        // given
        Header header = new Header(1L, SOURCE, CREATION_DATE);
        SqsRecord sourceRecord = new SqsRecord(header,
                new SqsRecordPayload(RECEIPT_HANDLE, MESSAGE));

        ReportExportRequest reportExportRequest = new ReportExportRequest(CAMPAIGN,
                CONDITION, STAFF_ID, MERCHANT_ID, PUBLISHER_ID, LOCALE);
        when(jsonSerializerService.fromJson(MESSAGE, ReportExportRequest.class))
                .thenReturn(reportExportRequest);
        doReturn(DEFAULT_LOCALE).when(underTest).getLocaleBy(LOCALE);
        when(reportTypeMapping.get(CAMPAIGN))
                .thenReturn(CampaignReportExportRequest.class);
        CampaignReportExportRequest campaignReportExportRequest = new CampaignReportExportRequest(
                COUNTRY_CODE, FROM_DATE, TO_DATE, SITE_IDS, CAMPAIGN_IDS,
                CONFIRMATION_DATE, Arrays.asList(CONVERSION_STATUS), MERCHANT_IDS,
                PUBLISHER_IDS, PUBLISHER_TYPE, SITE_TYPES, SITE_CATEGORIES,
                CAMPAIGN_TYPES, CAMPAIGN_CATEGORIES, CAMPAIGN_STATUS,
                IS_PENDING_FOR_MERCHANT_PAYMENT);
        when(jsonSerializerService.fromJson(CONDITION, CampaignReportExportRequest.class))
                .thenReturn(campaignReportExportRequest);

        when(fileService
                .createFileName(REPORT_TYPE_NAME,
                        COUNTRY_CODE, CREATION_DATE.toInstant()
                                .atZone(ZoneId.systemDefault()).toLocalDate().toString()))
                .thenReturn(FILE_NAME);

        doNothing().when(underTest).writeDataToFile(REPORT_EXPORT_PREFIX + FILE_NAME,
                campaignReportExportRequest, DEFAULT_LOCALE);

        // when
        ReportExportRecord actual = underTest.processRecord(sourceRecord);

        // then
        assertNotNull(actual);

        assertSame(header, actual.getHeader());

        ReportExportRecordPayload actualPayload = actual.getPayload();
        assertNotNull(actualPayload);
        assertEquals(RECEIPT_HANDLE, actualPayload.getReceiptHandle());
        assertEquals(REPORT_TYPE_NAME, actualPayload.getReportType());
        assertEquals(COUNTRY_CODE, actualPayload.getCountryCode());
        assertEquals(REPORT_EXPORT_PREFIX + FILE_NAME, actualPayload.getFilename());
        assertEquals(STAFF_ID, actualPayload.getStaffId());
        assertEquals(MERCHANT_ID, actualPayload.getMerchantId());
        assertEquals(PUBLISHER_ID, actualPayload.getPublisherId());
        assertEquals(DEFAULT_LOCALE, actualPayload.getLocale());
    }

    @Test
    public void testProcessRecordShouldReturnErrorWhenParsingToCampaignReportExportRequestFails() {
        // given
        Header header = mock(Header.class);
        SqsRecord sourceRecord = new SqsRecord(header,
                new SqsRecordPayload(RECEIPT_HANDLE, MESSAGE));

        ReportExportRequest reportExportRequest = new ReportExportRequest(CAMPAIGN,
                CONDITION, STAFF_ID, MERCHANT_ID, PUBLISHER_ID, LOCALE);
        when(jsonSerializerService.fromJson(MESSAGE, ReportExportRequest.class))
                .thenReturn(reportExportRequest);
        when(reportTypeMapping.get(CAMPAIGN))
                .thenReturn(CampaignReportExportRequest.class);
        JsonSyntaxException expectedException = new JsonSyntaxException(
                "TESTING_ERROR_FLOW");
        when(jsonSerializerService.fromJson(CONDITION, CampaignReportExportRequest.class))
                .thenThrow(expectedException);

        doReturn(logger).when(underTest).getLogger();

        try {
            // when
            underTest.processRecord(sourceRecord);
            fail();

        } catch (Exception ex) {
            // then
            verify(logger).error(ERROR_MESSAGE, expectedException);
        }
    }

    @Test
    public void testWriteDataToFileShouldWriteCampaignReportDataWhenCalled() throws IOException {
        // given
        CampaignReportExportRequest campaignReportExportRequest = new CampaignReportExportRequest(
                COUNTRY_CODE, FROM_DATE, TO_DATE, SITE_IDS, CAMPAIGN_IDS,
                CONFIRMATION_DATE, Arrays.asList(CONVERSION_STATUS), MERCHANT_IDS,
                PUBLISHER_IDS, PUBLISHER_TYPE, SITE_TYPES, SITE_CATEGORIES,
                CAMPAIGN_TYPES, CAMPAIGN_CATEGORIES, CAMPAIGN_STATUS,
                IS_PENDING_FOR_MERCHANT_PAYMENT);
        doReturn(campaignReportExporter).when(reportExporterMapping)
                .get(CampaignReportExportRequest.class);
        List<CampaignReportItem> reports = new ArrayList<>();
        reports.add(createCampaignReportItem(1L));
        reports.add(createCampaignReportItem(2L));
        ReportExportPagedDetails<CampaignReportItem, Integer> firstPagedDetails =
                new ReportExportPagedDetails<>(reports, PAGE_NUMBER_TWO);

        ReportExportPagedDetails<CampaignReportItem, Integer> emptyPagedDetails =
                new ReportExportPagedDetails<>(emptyList(), null);

        when(campaignReportExporter.findReportData(campaignReportExportRequest,
                null)).thenReturn(firstPagedDetails);
        when(campaignReportExporter.findReportData(campaignReportExportRequest,
                PAGE_NUMBER_TWO)).thenReturn(emptyPagedDetails);

        when(reportTypeMapping.get(CAMPAIGN)).thenReturn(
                CampaignReportExportRequest.class);
        List<String> emptyCustomFields = emptyList();
        when(campaignReportExporter
                .findCustomFields(campaignReportExportRequest))
                        .thenReturn(emptyCustomFields);

        doReturn(campaignCsvExporter).when(csvExporterMapping)
                .get(CampaignReportExportRequest.class);
        when(campaignReportExporter.updateRequestBy(
                campaignReportExportRequest)).thenReturn(campaignReportExportRequest);
        doReturn(logger).when(underTest).getLogger();
        SqlSessionManager sqlSessionManager = mock(SqlSessionManager.class);
        doReturn(sqlSessionManager).when(underTest).getSqlSessionManager();

        // when
        underTest.writeDataToFile(FILE_NAME, campaignReportExportRequest, LOCALE);

        // then
        verify(campaignCsvExporter).writeHeader(emptyCustomFields, FILE_NAME,
                CONFIRMATION_DATE, LOCALE, SITE_IDS);
        verify(campaignCsvExporter).writeData(reports, CONFIRMATION_DATE, FILE_NAME,
                SITE_IDS);
    }

    @Test
    public void testWriteDataToFileShouldWriteConversionReportDataWhenCalled() throws IOException {
        // given
        ConversionReportExportRequest conversionReportExportRequest =
                new ConversionReportExportRequest(COUNTRY_CODE, FROM_DATE, TO_DATE,
                        SITE_IDS, CAMPAIGN_IDS, CONFIRMATION_DATE,
                        Arrays.asList(CONVERSION_STATUS), VERIFICATION_IDS,
                        PRODUCT_IDS, CREATIVE_IDS, RESULT_IDS, RANKS, DEVICE_TYPES,
                        CONVERSION_IDS, MERCHANT_IDS, PUBLISHER_IDS, PUBLISHER_AGENCY_ID,
                        PUBLISHER_TYPE, IS_PENDING_FOR_MERCHANT_PAYMENT);
        doReturn(conversionReportExporter).when(reportExporterMapping)
                .get(ConversionReportExportRequest.class);
        List<ConversionReport> conversionReports = new ArrayList<>();
        conversionReports.add(mock(ConversionReport.class));
        ReportExportPagedDetails<ConversionReport, Long> expectedPagedDetails =
                new ReportExportPagedDetails<>(conversionReports, MAX_LATEST_CONVERSION_ID);
        when(conversionReportExporter.findReportData(conversionReportExportRequest, null))
                .thenReturn(expectedPagedDetails);
        when(conversionReportExporter.findReportData(conversionReportExportRequest,
                MAX_LATEST_CONVERSION_ID)).thenReturn(
                        new ReportExportPagedDetails<>(emptyList(), null));
        doReturn(conversionCsvExporter).when(csvExporterMapping)
                .get(ConversionReportExportRequest.class);
        List<String> customFields = Arrays.asList("Header1", "Header2", "Header3");
        when(conversionReportExporter.findCustomFields(conversionReportExportRequest))
                .thenReturn(customFields);
        when(conversionReportExporter.updateRequestBy(conversionReportExportRequest))
                .thenReturn(conversionReportExportRequest);
        SqlSessionManager sqlSessionManager = mock(SqlSessionManager.class);
        doReturn(sqlSessionManager).when(underTest).getSqlSessionManager();

        // when
        underTest.writeDataToFile(FILE_NAME, conversionReportExportRequest, LOCALE);

        // then
        verify(conversionCsvExporter).writeHeader(customFields, FILE_NAME,
                CONFIRMATION_DATE, LOCALE, SITE_IDS);
        verify(conversionCsvExporter).writeData(conversionReports, CONFIRMATION_DATE,
                FILE_NAME, SITE_IDS);
    }

    @Test
    public void testWriteDataToFileShouldWriteErrorLogWhenErrorOccuredInCampaignReport()
            throws IOException {
        // given
        CampaignReportExportRequest campaignReportExportRequest = new CampaignReportExportRequest(
                COUNTRY_CODE, FROM_DATE, TO_DATE, SITE_IDS, CAMPAIGN_IDS,
                CONFIRMATION_DATE, Arrays.asList(CONVERSION_STATUS), MERCHANT_IDS,
                PUBLISHER_IDS, PUBLISHER_TYPE, SITE_TYPES, SITE_CATEGORIES,
                CAMPAIGN_TYPES, CAMPAIGN_CATEGORIES, CAMPAIGN_STATUS,
                IS_PENDING_FOR_MERCHANT_PAYMENT);
        doReturn(campaignCsvExporter).when(csvExporterMapping).get(
                CampaignReportExportRequest.class);
        doReturn(campaignReportExporter).when(reportExporterMapping).get(
                CampaignReportExportRequest.class);

        ReportExportPagedDetails<CampaignReportItem, Integer> emptyPagedDetails =
                new ReportExportPagedDetails<>(emptyList(), null);
        when(campaignReportExporter.findReportData(campaignReportExportRequest,
                null)).thenReturn(emptyPagedDetails);

        IOException expectedException = new IOException();
        List<String> emptyCustomFields = emptyList();
        when(campaignReportExporter.findCustomFields(campaignReportExportRequest))
                .thenReturn(emptyCustomFields);
        doThrow(expectedException).when(campaignCsvExporter)
                .writeHeader(emptyCustomFields, FILE_NAME, CONFIRMATION_DATE, LOCALE,
                        SITE_IDS);
        doReturn(logger).when(underTest).getLogger();
        when(campaignReportExporter.updateRequestBy(campaignReportExportRequest))
                .thenReturn(campaignReportExportRequest);
        SqlSessionManager sqlSessionManager = mock(SqlSessionManager.class);
        doReturn(sqlSessionManager).when(underTest).getSqlSessionManager();

        // when
        underTest.writeDataToFile(FILE_NAME, campaignReportExportRequest, LOCALE);

        // then
        verify(logger).error("Failed to process CSV file ", expectedException);
    }

    @Test
    public void testWriteDataToFileShouldWriteErrorLogWhenErrorOccuredInConversionReport()
            throws IOException {
        // given
        ConversionReportExportRequest conversionReportExportRequest =
                new ConversionReportExportRequest(COUNTRY_CODE, FROM_DATE, TO_DATE,
                        SITE_IDS, CAMPAIGN_IDS, CONFIRMATION_DATE,
                        Arrays.asList(CONVERSION_STATUS), VERIFICATION_IDS, PRODUCT_IDS,
                        CREATIVE_IDS, RESULT_IDS, RANKS, DEVICE_TYPES, CONVERSION_IDS,
                        MERCHANT_IDS, PUBLISHER_IDS, PUBLISHER_AGENCY_ID, PUBLISHER_TYPE,
                        IS_PENDING_FOR_MERCHANT_PAYMENT);
        doReturn(conversionCsvExporter).when(csvExporterMapping).get(
                ConversionReportExportRequest.class);
        doReturn(conversionReportExporter).when(reportExporterMapping).get(
                ConversionReportExportRequest.class);

        ReportExportPagedDetails<ConversionReport, Long> emptyPagedDetails =
                new ReportExportPagedDetails<>(emptyList(), null);
        when(conversionReportExporter.findReportData(conversionReportExportRequest, null))
            .thenReturn(emptyPagedDetails);

        IOException expectedException = new IOException();
        List<String> customFields = Arrays.asList("Header1", "Header2", "Header3");
        when(conversionReportExporter.findCustomFields(conversionReportExportRequest))
                .thenReturn(customFields);
        doThrow(expectedException).when(conversionCsvExporter)
                .writeHeader(customFields, FILE_NAME, CONFIRMATION_DATE, LOCALE,
                        SITE_IDS);
        doReturn(logger).when(underTest).getLogger();
        when(conversionReportExporter.updateRequestBy(conversionReportExportRequest))
                .thenReturn(conversionReportExportRequest);
        SqlSessionManager sqlSessionManager = mock(SqlSessionManager.class);
        doReturn(sqlSessionManager).when(underTest).getSqlSessionManager();

        // when
        underTest.writeDataToFile(FILE_NAME, conversionReportExportRequest, LOCALE);

        // then
        verify(logger).error("Failed to process CSV file ", expectedException);
    }

    @Test
    public void testGetLocaleByShouldReturnEnglishLocaleWhenGivenLocaleIsNull() {
        // given
        String locale = null;

        // when
        String actual = underTest.getLocaleBy(locale);

        // then
        assertEquals(LOCALE, actual);
    }

    @Test
    public void testGetLocaleByShouldReturnEnglishLocaleWhenGivenLocaleIsEmpty() {
        // given
        String locale = "";

        // when
        String actual = underTest.getLocaleBy(locale);

        // then
        assertEquals(LOCALE, actual);
    }

    private CampaignReportItem createCampaignReportItem(Long camapignId) {
        CampaignReportItem campaignReportItem = new CampaignReportItem();
        campaignReportItem.setCampaignId(camapignId);
        return campaignReportItem;
    }
}
