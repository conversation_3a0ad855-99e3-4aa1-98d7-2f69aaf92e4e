/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.writer;

import java.io.IOException;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.easybatch.core.record.Batch;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;
import org.slf4j.Logger;
import org.supercsv.io.CsvBeanWriter;
import org.supercsv.io.ICsvBeanWriter;
import org.supercsv.prefs.CsvPreference;

import jp.ne.interspace.taekkyeon.model.ApprovedRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.ConversionAndCreativeFunnelDetails;
import jp.ne.interspace.taekkyeon.model.ConversionTrackingFunnelDetails;
import jp.ne.interspace.taekkyeon.model.CreativeTrackingFunnelDetails;
import jp.ne.interspace.taekkyeon.model.FirstApprovedAffiliationDetails;
import jp.ne.interspace.taekkyeon.model.OccurredRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.Pair;
import jp.ne.interspace.taekkyeon.model.PublisherAccountFunnelDetails;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelAccumulatedRewardResult;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelDetails;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelDetailsByTime;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelDetailsPayload;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelTrendLoaderRecordPayload;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelTrendLoaderRequest;
import jp.ne.interspace.taekkyeon.model.RewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.Site;
import jp.ne.interspace.taekkyeon.module.Country;
import jp.ne.interspace.taekkyeon.module.Environment;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionLogMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CreativeAccessLogSummaryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PublisherAccountFunnelMapper;
import jp.ne.interspace.taekkyeon.persist.aws.redshift.mapper.PublisherFunnelTrendMapper;
import jp.ne.interspace.taekkyeon.persist.aws.s3.SimpleStorageServiceClient;
import jp.ne.interspace.taekkyeon.persist.aws.sqs.PublisherFunnelTrendQueue;
import jp.ne.interspace.taekkyeon.service.PublisherFunnelTrendAccumulatedRewardService;

import static com.google.common.collect.Iterables.partition;
import static java.math.BigDecimal.ZERO;
import static java.time.ZoneOffset.UTC;
import static java.util.Collections.emptyList;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.HYPHEN;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.NULL_STRING;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.UNDERSCORE;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonRedshiftSyncModule.BIND_KEY_BUCKET_NAME;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonRedshiftSyncModule.BIND_KEY_MINUTES_OFFSET;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonRedshiftSyncModule.BIND_KEY_REDSHIFT_CREDENTIALS;
import static lombok.AccessLevel.PACKAGE;

/**
 * {@link RecordWriter} implementation for writing publisher funnel data to {@code Redshift}.
 *
 * <AUTHOR>
 */
@Slf4j
public class PublisherFunnelTrendLoaderRecordWriter implements RecordWriter {

    private static final String S3_FILE_PATH_FORMAT = "s3://%s/%s";
    private static final String SUCCESS = "success";
    private static final String ERROR = "error";
    public static final String FILE_NAME_PREFIX = "publisher-funnel-data-";
    private static final int PARTITION_SIZE = 1000;

    @Inject @Named(BIND_KEY_REDSHIFT_CREDENTIALS) @Getter(PACKAGE)
    private String redshiftCredentials;

    @Inject @Named(BIND_KEY_BUCKET_NAME) @Getter(PACKAGE)
    private String bucketName;

    @Inject @Named(BIND_KEY_MINUTES_OFFSET) @Getter(PACKAGE)
    private int minutesOffset;

    @Inject
    private PublisherFunnelTrendMapper publisherFunnelTrendMapper;

    @Inject
    private PublisherAccountFunnelMapper publisherAccountFunnelMapper;

    @Inject
    private ConversionLogMapper conversionsMapper;

    @Inject
    private CreativeAccessLogSummaryMapper creativeAccessLogSummariesMapper;

    @Inject
    private SimpleStorageServiceClient s3Client;

    @Inject
    private PublisherFunnelTrendQueue sqsQueue;

    @Inject
    private PublisherFunnelTrendAccumulatedRewardService accumulatedRewardService;

    @Getter(PACKAGE)
    private boolean isError = false;

    @Getter(PACKAGE)
    private LocalDateTime targetTimeEnd = getCurrentDateTime()
            .minusMinutes(getMinutesOffset());

    @Override
    public void open() throws Exception {
        // do nothing
    }

    @Override
    public void writeRecords(Batch batch) throws Exception {
        processRecordsIn(batch);
    }

    @Override
    public void close() throws Exception {
        // do nothing
    }

    @VisibleForTesting
    void safelyUpsertData(Country country, Environment environment,
            PublisherFunnelTrendLoaderRecordPayload payload, String fileName,
            String temporaryTableKey) throws IOException {
        try {
            Pair<PublisherFunnelDetailsPayload, List<PublisherFunnelDetailsByTime>> result =
                    findPublisherFunnelDetails(payload.getRequests());
            PublisherFunnelDetailsPayload publisherFunnelDetailsPayload = result.getLeft();
            List<PublisherFunnelDetails> publisherFunnelDetails = createPublisherFunnelDetails(
                    publisherFunnelDetailsPayload, result.getRight());
            s3Client.putObject(getBucketName(), fileName, convertToCsvContentsFrom(
                    publisherFunnelDetails));
            upsertPublisherFunnelData(country, environment, temporaryTableKey, fileName);
            move(fileName, SUCCESS);
            upsertOraclePublisherFunnelTrend(publisherFunnelDetails);
        } catch (Exception e) {
            isError = true;
            getLogger().error(
                    "Failed to process CSV file on S3 or deactivate tracking data.", e);
            move(fileName, ERROR);
            throw e;
        }
    }

    @VisibleForTesting
    void upsertPublisherFunnelData(Country country, Environment environment,
            String temporaryTableKey, String fileName) {
        publisherFunnelTrendMapper
                .createTemporarySyncTable(country, environment, temporaryTableKey);
        publisherFunnelTrendMapper.copy(country, environment,
                String.format(S3_FILE_PATH_FORMAT, getBucketName(), fileName),
                getRedshiftCredentials(), temporaryTableKey);
        publisherFunnelTrendMapper
                .updateDataByTemporaryTable(country, environment, temporaryTableKey);
        publisherFunnelTrendMapper
                .insertDataByTemporaryTable(country, environment, temporaryTableKey);
        publisherFunnelTrendMapper
                .dropTemporaryTable(country, environment, temporaryTableKey);
    }

    @VisibleForTesting
    void upsertOraclePublisherFunnelTrend(
            List<PublisherFunnelDetails> publisherFunnelDetails) {
        if (publisherFunnelDetails.isEmpty()) {
            return;
        }
        publisherAccountFunnelMapper.upsertPublisherFunnelTrendSummary(
                publisherFunnelDetails, getTargetTimeEnd());
    }

    @VisibleForTesting
    String convertToCsvContentsFrom(List<PublisherFunnelDetails> publisherFunnels)
            throws IOException {
        StringWriter stringWriter = new StringWriter();
        try (ICsvBeanWriter csvWriter = new CsvBeanWriter(stringWriter,
                CsvPreference.STANDARD_PREFERENCE)) {
            for (PublisherFunnelDetails publisherFunnelDetails : publisherFunnels) {
                csvWriter.write(publisherFunnelDetails,
                        PublisherFunnelDetails.NAME_MAPPING,
                        PublisherFunnelDetails.CELL_PROCESSOR);
            }
            csvWriter.flush();
        }
        return stringWriter.toString();
    }

    @VisibleForTesting
    String createTemporaryTableKey() {
        return UUID.randomUUID().toString().replace(HYPHEN, UNDERSCORE);
    }

    @VisibleForTesting
    List<PublisherFunnelDetails> createPublisherFunnelDetails(
            PublisherFunnelDetailsPayload payload,
            List<PublisherFunnelDetailsByTime> publisherFunnelDetailsByTimes) {
        Map<Long, PublisherAccountFunnelDetails> accountDetails = payload
                .getPublisherAccountFunnelDetails();
        Map<Long, FirstApprovedAffiliationDetails> affiliationDetails = payload
                .getFirstApprovedAffiliationDetails();
        Map<Long, List<Long>> activatedSiteIdsMap = payload
                .getPublisherIdsAndActivatedSiteIds();
        Map<Long, ConversionTrackingFunnelDetails> conversionMap = payload
                .getConversionsTrackingFunnelDetails();
        Map<Long, CreativeTrackingFunnelDetails> creativeMap = payload
                .getCreativesTrackingFunnelDetails();
        Map<Long, ApprovedRewardFunnelDetails> approvedRewardMap = payload
                .getApprovedRewardFunnelDetails();
        Map<Long, OccurredRewardFunnelDetails> occurredRewardMap = payload
                .getOccurredRewardFunnelDetails();

        List<PublisherFunnelDetails> result = new ArrayList<>();

        for (Map.Entry<Long, PublisherAccountFunnelDetails> entry : accountDetails.entrySet()) {
            Long publisherId = entry.getKey();
            PublisherAccountFunnelDetails account = entry.getValue();
            List<Long> siteIds = activatedSiteIdsMap.getOrDefault(publisherId, emptyList());
            ConversionAndCreativeFunnelDetails conversionCreative =
                    getConversionAndCreativeFunnelDetail(siteIds,
                            conversionMap, creativeMap);
            RewardFunnelDetails reward = getRewardFunnelDetail(siteIds,
                    approvedRewardMap, occurredRewardMap);
            FirstApprovedAffiliationDetails affiliation = affiliationDetails.getOrDefault(
                    publisherId, FirstApprovedAffiliationDetails.DEFAULT_ITEM);
            result.add(buildPublisherFunnelDetails(account,
                    affiliation, conversionCreative, reward));
        }

        for (PublisherFunnelDetailsByTime byTime : publisherFunnelDetailsByTimes) {
            List<Long> siteIds = activatedSiteIdsMap.getOrDefault(
                    byTime.getAccountId(), emptyList());
            RewardFunnelDetails reward = getRewardFunnelDetail(
                    siteIds, approvedRewardMap, occurredRewardMap);
            result.add(buildPublisherFunnelDetails(byTime, reward));
        }

        return result;
    }

    @VisibleForTesting
    String getDomainFrom(String url) {
        if (!isNullOrEmptyOrNullString(url)) {
            try {
                URL uri = new URL(url);
                return uri.getHost();
            } catch (MalformedURLException e) {
                return EMPTY;
            }
        }
        return EMPTY;
    }

    @VisibleForTesting
    boolean isNullOrEmptyOrNullString(String string) {
        return (Strings.isNullOrEmpty(string) || NULL_STRING.equalsIgnoreCase(string));
    }

    @VisibleForTesting
    void move(String fileName, String targetFolder) {
        if (s3Client.doesObjectExist(getBucketName(), fileName)) {
            s3Client.copyObject(getBucketName(), fileName, getBucketName(),
                    targetFolder + "/" + fileName);
            s3Client.deleteObject(getBucketName(), fileName);
        }
    }

    @VisibleForTesting
    Pair<PublisherFunnelDetailsPayload, List<PublisherFunnelDetailsByTime>>
            findPublisherFunnelDetails(
                List<PublisherFunnelTrendLoaderRequest> requests) {
        Map<Long, List<Long>> publisherIdsAndActivatedSiteIds = new HashMap<>();
        Map<Long, ConversionTrackingFunnelDetails> conversionsTrackingFunnelDetails =
                new HashMap<>();
        Map<Long, CreativeTrackingFunnelDetails> creativesTrackingFunnelDetails =
                new HashMap<>();
        Map<Long, ApprovedRewardFunnelDetails> approvedRewardFunnelDetails =
                new HashMap<>();
        Map<Long, OccurredRewardFunnelDetails> occurredRewardFunnelDetails =
                new HashMap<>();
        Map<Long, PublisherAccountFunnelDetails> publisherAccountFunnelDetails =
                new HashMap<>();
        Map<Long, FirstApprovedAffiliationDetails> firstApprovedAffiliationDetails =
                new HashMap<>();

        List<Long> publisherIds = new LinkedList<>();
        List<Long> activatedSiteIds = new LinkedList<>();
        Set<Long> siteIdsToGetAccumulatedData = new HashSet<>();
        List<PublisherFunnelDetailsByTime> publisherFunnelDetailsByTimes = emptyList();
        requests.forEach(request -> {
            Long publisherId = request.getPublisherId();
            List<Long> siteIds = request.getSiteIds();
            publisherIdsAndActivatedSiteIds.put(publisherId, siteIds);
            publisherIds.add(publisherId);
            activatedSiteIds.addAll(siteIds);
        });
        publisherFunnelDetailsByTimes =
                publisherAccountFunnelMapper.findPublisherFunnelDetailsByTime(
                        publisherIds);
        if (!publisherFunnelDetailsByTimes.isEmpty()) {
            Set<Long> existFunnelTrendAccountIds = publisherFunnelDetailsByTimes.stream()
                    .map(PublisherFunnelDetailsByTime::getAccountId)
                    .collect(Collectors.toSet());
            publisherIds.removeAll(existFunnelTrendAccountIds);
            siteIdsToGetAccumulatedData = existFunnelTrendAccountIds.stream()
                    .flatMap(id -> publisherIdsAndActivatedSiteIds.get(id).stream())
                    .collect(Collectors.toSet());
            activatedSiteIds.removeAll(siteIdsToGetAccumulatedData);
        }
        if (!publisherIds.isEmpty()) {
            publisherAccountFunnelDetails = publisherAccountFunnelMapper
                    .findPublisherAccountFunnelDetailsBy(publisherIds);
            firstApprovedAffiliationDetails =
                    publisherAccountFunnelMapper
                            .findFirstApprovedAffiliationDetailsBy(publisherIds);
        }
        if (!activatedSiteIds.isEmpty()) {
            conversionsTrackingFunnelDetails =
                    conversionsMapper.findConversionTrackingFunnelDetails(
                            activatedSiteIds);
            creativesTrackingFunnelDetails =
                    creativeAccessLogSummariesMapper.findCreativeTrackingFunnelDetails(
                            activatedSiteIds);
            approvedRewardFunnelDetails =
                    conversionsMapper.findApprovedRewardFunnelDetails(activatedSiteIds);
            occurredRewardFunnelDetails =
                    conversionsMapper.findOccurredRewardFunnelDetails(activatedSiteIds);
        }

        if (!siteIdsToGetAccumulatedData.isEmpty()) {
            getAccumulatedDataForExistFunnelTrend(publisherIdsAndActivatedSiteIds,
                    publisherFunnelDetailsByTimes, approvedRewardFunnelDetails,
                    occurredRewardFunnelDetails);
        }
        return Pair.of(new PublisherFunnelDetailsPayload(publisherIdsAndActivatedSiteIds,
                publisherAccountFunnelDetails, firstApprovedAffiliationDetails,
                conversionsTrackingFunnelDetails, creativesTrackingFunnelDetails,
                approvedRewardFunnelDetails, occurredRewardFunnelDetails),
                publisherFunnelDetailsByTimes);
    }

    @VisibleForTesting
    Map<Long, Set<Long>> getActivatedSiteIdsBy(List<Long> publisherIds) {
        Map<Long, Set<Long>> activatedSiteIds = new HashMap<>();
        for (List<Long> partitionPublisherIds : partition(publisherIds, PARTITION_SIZE)) {
            activatedSiteIds.putAll(publisherAccountFunnelMapper.findActivatedSiteIdsBy(
                    partitionPublisherIds)
                    .stream()
                    .collect(Collectors.groupingBy(Site::getPublisherId,
                            Collectors.mapping(Site::getSiteId,
                                    Collectors.toSet()))));
        }
        return activatedSiteIds;
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }

    @VisibleForTesting
    LocalDateTime getCurrentDateTime() {
        return LocalDateTime.now();
    }

    @SuppressWarnings("unchecked")
    private void processRecordsIn(Batch batch) throws IOException {
        List<String> receiptHandles = new LinkedList<>();
        for (Record<PublisherFunnelTrendLoaderRecordPayload> payloadRecord : batch) {
            if (!payloadRecord.getPayload().getRequests().isEmpty()) {
                safelyUpsertData(getCurrentCountry(), getCurrentEnvironment(),
                        payloadRecord.getPayload(),
                        createFileNameBy(payloadRecord.getHeader().getCreationDate()),
                        createTemporaryTableKey());
            }
            receiptHandles.add(payloadRecord.getPayload().getReceiptHandle());
        }
        sqsQueue.delete(receiptHandles);
    }

    private ConversionAndCreativeFunnelDetails getConversionAndCreativeFunnelDetail(
            List<Long> siteIds,
            Map<Long, ConversionTrackingFunnelDetails> conversionTrackingFunnelDetailsBySiteId,
            Map<Long, CreativeTrackingFunnelDetails> creativeTrackingFunnelDetailsBySiteId) {
        List<LocalDate> firstImpressionOrClickDates = new LinkedList<>();
        List<LocalDate> firstConversionDates = new LinkedList<>();
        List<LocalDate> firstApprovedConversionDates = new LinkedList<>();
        siteIds.forEach(siteId -> {
            firstImpressionOrClickDates.add(creativeTrackingFunnelDetailsBySiteId
                    .getOrDefault(siteId, CreativeTrackingFunnelDetails.DEFAULT_ITEM)
                    .getFirstImpressionOrClickDate());
            ConversionTrackingFunnelDetails conversionTrackingFunnelDetails =
                    conversionTrackingFunnelDetailsBySiteId.getOrDefault(siteId,
                            ConversionTrackingFunnelDetails.DEFAULT_ITEM);
            firstConversionDates
                    .add(conversionTrackingFunnelDetails.getFirstConversionDate());
            firstApprovedConversionDates.add(conversionTrackingFunnelDetails
                    .getFirstApprovedConversionDate());
        });

        return new ConversionAndCreativeFunnelDetails(
                getMinLocalDateBy(firstImpressionOrClickDates),
                getMinLocalDateBy(firstConversionDates),
                getMinLocalDateBy(firstApprovedConversionDates));
    }

    private LocalDate getMinLocalDateBy(List<LocalDate> localDates) {
        return localDates.stream()
                .filter(Objects::nonNull)
                .min(Comparator.comparing(LocalDate::toEpochDay))
                .orElse(null);
    }

    private RewardFunnelDetails getRewardFunnelDetail(
            List<Long> siteIds,
            Map<Long, ApprovedRewardFunnelDetails> approvedRewardFunnelDetailsBySiteId,
            Map<Long, OccurredRewardFunnelDetails> occurredRewardFunnelDetailsBySiteId) {
        List<ApprovedRewardFunnelDetails> approvedRewards = new LinkedList<>();
        List<OccurredRewardFunnelDetails> occurredRewards = new LinkedList<>();

        siteIds.forEach(siteId -> {
            ApprovedRewardFunnelDetails approvedRewardFunnelDetail =
                    approvedRewardFunnelDetailsBySiteId.getOrDefault(siteId,
                            ApprovedRewardFunnelDetails.DEFAULT_ITEM);
            approvedRewards.add(approvedRewardFunnelDetail);
            OccurredRewardFunnelDetails occurredRewardFunnelDetail =
                    occurredRewardFunnelDetailsBySiteId.getOrDefault(siteId,
                            OccurredRewardFunnelDetails.DEFAULT_ITEM);
            occurredRewards.add(occurredRewardFunnelDetail);
        });
        return new RewardFunnelDetails(
                sumAmount(approvedRewards,
                        ApprovedRewardFunnelDetails::getApprovedSalesReward),
                sumAmount(approvedRewards,
                        ApprovedRewardFunnelDetails::getApprovedTransactionAmountReward),
                sumAmount(approvedRewards,
                        ApprovedRewardFunnelDetails::getApprovedAtCommission),
                sumAmount(approvedRewards,
                        ApprovedRewardFunnelDetails::getApprovedMerchantAgentCommission),
                sumAmount(approvedRewards,
                        ApprovedRewardFunnelDetails::getApprovedPublisherAgentCommission),
                sumAmount(occurredRewards,
                        OccurredRewardFunnelDetails::getOccurredSalesReward),
                sumAmount(occurredRewards,
                        OccurredRewardFunnelDetails::getOccurredTransactionAmountReward),
                sumAmount(occurredRewards,
                        OccurredRewardFunnelDetails::getOccurredAtCommission),
                sumAmount(occurredRewards,
                        OccurredRewardFunnelDetails::getOccurredMerchantAgentCommission),
                sumAmount(occurredRewards,
                        OccurredRewardFunnelDetails::getOccurredPublisherAgentCommission));
    }

    private <T> BigDecimal sumAmount(List<T> list, Function<T, BigDecimal> mapper) {
        return list.stream().map(mapper).reduce(ZERO, BigDecimal::add);
    }

    private String createFileNameBy(Date date) {
        return Joiner.on(EMPTY).join(FILE_NAME_PREFIX,
                date.toInstant().atOffset(UTC).toLocalDateTime(), ".csv");
    }

    private Map<LocalDateTime, List<Long>> groupSiteIdsByLatestTime(
            List<PublisherFunnelDetailsByTime> publisherFunnelDetailsByTimes,
            Map<Long, List<Long>> accountIdsAndActivatedSiteIds) {
        Map<LocalDateTime, List<Long>> siteIdsByLatestTime = new HashMap<>();
        for (PublisherFunnelDetailsByTime detailsByTime : publisherFunnelDetailsByTimes) {
            if (detailsByTime.getLatestTime() != null) {
                siteIdsByLatestTime.computeIfAbsent(
                                detailsByTime.getLatestTime(), k -> new ArrayList<>())
                        .addAll(accountIdsAndActivatedSiteIds
                                .get(detailsByTime.getAccountId()));
            }
        }
        return siteIdsByLatestTime;
    }

    private PublisherFunnelDetails buildPublisherFunnelDetails(
            PublisherFunnelDetailsByTime byTime,
            RewardFunnelDetails reward) {
        return new PublisherFunnelDetails(
                byTime.getAccountId(),
                byTime.getCountryCode(),
                byTime.getReferrerId(),
                byTime.getUtmSource(),
                byTime.getAccountType(),
                byTime.getRegisteredDate(),
                byTime.getActivatedDate(),
                byTime.getFirstApprovedSiteDate(),
                byTime.getFirstAffiliationDate(),
                byTime.getFirstApproveAffiliationDate(),
                byTime.getFirstImpressionOrClickDate(),
                byTime.getFirstConversionDate(),
                byTime.getFirstApprovedConversionDate(),
                byTime.getFirstPaymentDate(),
                reward.getOccurredSalesReward(),
                reward.getOccurredTransactionAmountReward(),
                reward.getOccurredAtCommission(),
                reward.getOccurredMerchantAgentCommission(),
                reward.getOccurredPublisherAgentCommission(),
                reward.getApprovedSalesReward(),
                reward.getApprovedTransactionAmountReward(),
                reward.getApprovedAtCommission(),
                reward.getApprovedMerchantAgentCommission(),
                reward.getApprovedPublisherAgentCommission(),
                byTime.getRegistrationReferralUrl(),
                getDomainFrom(byTime.getRegistrationReferralUrl()),
                byTime.getUtmMedium(),
                byTime.getUtmContent(),
                byTime.getUtmCampaign(),
                byTime.getUtmTerm(),
                byTime.getEmail()
        );
    }

    private PublisherFunnelDetails buildPublisherFunnelDetails(
            PublisherAccountFunnelDetails account,
            FirstApprovedAffiliationDetails affiliation,
            ConversionAndCreativeFunnelDetails conversionCreative,
            RewardFunnelDetails reward) {
        return new PublisherFunnelDetails(
                account.getAccountId(),
                account.getCountryCode(),
                account.getReferrerId(),
                account.getUtmSource(),
                account.getAccountType(),
                account.getRegisteredDate(),
                account.getActivatedDate(),
                account.getFirstApprovedSiteDate(),
                account.getFirstAffiliationDate(),
                affiliation.getFirstApprovedAffiliationDate(),
                conversionCreative.getFirstImpressionOrClickDate(),
                conversionCreative.getFirstConversionDate(),
                conversionCreative.getFirstApprovedConversionDate(),
                account.getFirstPaymentDate(),
                reward.getOccurredSalesReward(),
                reward.getOccurredTransactionAmountReward(),
                reward.getOccurredAtCommission(),
                reward.getOccurredMerchantAgentCommission(),
                reward.getOccurredPublisherAgentCommission(),
                reward.getApprovedSalesReward(),
                reward.getApprovedTransactionAmountReward(),
                reward.getApprovedAtCommission(),
                reward.getApprovedMerchantAgentCommission(),
                reward.getApprovedPublisherAgentCommission(),
                account.getRegistrationReferralUrl(),
                getDomainFrom(account.getRegistrationReferralUrl()),
                account.getUtmMedium(),
                account.getUtmContent(),
                account.getUtmCampaign(),
                account.getUtmTerm(),
                account.getEmail()
        );
    }

    private void getAccumulatedDataForExistFunnelTrend(
            Map<Long, List<Long>> accountIdsAndActivatedSiteIds,
            List<PublisherFunnelDetailsByTime> publisherFunnelDetailsByTimes,
            Map<Long, ApprovedRewardFunnelDetails> approvedRewardFunnelDetails,
            Map<Long, OccurredRewardFunnelDetails> occurredRewardFunnelDetails) {
        PublisherFunnelAccumulatedRewardResult accumulatedDataResult =
                accumulatedRewardService.collectDataForAccountIdsWithTimeRange(
                        publisherFunnelDetailsByTimes,
                        groupSiteIdsByLatestTime(publisherFunnelDetailsByTimes,
                                accountIdsAndActivatedSiteIds),
                        accountIdsAndActivatedSiteIds, getTargetTimeEnd());
        approvedRewardFunnelDetails.putAll(accumulatedDataResult.getApprovedRewards());
        occurredRewardFunnelDetails.putAll(accumulatedDataResult.getOccurredRewards());
    }
}
