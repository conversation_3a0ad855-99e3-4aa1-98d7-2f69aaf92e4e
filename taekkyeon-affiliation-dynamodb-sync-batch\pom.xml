<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>jp.ne.interspace</groupId>
  <artifactId>taekkyeon-affiliation-dynamodb-sync-batch</artifactId>
 <version>1.137.0-SNAPSHOT</version>
  <name>taekkyeon-affiliation-dynamodb-sync-batch</name>

  <properties>
    <easybatch.version>5.0.0</easybatch.version>
    <eclipse-collections.version>8.2.0</eclipse-collections.version>
    <guava.version>19.0</guava.version>
    <guice.version>4.1.0</guice.version>
    <hsqldb.version>2.3.1</hsqldb.version>
    <jdk.version>1.8</jdk.version>
    <junit.version>4.12</junit.version>
    <log4j.version>1.2.17</log4j.version>
    <lombok.version>1.16.12</lombok.version>
    <maven.assembly.plugin.version>2.3</maven.assembly.plugin.version>
    <maven.checkstyle.plugin.version>2.17</maven.checkstyle.plugin.version>
    <maven.compiler.plugin.optimize>true</maven.compiler.plugin.optimize>
    <maven.compiler.plugin.version>3.3</maven.compiler.plugin.version>
    <maven.exec.plugin.version>1.6.0</maven.exec.plugin.version>
    <mockito.version>2.8.47</mockito.version>
    <mybatis.guice.version>3.7.1</mybatis.guice.version>
    <mybatis.version>3.4.1</mybatis.version>
    <oracle.driver.version>********</oracle.driver.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <slf4j.version>1.7.5</slf4j.version>
    <taekkyeon.core.version>1.137.0-SNAPSHOT</taekkyeon.core.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>jp.ne.interspace</groupId>
      <artifactId>taekkyeon-core</artifactId>
      <version>${taekkyeon.core.version}</version>
    </dependency>

    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis</artifactId>
      <version>${mybatis.version}</version>
    </dependency>

    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis-guice</artifactId>
      <version>${mybatis.guice.version}</version>
    </dependency>

    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>${guava.version}</version>
    </dependency>

    <dependency>
      <groupId>org.eclipse.collections</groupId>
      <artifactId>eclipse-collections</artifactId>
      <version>${eclipse-collections.version}</version>
    </dependency>

    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
      <version>${guice.version}</version>
    </dependency>

    <dependency>
      <groupId>com.google.inject.extensions</groupId>
      <artifactId>guice-multibindings</artifactId>
      <version>${guice.version}</version>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit.version}</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>${mockito.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4j.version}</version>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-log4j12</artifactId>
      <version>${slf4j.version}</version>
    </dependency>

    <dependency>
      <groupId>log4j</groupId>
      <artifactId>log4j</artifactId>
      <version>${log4j.version}</version>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jcl-over-slf4j</artifactId>
      <version>${slf4j.version}</version>
    </dependency>

    <dependency>
      <groupId>com.oracle</groupId>
      <artifactId>ojdbc6</artifactId>
      <version>${oracle.driver.version}</version>
    </dependency>

    <dependency>
      <groupId>org.easybatch</groupId>
      <artifactId>easybatch-core</artifactId>
      <version>${easybatch.version}</version>
    </dependency>

    <dependency>
      <groupId>org.hsqldb</groupId>
      <artifactId>hsqldb</artifactId>
      <version>${hsqldb.version}</version>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <resources>
      <resource>
        <filtering>false</filtering>
        <directory>src/main/resources</directory>
      </resource>

      <resource>
        <filtering>false</filtering>
        <directory>src/main/java</directory>
        <includes>
          <include>**</include>
        </includes>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </resource>
    </resources>

    <testResources>

      <testResource>
        <filtering>false</filtering>
        <directory>src/test/resources</directory>
      </testResource>

      <testResource>
        <filtering>false</filtering>
        <directory>src/test/java</directory>
        <includes>
          <include>**</include>
        </includes>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </testResource>

    </testResources>

    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <versionRange>[${maven.checkstyle.plugin.version},)</versionRange>
                    <goals>
                      <goal>check</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <execute>
                      <runOnIncremental>false</runOnIncremental>
                    </execute>
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven.compiler.plugin.version}</version>
        <configuration>
          <source>${jdk.version}</source>
          <target>${jdk.version}</target>
          <annotationProcessors>
            <annotationProcessor>
              jp.ne.interspace.taekkyeon.multiline.MultilineProcessor
            </annotationProcessor>
            <annotationProcessor>
              lombok.launch.AnnotationProcessorHider$AnnotationProcessor
            </annotationProcessor>
          </annotationProcessors>
          <encoding>${project.build.sourceEncoding}</encoding>
          <optimize>${maven.compiler.plugin.optimize}</optimize>
          <showWarnings>true</showWarnings>
          <showDeprecation>true</showDeprecation>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>${maven.assembly.plugin.version}</version>
        <configuration>
          <descriptorRefs>
            <descriptorRef>jar-with-dependencies</descriptorRef>
          </descriptorRefs>
        </configuration>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${maven.checkstyle.plugin.version}</version>
        <dependencies>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>6.19</version>
          </dependency>
        </dependencies>
        <executions>
          <execution>
            <id>validate</id>
            <phase>validate</phase>
            <configuration>
              <configLocation>../checkstyle/taekkyeonCheckstyle.xml</configLocation>
              <propertiesLocation>../checkstyle/taekkyeonCheckstyleConfiguration.properties</propertiesLocation>
              <encoding>UTF-8</encoding>
              <consoleOutput>true</consoleOutput>
              <failsOnError>true</failsOnError>
              <linkXRef>false</linkXRef>
              <includeTestSourceDirectory>true</includeTestSourceDirectory>
            </configuration>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
