/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageResult;
import com.amazonaws.services.sqs.model.SendMessageBatchRequestEntry;
import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionManager;
import org.easybatch.core.record.Batch;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;

import redis.clients.jedis.Jedis;
import redis.embedded.RedisServer;

import jp.ne.interspace.taekkyeon.batch.processor.LogRecordProcessor;
import jp.ne.interspace.taekkyeon.batch.reader.LogRecordReader;
import jp.ne.interspace.taekkyeon.batch.writer.LogRecordWriter;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonIntegrationTestHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CommissionType;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.DeviceOs;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.IntegrationTestConversion;
import jp.ne.interspace.taekkyeon.model.IntegrationTestParameter;
import jp.ne.interspace.taekkyeon.model.LogRecord;
import jp.ne.interspace.taekkyeon.model.RewardType;
import jp.ne.interspace.taekkyeon.model.SqsRecord;
import jp.ne.interspace.taekkyeon.module.CommonLogLoaderPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.module.ConversionLogLoaderJunitModule;
import jp.ne.interspace.taekkyeon.module.OracleResolver;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.IntegrationTestClickParametersMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.IntegrationTestConversionMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.IntegrationTestConversionParametersMapper;
import jp.ne.interspace.taekkyeon.persist.aws.sqs.LogQueueByDefault;

import static java.math.BigDecimal.ZERO;
import static java.math.BigDecimal.valueOf;
import static java.time.LocalDateTime.of;
import static jp.ne.interspace.taekkyeon.model.CommissionType.GROSS_AMOUNT_SOLD;
import static jp.ne.interspace.taekkyeon.model.CommissionType.NET;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.PENDING;
import static jp.ne.interspace.taekkyeon.model.DeviceOs.IPHONE;
import static jp.ne.interspace.taekkyeon.model.DeviceType.DESKTOP;
import static jp.ne.interspace.taekkyeon.model.RewardType.CPA_FIXED;
import static jp.ne.interspace.taekkyeon.model.RewardType.CPA_SALES;
import static org.easybatch.core.job.JobBuilder.newJob;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * Integration test for the conversion log loader batch.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonIntegrationTestHsqldbJunitRunner.class)
@TaekkyeonModules({ CommonLogLoaderPropertiesJunitModule.class,
        TaekkyeonPropertiesJunitModule.class, TaekkyeonHsqldbOracleJunitModule.class,
        ConversionLogLoaderJunitModule.class })
public class ConversionLogLoaderIntegrationTest {

    private static final String DISCOUNT = "10";
    private static final BigDecimal PRICE = valueOf(108);
    private static final BigDecimal TOTAL_PRICE = valueOf(108);
    private static final BigDecimal DEFAULT_PRICE = valueOf(108);
    private static final BigDecimal DISCOUNT_VALUE = valueOf(10);
    private static final BigDecimal ORIGINAL_CURRENCY_TOTAL_PRICE = valueOf(90);
    private static final String CAMPAIGN_ID_1 = "1";
    private static final String CUSTOMER_TYPE = "customerType";
    private static final String CLICK_IP_ADDRESS = "*********";
    private static final LocalDateTime EXPECTED_CONVERSION_TIME = of(2017, 1, 22, 16, 51, 17);

    @Inject @OracleResolver
    private SqlSessionManager sqlSessionManager;

    @Inject
    private LogRecordReader logRecordReader;

    @Inject
    private LogRecordProcessor logRecordProcessor;

    @Inject
    private LogRecordWriter logRecordWriter;

    @Inject
    private LogQueueByDefault logQueueByDefault;

    @Inject
    private IntegrationTestConversionMapper conversionMapper;

    @Inject
    private IntegrationTestClickParametersMapper clickParametersMapper;

    @Inject
    private IntegrationTestConversionParametersMapper conversionParametersMapper;

    private static RedisServer redisServer;

    @BeforeClass
    @VisibleForTesting
    public static void startRedis() throws Exception {
        int maxTry = 5;
        int triedCount = 0;
        redisServer = new RedisServer();
        redisServer.start();
        while (triedCount++ < maxTry) {
            System.out.println("Waiting to start redis server.");
            if (redisServer.isActive()) {
                break;
            }
            Thread.sleep(2000);
        }
    }

    @Before
    public void setup() throws Exception {
        Jedis jedis = new Jedis("localhost", 6379);
        jedis.flushAll();
        jedis.close();
        logQueueByDefault.setUpForTest();
        logQueueByDefault.waitUntilAvailable();
    }

    @After
    public void tearDown() throws Exception {
        logQueueByDefault.tearDownForTest();
        conversionMapper.deleteAll();
        clickParametersMapper.deleteAll();
        conversionParametersMapper.deleteAll();

    }

    @AfterClass
    @VisibleForTesting
    public static void stopRedis() throws Exception {
        redisServer.stop();
        int maxTry = 5;
        int triedCount = 0;
        while (triedCount++ < maxTry) {
            System.out.println("Waiting to stop redis server.");
            if (!redisServer.isActive()) {
                break;
            }
            Thread.sleep(2000);
        }
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionWhenGivenInvalidReferer()
            throws Exception {
        // given
        String invalidReferer = "INVALID_REFERER";
        String defaultSessionId = "sessionId";
        String defaultIdentifier = "identifier";
        String defaultResultId = "3";
        String defaultQuantity = "1";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String message = createMessage(defaultResultId, defaultQuantity, invalidReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadAConversionIntoTheDatabaseCorrectlyWhenGivenValidReferer()
            throws Exception {
        // given
        int resultId = 3;
        String validReferer = "http://google.co.jp";
        String defaultSessionId = "sessionId";
        String defaultIdentifier = "test";
        String defaultResultId = "3";
        String defaultQuantity = "1";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String message = createMessage(defaultResultId, defaultQuantity, validReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, PRICE, TOTAL_PRICE, DEFAULT_PRICE,
                DISCOUNT_VALUE, ORIGINAL_CURRENCY_TOTAL_PRICE, CPA_SALES, valueOf(3.24),
                GROSS_AMOUNT_SOLD, valueOf(1.29), valueOf(1.62));
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenSessionIdWithNullString()
            throws Exception {
        // given
        String sessionId = "NULL";
        String defaultIdentifier = "identifier";
        String defaultResultId = "3";
        String defaultQuantity = "1";
        String defaultReferer = "http://google.co.jp";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String message = createMessage(defaultResultId, defaultQuantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId, sessionId,
                DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenSessionIdIsNull()
            throws Exception {
        // given
        String sessionId = null;
        String defaultIdentifier = "identifier";
        String defaultResultId = "3";
        String defaultQuantity = "1";
        String defaultReferer = "http://google.co.jp";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String message = createMessage(defaultResultId, defaultQuantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId, sessionId,
                DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenSessionIdDoesNotExist()
            throws Exception {
        // given
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:3&"
                + "goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=test#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId#Quantity=1#Unit_Price=100"
                + "#Category_ID=categoryId#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenRequestIsNull()
            throws Exception {
        // given
        String request = null;
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=" + request
                + "#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId"
                + "#Quantity=1#Unit_Price=100"
                + "#Category_ID=categoryId#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenRequestIsNullString()
            throws Exception {
        // given
        String request = "NULL";
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=" + request
                + "#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId"
                + "#Quantity=1#Unit_Price=100"
                + "#Category_ID=categoryId#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenRequestDoesNotExist()
            throws Exception {
        // given
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17"
                + "#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId"
                + "#Quantity=1#Unit_Price=100"
                + "#Category_ID=categoryId#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenIdentifierDoesNotExist()
            throws Exception {
        // given
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:3&"
                + "goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId#Quantity=1#Unit_Price=100"
                + "#Category_ID=categoryId#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenIdentifierIsNull()
            throws Exception {
        // given
        String identifier = null;
        String defaultResultId = "3";
        String defaultQuantity = "1";
        String defaultReferer = "http://google.co.jp";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, defaultQuantity, defaultReferer,
                identifier, defaultConversionDate, defaultCategoryId, defaultSessionId,
                DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenIdentifierIsNullString()
            throws Exception {
        // given
        String identifier = "NULL";
        String defaultResultId = "3";
        String defaultQuantity = "1";
        String defaultReferer = "http://google.co.jp";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, defaultQuantity, defaultReferer,
                identifier, defaultConversionDate, defaultCategoryId, defaultSessionId,
                DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenQuantityIsNullAndConversionIsProductReward()
            throws Exception {
        // given
        String quantity = null;
        String defaultResultId = "3";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenQuantityIsNullAndConversionIsCategoryReward()
            throws Exception {
        // given
        String quantity = null;
        String defaultResultId = "30";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenGivenQuantityIsNullAndConversionIsNotCategoryOrProductReward()
            throws Exception {
        // given
        String quantity = null;
        int resultId = 1;
        String defaultResultId = "1";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessageHasValue(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1, 100);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, PRICE, TOTAL_PRICE, DEFAULT_PRICE,
                DISCOUNT_VALUE, ORIGINAL_CURRENCY_TOTAL_PRICE, CPA_FIXED, ZERO, NET, ZERO,
                ZERO);
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenGivenQuantityIsNullAndConversionIsNotCategoryOrProductRewardAndValueIsNegative()
            throws Exception {
        // given
        String quantity = null;
        int resultId = 1;
        String defaultResultId = "1";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessageHasValue(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1, -100);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, ZERO, ZERO, ZERO,
                DISCOUNT_VALUE, ZERO, CPA_FIXED, ZERO, NET, ZERO, ZERO);
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenGivenQuantityIsNullAndConversionIsCategoryOrProductRewardAndValueIsNegative()
            throws Exception {
        // given
        String quantity = "1";
        int resultId = 1;
        String defaultResultId = "1";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessageHasPrice(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1, -100);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, ZERO, ZERO, ZERO,
                DISCOUNT_VALUE, ZERO, CPA_FIXED, ZERO, NET, ZERO, ZERO);
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenQuantityIsNullStringAndConversionIsProductReward()
            throws Exception {
        // given
        String quantity = "NULL";
        String defaultResultId = "3";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenQuantityIsNullStringAndConversionIsCategoryReward()
            throws Exception {
        // given
        String quantity = "NULL";
        String defaultResultId = "30";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenGivenQuantityIsNullStringAndConversionIsNotProductOrCategoryReward()
            throws Exception {
        // given
        int resultId = 1;
        String quantity = "NULL";
        String defaultResultId = "1";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessageHasValue(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1, 100);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, PRICE, TOTAL_PRICE, DEFAULT_PRICE,
                DISCOUNT_VALUE, ORIGINAL_CURRENCY_TOTAL_PRICE, CPA_FIXED, ZERO, NET, ZERO,
                ZERO);
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenQuantityIsNotNumericStringAndConversionIsProductReward()
            throws Exception {
        // given
        String quantity = "b2";
        String defaultResultId = "3";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenQuantityIsNotNumericStringAndConversionIsCategoryReward()
            throws Exception {
        // given
        String quantity = "bb";
        String defaultResultId = "30";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenGivenQuantityIsNotNumericStringAndConversionIsNotCategoryOrProductReward()
            throws Exception {
        // given
        int resultId = 1;
        String quantity = "b2";
        String defaultResultId = "1";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessageHasValue(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1, 100);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, PRICE, TOTAL_PRICE, DEFAULT_PRICE,
                DISCOUNT_VALUE, ORIGINAL_CURRENCY_TOTAL_PRICE, CPA_FIXED, ZERO, NET, ZERO,
                ZERO);
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotInsertConversionAndThrowExceptionWhenQuantityValueIsLessThanDefaultValueAndConversionIsProductReward()
            throws Exception {

        // given
        String quantity = "0";
        String defaultResultId = "3";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotInsertConversionAndThrowExceptionWhenQuantityValueIsLessThanDefaultValueAndConversionIsCategoryReward()
            throws Exception {

        // given
        String quantity = "0";
        String defaultResultId = "30";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenQuantityValueIsLessThanDefaultValueAndConversionIsNOtCategoryOrProductReward()
            throws Exception {

        // given
        int resultId = 1;
        String quantity = "0";
        String defaultResultId = "1";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessageHasValue(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1, 100);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, PRICE, TOTAL_PRICE, DEFAULT_PRICE,
                DISCOUNT_VALUE, ORIGINAL_CURRENCY_TOTAL_PRICE, CPA_FIXED, ZERO, NET, ZERO,
                ZERO);
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenQuantityValueIsBiggerThanDefaultValueAndConversionIsProductReward()
            throws Exception {
        // given
        int resultId = 3;
        String quantity = "2";
        String defaultResultId = "3";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        verifyLogQueueIsEmpty();
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(2, actualConversions.size());
        assertFields(actualConversions.get(0), 0, 1L, 4973L, 1L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-22 00:00:00-test-3-customerType-productId-0", 1L, 5, "test",
                resultId, "productId", PENDING, 1L, valueOf(108), valueOf(108), CPA_SALES,
                valueOf(0), valueOf(3.24), GROSS_AMOUNT_SOLD, valueOf(1.29),
                valueOf(1.62), valueOf(0), "127.0.0.1", null, "http://google.co.jp", null,
                "Mozilla/5.0 iPhone", null, null, 1L, valueOf(108), null, null, DESKTOP,
                "pointbackId", 1, null, "sessionId", "uuid", IPHONE, "categoryId",
                valueOf(10), "1$test$3$customerType", valueOf(90), "USD", "clickReferer",
                "clickUrl", "clickUserAgent", "English");
        assertFields(actualConversions.get(1), 0, 1L, 4973L, 1L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-22 00:00:00-test-3-customerType-productId-1", 1L, 5, "test",
                resultId, "productId", PENDING, 1L, valueOf(108), valueOf(108), CPA_SALES,
                valueOf(0), valueOf(3.24), GROSS_AMOUNT_SOLD, valueOf(1.29),
                valueOf(1.62), valueOf(0), "127.0.0.1", null, "http://google.co.jp", null,
                "Mozilla/5.0 iPhone", null, null, 1L, valueOf(108), null, null, DESKTOP,
                "pointbackId", 1, null, "sessionId", "uuid", IPHONE, "categoryId",
                valueOf(10), "1$test$3$customerType", valueOf(90), "USD", "clickReferer",
                "clickUrl", "clickUserAgent", "English");

        List<IntegrationTestParameter> actualClickParameters =
                clickParametersMapper.findAll();
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "test", "test");
        assertFields(actualClickParameters.get(2), "pbid", "pointbackId");
        assertFields(actualClickParameters.get(3), "language", "en_US");
        assertFields(actualClickParameters.get(4), "click_url", "clickUrl");
        assertFields(actualClickParameters.get(5), "click_referer", "clickReferer");

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertEquals(2, actualConversionParameters.size());
        assertFields(actualConversionParameters.get(0), "one", "1");
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenQuantityValueIsBiggerThanDefaultValueAndConversionIsCategoryReward()
            throws Exception {

        // given
        int resultId = 30;
        String quantity = "2";
        String defaultResultId = "30";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        verifyLogQueueIsEmpty();
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(2, actualConversions.size());
        assertFields(actualConversions.get(0), 0, 1L, 4973L, 1L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-22 00:00:00-test-30-customerType-productId-0", 1L, 5, "test",
                resultId, "productId", PENDING, 1L, valueOf(108),
                valueOf(108), CPA_FIXED, valueOf(0), valueOf(0),
                NET, valueOf(0), valueOf(0), valueOf(0), "127.0.0.1", null,
                "http://google.co.jp", null, "Mozilla/5.0 iPhone", null, null, 1L,
                valueOf(108), null, null, DESKTOP, "pointbackId", 1, null,
                "sessionId", "uuid", IPHONE, "categoryId", valueOf(10),
                "1$test$30$customerType", valueOf(90), "USD", "clickReferer", "clickUrl",
                "clickUserAgent", "English");
        assertFields(actualConversions.get(1), 0, 1L, 4973L, 1L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-22 00:00:00-test-30-customerType-productId-1", 1L, 5, "test",
                resultId, "productId", PENDING, 1L, valueOf(108),
                valueOf(108), CPA_FIXED, valueOf(0), valueOf(0),
                NET, valueOf(0), valueOf(0), valueOf(0), "127.0.0.1", null,
                "http://google.co.jp", null, "Mozilla/5.0 iPhone", null, null, 1L,
                valueOf(108), null, null, DESKTOP, "pointbackId", 1, null,
                "sessionId", "uuid", IPHONE, "categoryId", valueOf(10),
                "1$test$30$customerType", valueOf(90), "USD", "clickReferer", "clickUrl",
                "clickUserAgent", "English");

        List<IntegrationTestParameter> actualClickParameters =
                clickParametersMapper.findAll();
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "test", "test");
        assertFields(actualClickParameters.get(2), "pbid", "pointbackId");
        assertFields(actualClickParameters.get(3), "language", "en_US");
        assertFields(actualClickParameters.get(4), "click_url", "clickUrl");
        assertFields(actualClickParameters.get(5), "click_referer", "clickReferer");

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertEquals(2, actualConversionParameters.size());
        assertFields(actualConversionParameters.get(0), "one", "1");
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenQuantityValueIsBiggerThanDefaultValueAndConversionIsNotCategoryOrProductReward()
            throws Exception {

        // given
        int resultId = 2;
        String quantity = "2";
        String defaultResultId = "2";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessageHasValue(defaultResultId, quantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1, 500);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        verifyLogQueueIsEmpty();
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(1, actualConversions.size());
        assertFields(actualConversions.get(0), 0, 1L, 4973L, 1L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-22 00:00:00-test-2-customerType-productId", 1L, 5, "test",
                resultId, "productId", PENDING, 1L, valueOf(540),
                valueOf(540), CPA_FIXED, valueOf(0), valueOf(0),
                NET, valueOf(0), valueOf(0), valueOf(0), "127.0.0.1", null,
                "http://google.co.jp", null, "Mozilla/5.0 iPhone", null, null, 1L,
                valueOf(540), null, null, DESKTOP, "pointbackId", 1, null,
                "sessionId", "uuid", IPHONE, "categoryId", valueOf(50),
                "1$test$2$customerType", valueOf(450), "USD", "clickReferer", "clickUrl",
                "clickUserAgent", "English");

        List<IntegrationTestParameter> actualClickParameters = clickParametersMapper
                .findAll();
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "test", "test");
        assertFields(actualClickParameters.get(2), "pbid", "pointbackId");
        assertFields(actualClickParameters.get(3), "language", "en_US");
        assertFields(actualClickParameters.get(4), "click_url", "clickUrl");
        assertFields(actualClickParameters.get(5), "click_referer", "clickReferer");

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertEquals(1, actualConversionParameters.size());
        assertFields(actualConversionParameters.get(0), "one", "1");
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenQuantityValueIsBiggerThanDefaultValueAndConversionIsNotCategoryOrProductRewardAndIsDiscountableConversion()
            throws Exception {

        // given
        int resultId = 2;
        String quantity = "3";
        String defaultResultId = "2";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:" + defaultIdentifier
                + "&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&value:500"
                + "&result_id:" + defaultResultId
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM#Referer=" + defaultReferer + "#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=" + defaultIdentifier + "#Session_ID="
                + defaultSessionId + "#Sales_Date=" + defaultConversionDate
                + "#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId#Quantity=" + quantity
                + "#Unit_Price=500#Category_ID=" + defaultCategoryId
                + "#Original_total_price=1500#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=500#Pbid=pointbackId"
                + "#Customer_Type=" + CUSTOMER_TYPE + "#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        verifyLogQueueIsEmpty();
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(1, actualConversions.size());
        assertFields(actualConversions.get(0), 0, 1L, 4973L, 1L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-22 00:00:00-test-2-customerType-productId", 1L, 5, "test",
                resultId, "productId", PENDING, 1L, valueOf(400),
                valueOf(400), CPA_FIXED, valueOf(0), valueOf(0),
                NET, valueOf(0), valueOf(0), valueOf(0), "127.0.0.1", null,
                "http://google.co.jp", null, "Mozilla/5.0 iPhone", null, null, 1L,
                valueOf(400), null, null, DESKTOP, "pointbackId", 1, null,
                "sessionId", "uuid", IPHONE, "categoryId", valueOf(166.67),
                "1$test$2$customerType", valueOf(333.33), "USD", "clickReferer",
                "clickUrl", "clickUserAgent", "English");

        List<IntegrationTestParameter> actualClickParameters = clickParametersMapper
                .findAll();
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "test", "test");
        assertFields(actualClickParameters.get(2), "pbid", "pointbackId");
        assertFields(actualClickParameters.get(3), "language", "en_US");
        assertFields(actualClickParameters.get(4), "click_url", "clickUrl");
        assertFields(actualClickParameters.get(5), "click_referer", "clickReferer");

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertEquals(1, actualConversionParameters.size());
        assertFields(actualConversionParameters.get(0), "one", "1");
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenQuantityDoesNotExistAndConversionIsCategoryReward()
            throws Exception {
        // given
        String resultId = "30";
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:"
                + resultId + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj"
                + "5OqiOpCFpkNWHQgKM#Referer=http://google.co.jp#User-agent=Mozilla/5.0 "
                + "iPhone#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId#Unit_Price=100"
                + "#Category_ID=categoryId#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Pbid=pointbackId"
                + "#Language=en_US#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowsExceptionWhenGivenQuantityDoesNotExistAndConversionIsProductReward()
            throws Exception {
        // given
        String resultId = "3";
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:"
                + resultId + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj"
                + "5OqiOpCFpkNWHQgKM#Referer=http://google.co.jp#User-agent=Mozilla/5.0 "
                + "iPhone#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId#Unit_Price=100"
                + "#Category_ID=categoryId#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Pbid=pointbackId"
                + "#Language=en_US#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenGivenQuantityDoesNotExistAndConversionIsNotProductOrCategoryReward()
            throws Exception {
        // given
        int resultId = 2;
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&value:100"
                + "&result_id:" + resultId
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj"
                + "5OqiOpCFpkNWHQgKM#Referer=http://google.co.jp#User-agent=Mozilla/5.0 "
                + "iPhone#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId#Unit_Price=100"
                + "#Category_ID=categoryId#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Pbid=pointbackId"
                + "#Customer_Type=" + CUSTOMER_TYPE + "#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries =
                Arrays.asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, PRICE, TOTAL_PRICE, DEFAULT_PRICE,
                DISCOUNT_VALUE, ORIGINAL_CURRENCY_TOTAL_PRICE, CPA_FIXED, ZERO, NET, ZERO,
                ZERO);
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadAConversionIntoTheDatabaseCorrectlyWhenGivenSalesDateFormatIsYyyySlashMmSlashDdHhMmSs()
            throws Exception {
        // given
        int resultId = 3;
        String defaultResultId = "3";
        String defaultQuantity = "1";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String dateTimeFormatYyyySlashMmSlashDdHhMmSs = "2017/01/22 16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, defaultQuantity, defaultReferer,
                defaultIdentifier, dateTimeFormatYyyySlashMmSlashDdHhMmSs,
                defaultCategoryId, defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, PRICE, TOTAL_PRICE, DEFAULT_PRICE,
                DISCOUNT_VALUE, ORIGINAL_CURRENCY_TOTAL_PRICE, CPA_SALES, valueOf(3.24),
                GROSS_AMOUNT_SOLD, valueOf(1.29), valueOf(1.62));

    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadAConversionIntoTheDatabaseCorrectlyWhenGivenSalesDateFormatIsYyyySlashMmSlashDdTHhMmSs()
            throws Exception {
        // given
        int resultId = 3;
        String defaultResultId = "3";
        String defaultQuantity = "1";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String dateTimeFormatYyyySlashMmSlashDdTHhMmSs = "2017/01/22T16:51:17";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, defaultQuantity, defaultReferer,
                defaultIdentifier, dateTimeFormatYyyySlashMmSlashDdTHhMmSs,
                defaultCategoryId, defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, PRICE, TOTAL_PRICE, DEFAULT_PRICE,
                DISCOUNT_VALUE, ORIGINAL_CURRENCY_TOTAL_PRICE, CPA_SALES, valueOf(3.24),
                GROSS_AMOUNT_SOLD, valueOf(1.29), valueOf(1.62));
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenCategoryIdIsEmptyStringAndConversionIsCategoryReward()
            throws Exception {
        // given
        String defaultQuantity = "1";
        String defaultResultId = "30";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String categoryId = "";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, defaultQuantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, categoryId, defaultSessionId,
                DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenGivenCategoryIdIsEmptyStringAndConversionIsNotCategoryReward()
            throws Exception {
        // given
        int resultId = 3;
        String defaultQuantity = "1";
        String defaultResultId = "3";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String categoryId = "";
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, defaultQuantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, categoryId, defaultSessionId,
                DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        verifyLogQueueIsEmpty();
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(1, actualConversions.size());
        assertFields(actualConversions.get(0), 0, 1L, 4973L, 1L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-22 00:00:00-test-3-customerType-productId", 1L, 5, "test",
                resultId, "productId", PENDING, 1L, valueOf(108),
                valueOf(108), CPA_SALES, valueOf(0), valueOf(3.24), GROSS_AMOUNT_SOLD,
                valueOf(1.29), valueOf(1.62), valueOf(0), "127.0.0.1", null,
                "http://google.co.jp", null, "Mozilla/5.0 iPhone", null, null, 1L,
                valueOf(108), null, null, DESKTOP, "pointbackId", 1, null,
                "sessionId", "uuid", IPHONE, null, valueOf(10),
                "1$test$3$customerType", valueOf(90), "USD", "clickReferer", "clickUrl",
                "clickUserAgent", "English");

        List<IntegrationTestParameter> actualClickParameters = clickParametersMapper
                .findAll();
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "test", "test");
        assertFields(actualClickParameters.get(2), "pbid", "pointbackId");
        assertFields(actualClickParameters.get(3), "language", "en_US");
        assertFields(actualClickParameters.get(4), "click_url", "clickUrl");
        assertFields(actualClickParameters.get(5), "click_referer", "clickReferer");

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertEquals(1, actualConversionParameters.size());
        assertFields(actualConversionParameters.get(0), "one", "1");
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenCategoryIdIsNullAndConversionIsCategoryReward()
            throws Exception {
        // given
        String defaultQuantity = "1";
        String defaultResultId = "30";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String categoryId = null;
        String defaultSessionId = "sessionId";
        String message = createMessage(defaultResultId, defaultQuantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, categoryId, defaultSessionId,
                DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenGivenCategoryIdIsNullAndConversionIsCategoryReward()
            throws Exception {
        // given
        int resultId = 3;
        String defaultQuantity = "1";
        String defaultResultId = "3";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultSessionId = "sessionId";
        String defaultCategoryId = null;
        String message = createMessage(defaultResultId, defaultQuantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        verifyLogQueueIsEmpty();
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(1, actualConversions.size());
        assertFields(actualConversions.get(0), 0, 1L, 4973L, 1L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-22 00:00:00-test-3-customerType-productId", 1L, 5, "test",
                resultId, "productId", PENDING, 1L, valueOf(108), valueOf(108), CPA_SALES,
                valueOf(0), valueOf(3.24), GROSS_AMOUNT_SOLD, valueOf(1.29),
                valueOf(1.62), valueOf(0), "127.0.0.1", null, "http://google.co.jp", null,
                "Mozilla/5.0 iPhone", null, null, 1L, valueOf(108), null, null, DESKTOP,
                "pointbackId", 1, null, "sessionId", "uuid", IPHONE, "null", valueOf(10),
                "1$test$3$customerType", valueOf(90), "USD", "clickReferer", "clickUrl",
                "clickUserAgent", "English");

        List<IntegrationTestParameter> actualClickParameters = clickParametersMapper
                .findAll();
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "test", "test");
        assertFields(actualClickParameters.get(2), "pbid", "pointbackId");
        assertFields(actualClickParameters.get(3), "language", "en_US");
        assertFields(actualClickParameters.get(4), "click_url", "clickUrl");
        assertFields(actualClickParameters.get(5), "click_referer", "clickReferer");

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertEquals(1, actualConversionParameters.size());
        assertFields(actualConversionParameters.get(0), "one", "1");
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenCategoryIdDoesNotExistAndConversionIsCategoryReward()
            throws Exception {
        // given
        int resultId = 30;
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:"
                + resultId
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId#Quantity=1"
                + "#Unit_Price=100" + "#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Pbid=pointbackId"
                + "#Language=en_US#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenGivenCategoryIdDoesNotExistAndConversionIsNotCategoryReward()
            throws Exception {
        // given
        int resultId = 3;
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:"
                + resultId
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId#Quantity=1#Category_ID=categoryId"
                + "#Unit_Price=100" + "#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Pbid=pointbackId"
                + "#Customer_Type=" + CUSTOMER_TYPE + "#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries =
                Arrays.asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, PRICE, TOTAL_PRICE, DEFAULT_PRICE,
                DISCOUNT_VALUE, ORIGINAL_CURRENCY_TOTAL_PRICE, CPA_SALES, valueOf(3.24),
                GROSS_AMOUNT_SOLD, valueOf(1.29), valueOf(1.62));
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenUnitPriceDoesNotExistAndConversionIsCategoryReward()
            throws Exception {

        // given
        int resultId = 30;
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:"
                + resultId
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId#Quantity=1#Category_ID=categoryId"
                + "#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Pbid=pointbackId"
                + "#Customer_Type=" + CUSTOMER_TYPE + "#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldNotLoadConversionAndThrowExceptionWhenGivenUnitPriceDoesNotExistAndConversionIsProductReward()
            throws Exception {

        // given
        int resultId = 3;
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:"
                + resultId
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId#Quantity=1#Category_ID=categoryId"
                + "#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Pbid=pointbackId"
                + "#Customer_Type=" + CUSTOMER_TYPE + "#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsNotSaved();
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWithPriceIsDefaultValueWhenGivenUnitPriceDoesNotExistAndConversionIsNotCategoryOrProductReward()
            throws Exception {

        // given
        int resultId = 2;
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:"
                + resultId
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId#Quantity=1#Category_ID=categoryId"
                + "#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=10#Pbid=pointbackId"
                + "#Customer_Type=" + CUSTOMER_TYPE + "#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, BigDecimal.ZERO, BigDecimal.ZERO,
                BigDecimal.ZERO, DISCOUNT_VALUE, BigDecimal.ZERO, CPA_FIXED, ZERO, NET,
                ZERO, ZERO);

        List<IntegrationTestParameter> actualClickParameters = clickParametersMapper
                .findAll();
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "test", "test");
        assertFields(actualClickParameters.get(2), "pbid", "pointbackId");
        assertFields(actualClickParameters.get(3), "language", "en_US");
        assertFields(actualClickParameters.get(4), "click_url", "clickUrl");
        assertFields(actualClickParameters.get(5), "click_referer", "clickReferer");

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertEquals(1, actualConversionParameters.size());
        assertFields(actualConversionParameters.get(0), "one", "1");

    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadAConversionIntoTheDatabaseWithDiscountPriceZeroWhenGivenDiscountIsHigherThanUnitPrice()
            throws Exception {
        // given
        int resultId = 3;
        String validReferer = "http://google.co.jp";
        String defaultSessionId = "sessionId";
        String defaultIdentifier = "test";
        String defaultResultId = "3";
        String defaultQuantity = "1";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultCategoryId = "categoryId";
        String higherDiscount = "200";
        String message = createMessage(defaultResultId, defaultQuantity, validReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, higherDiscount, CAMPAIGN_ID_1);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, BigDecimal.ZERO, BigDecimal.ZERO,
                BigDecimal.ZERO, valueOf(200), BigDecimal.ZERO, CPA_SALES, ZERO,
                GROSS_AMOUNT_SOLD, ZERO, ZERO);
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadAConversionIntoTheDatabaseWithPriceFromValueParamWhenProductIdIsAvailableButUnitPriceIsMissing()
            throws Exception {
        // given
        int resultId = 2;
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:2"
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM&value:500#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=test#Session_ID=sessionId"
                + "#Sales_Date=2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=1#Banner_Id="
                + "4973#Partner_Site_No=1#Goods_ID=productId#Quantity=1"
                + "#Category_ID=categoryId"
                + "#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=0" + "#Pbid=pointbackId"
                + "#Customer_Type=" + CUSTOMER_TYPE + "#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        assertConversionIsSaved(resultId, valueOf(600), valueOf(600), valueOf(600),
                BigDecimal.ZERO, valueOf(500), CPA_FIXED, ZERO, NET, ZERO, ZERO);
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenSomeConversionsIncludingSameProductIdAreRegisteredInSqs()
            throws Exception {
        // given
        int resultId = 3;
        String defaultResultId = "3";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-31T11:22:33";
        String defaultCategoryId = "categoryId";
        String defaultSessionId = "sessionId";
        String message1 = createMessage(defaultResultId, "2", defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        String message2 = createMessage(defaultResultId, "1", defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, CAMPAIGN_ID_1);
        logQueueByDefault.send(message1);
        logQueueByDefault.send(message2);

        // when
        processMultipleConversionLogs();

        // then
        verifyLogQueueIsEmpty();
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(3, actualConversions.size());
        assertFields(actualConversions.get(0), 0, 1L, 4973L, 1L, null,
                of(2017, 1, 21, 17, 46), of(2017, 1, 31, 11, 22, 33),
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-31 00:00:00-test-3-customerType-productId", 1L, 5, "test",
                resultId, "productId", PENDING, 1L, valueOf(108), valueOf(108), CPA_SALES,
                valueOf(0), valueOf(3.24), GROSS_AMOUNT_SOLD, valueOf(1.29),
                valueOf(1.62), valueOf(0), "127.0.0.1", null, "http://google.co.jp", null,
                "Mozilla/5.0 iPhone", null, null, 1L, valueOf(108), null, null, DESKTOP,
                "pointbackId", 1, null, "sessionId", "uuid", IPHONE, "categoryId",
                valueOf(10), "1$test$3$customerType", valueOf(90), "USD", "clickReferer",
                "clickUrl", "clickUserAgent", "English");
        assertFields(actualConversions.get(1), 0, 1L, 4973L, 1L, null,
                of(2017, 1, 21, 17, 46), of(2017, 1, 31, 11, 22, 33),
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-31 00:00:00-test-3-customerType-productId-0", 1L, 5, "test",
                resultId, "productId", PENDING, 1L, valueOf(108), valueOf(108), CPA_SALES,
                valueOf(0), valueOf(3.24), GROSS_AMOUNT_SOLD, valueOf(1.29),
                valueOf(1.62), valueOf(0), "127.0.0.1", null, "http://google.co.jp", null,
                "Mozilla/5.0 iPhone", null, null, 1L, valueOf(108), null, null, DESKTOP,
                "pointbackId", 1, null, "sessionId", "uuid", IPHONE, "categoryId",
                valueOf(10), "1$test$3$customerType", valueOf(90), "USD", "clickReferer",
                "clickUrl", "clickUserAgent", "English");
        assertFields(actualConversions.get(2), 0, 1L, 4973L, 1L, null,
                of(2017, 1, 21, 17, 46), of(2017, 1, 31, 11, 22, 33),
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-31 00:00:00-test-3-customerType-productId-1", 1L, 5, "test",
                resultId, "productId", PENDING, 1L, valueOf(108), valueOf(108), CPA_SALES,
                valueOf(0), valueOf(3.24), GROSS_AMOUNT_SOLD, valueOf(1.29),
                valueOf(1.62), valueOf(0), "127.0.0.1", null, "http://google.co.jp", null,
                "Mozilla/5.0 iPhone", null, null, 1L, valueOf(108), null, null, DESKTOP,
                "pointbackId", 1, null, "sessionId", "uuid", IPHONE, "categoryId",
                valueOf(10), "1$test$3$customerType", valueOf(90), "USD", "clickReferer",
                "clickUrl", "clickUserAgent", "English");

        List<IntegrationTestParameter> actualClickParameters =
                clickParametersMapper.findAll();
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "test", "test");
        assertFields(actualClickParameters.get(2), "pbid", "pointbackId");
        assertFields(actualClickParameters.get(3), "language", "en_US");
        assertFields(actualClickParameters.get(4), "click_url", "clickUrl");
        assertFields(actualClickParameters.get(5), "click_referer", "clickReferer");

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertEquals(3, actualConversionParameters.size());
        assertFields(actualConversionParameters.get(0), "one", "1");
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenGivenCampaignDuplicationCutValueIsFalse()
            throws Exception {
        // given
        int resultId = 3;
        String defaultQuantity = "1";
        String defaultResultId = "3";
        String defaultReferer = "http://google.co.jp";
        String defaultIdentifier = "test";
        String defaultConversionDate = "2017-01-22T16:51:17";
        String defaultSessionId = "sessionId";
        String campaignId = "3";
        String defaultCategoryId = null;
        String message = createMessage(defaultResultId, defaultQuantity, defaultReferer,
                defaultIdentifier, defaultConversionDate, defaultCategoryId,
                defaultSessionId, DISCOUNT, campaignId);
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        verifyLogQueueIsEmpty();
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(1, actualConversions.size());
        assertEquals(36, actualConversions.get(0).getInternalTransactionId().length());
        assertFields(actualConversions.get(0), 0, 1L, 4973L, 3L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-22 00:00:00-test-3-customerType-productId-", 1L, 0, "test",
                resultId, "productId", PENDING, 1L, valueOf(108),
                valueOf(108), CPA_FIXED, valueOf(0), valueOf(0),
                NET, valueOf(0), valueOf(0), valueOf(0), "127.0.0.1", null,
                "http://google.co.jp", null, "Mozilla/5.0 iPhone", null, null, 1L,
                valueOf(108), null, null, DESKTOP, "pointbackId", 1, null,
                "sessionId", "uuid", IPHONE, "null", valueOf(10),
                actualConversions.get(0).getInternalTransactionId(), valueOf(90), "USD",
                "clickReferer", "clickUrl", "clickUserAgent", "English");

        List<IntegrationTestParameter> actualClickParameters = clickParametersMapper
                .findAll();
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "test", "test");
        assertFields(actualClickParameters.get(2), "pbid", "pointbackId");
        assertFields(actualClickParameters.get(3), "language", "en_US");
        assertFields(actualClickParameters.get(4), "click_url", "clickUrl");
        assertFields(actualClickParameters.get(5), "click_referer", "clickReferer");

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertEquals(1, actualConversionParameters.size());
        assertFields(actualConversionParameters.get(0), "one", "1");
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenGivenCampaignDuplicationCutValueIsFalseAndProductIsNotInConversion()
            throws Exception {
        // given
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#"
                + "Request=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&"
                + "result_id:1&goods:productId;1;100&"
                + "rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWHQgKM#"
                + "Referer=http://google.co.jp#"
                + "User-agent=Mozilla/5.0 iPhone#Device_Type=1#"
                + "Identifier=test#Session_ID=sessionId#Sales_Date=2017-01-22T16:51:17#"
                + "Rk=0003u500002b#Click_Date=2017-01-21 16:46:00#UUID=uuid#"
                + "Merchant_Campaign_No=3#Banner_Id=4973#Partner_Site_No=1#"
                + "Unit_Price=100#Category_ID=null#Original_total_price=100#test=test#"
                + "CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl#"
                + "Click_User_Agent=clickUserAgent#Discount=10#Pbid=pointbackId"
                + "#Customer_Type=" + CUSTOMER_TYPE + "#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;

        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        verifyLogQueueIsEmpty();
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(1, actualConversions.size());
        assertEquals(36, actualConversions.get(0).getInternalTransactionId().length());
        assertFields(actualConversions.get(0), 0, 1L, 4973L, 3L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-22 00:00:00-test-1-", 1L, 0, "test", 1,
                null, PENDING, 1L, valueOf(0), valueOf(0),
                CPA_FIXED, valueOf(0), valueOf(0), NET,
                valueOf(0), valueOf(0), valueOf(0), "127.0.0.1", null,
                "http://google.co.jp", null, "Mozilla/5.0 iPhone", null, null, 1L,
                valueOf(0), null, null, DESKTOP, "pointbackId", 1, null,
                "sessionId", "uuid", IPHONE, "null", valueOf(10),
                actualConversions.get(0).getInternalTransactionId(), valueOf(0), "USD",
                "clickReferer", "clickUrl", "clickUserAgent", "English");

        List<IntegrationTestParameter> actualClickParameters = clickParametersMapper
                .findAll();
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "test", "test");
        assertFields(actualClickParameters.get(2), "pbid", "pointbackId");
        assertFields(actualClickParameters.get(3), "language", "en_US");
        assertFields(actualClickParameters.get(4), "click_url", "clickUrl");
        assertFields(actualClickParameters.get(5), "click_referer", "clickReferer");

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertEquals(1, actualConversionParameters.size());
        assertFields(actualConversionParameters.get(0), "one", "1");
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionAndParametersCorrectlyWhenGivenParametersValueIsEmpty()
            throws Exception {
        // given
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#"
                + "Request=identifier:test&9cfdf10e8fc047a44b08ed031e1f0ed1&"
                + "result_id:1&goods:productId;1;100&"
                + "rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWHQgKM#"
                + "Referer=http://google.co.jp#"
                + "User-agent=Mozilla/5.0 iPhone#Device_Type=1#"
                + "Identifier=test#Session_ID=sessionId#Sales_Date=2017-01-22T16:51:17#"
                + "Rk=0003u500002b#Click_Date=2017-01-21 16:46:00#UUID=uuid#"
                + "Merchant_Campaign_No=3#Banner_Id=4973#Partner_Site_No=1#"
                + "Unit_Price=100#Category_ID=null#Original_total_price=100#test=test#"
                + "CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl#"
                + "Click_User_Agent=clickUserAgent#Discount=10#Pbid=pointbackId#"
                + "CV_PARAM_CUSTOMER_TYPE=#Customer_Type=" + CUSTOMER_TYPE
                + "#Language=en_US#Click_IP_Address=" + CLICK_IP_ADDRESS;

        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        verifyLogQueueIsEmpty();
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(1, actualConversions.size());
        assertEquals(36, actualConversions.get(0).getInternalTransactionId().length());
        assertFields(actualConversions.get(0), 0, 1L, 4973L, 3L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null, "2017-01-22 00:00:00-test-1-", 1L, 0,
                "test", 1, null, PENDING, 1L, valueOf(0), valueOf(0),
                CPA_FIXED, valueOf(0), valueOf(0), NET,
                valueOf(0), valueOf(0), valueOf(0), "127.0.0.1", null,
                "http://google.co.jp", null, "Mozilla/5.0 iPhone", null, null, 1L,
                valueOf(0), null, null, DESKTOP, "pointbackId", 1, null,
                "sessionId", "uuid", IPHONE, "null", valueOf(10),
                actualConversions.get(0).getInternalTransactionId(), valueOf(0), "USD",
                "clickReferer", "clickUrl", "clickUserAgent", "English");

        List<IntegrationTestParameter> actualClickParameters = clickParametersMapper
                .findAll();
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "test", "test");
        assertFields(actualClickParameters.get(2), "pbid", "pointbackId");
        assertFields(actualClickParameters.get(3), "language", "en_US");
        assertFields(actualClickParameters.get(4), "click_url", "clickUrl");
        assertFields(actualClickParameters.get(5), "click_referer", "clickReferer");

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertEquals(2, actualConversionParameters.size());
        assertFields(actualConversionParameters.get(0), "customer_type", null);
        assertFields(actualConversionParameters.get(1), "one", "1");
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionCorrectlyWhenGivenCurrency()
            throws Exception {
        // given
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:2"
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM&value:1000#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=4#Banner_Id="
                + "4444#Partner_Site_No=1#Goods_ID=productId#Quantity=1#Category_ID=categoryId"
                + "#Unit_Price=1000#Original_total_price=1000##Click_Referer=clickReferer"
                + "#Click_Url=clickUrl#Click_User_Agent=clickUserAgent#Customer_Type="
                + CUSTOMER_TYPE + "#Language=en_US#Click_IP_Address=" + CLICK_IP_ADDRESS
                + "#currency=jpy";
        List<SendMessageBatchRequestEntry> entries = Arrays.asList(
                new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(1, actualConversions.size());
        assertFields(actualConversions.get(0), 0, 1L, 4444L, 4L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-22 00:00:00-test-2-customerType-productId", 1L, 5, "test", 2,
                "productId", PENDING, 1L, valueOf(1500), valueOf(1500),
                CPA_FIXED, valueOf(0), valueOf(0), NET,
                valueOf(0), valueOf(0), valueOf(0), "127.0.0.1", null,
                "http://google.co.jp", null, "Mozilla/5.0 iPhone", null, null, 1L,
                valueOf(1500), null, null, DESKTOP, null, 1, null, "sessionId",
                "uuid", IPHONE, "categoryId", valueOf(0),
                "2017-01-22$4$test$2$customerType", valueOf(1000), "JPY", "clickReferer",
                "clickUrl", "clickUserAgent", "English");
    }

    @Test
    public void testConversionLogLoaderBatchShouldLoadConversionWithDecodedUrlCorrectlyWhenGivenUrl()
            throws Exception {
        // given
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:test&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:2"
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM&value:1000#Referer=http://google.co.jp#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=test#Session_ID=sessionId#Sales_Date="
                + "2017-01-22T16:51:17#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=4#Banner_Id="
                + "4444#Partner_Site_No=1#Goods_ID=productId#Quantity=1#Category_ID=categoryId"
                + "#Unit_Price=1000#Original_total_price=1000#Click_Referer="
                + "https%3A%2F%2Fgoogle1.com%3Ftest1%3D123%26test2%3D%23asdf"
                + "#Click_Url=https%3A%2F%2Fgoogle2.com%3Ftest1%3D123%26test2%3D%23asdf"
                + "#Click_User_Agent=clickUserAgent#Customer_Type="
                + CUSTOMER_TYPE + "#Language=en_US#Click_IP_Address=" + CLICK_IP_ADDRESS
                + "#currency=jpy";
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry("test", message));
        logQueueByDefault.send(entries);

        // when
        processSingleConversionLog();

        // then
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(1, actualConversions.size());
        assertFields(actualConversions.get(0), 0, 1L, 4444L, 4L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null,
                "2017-01-22 00:00:00-test-2-customerType-productId", 1L, 5, "test", 2,
                "productId", PENDING, 1L, valueOf(1500), valueOf(1500),
                CPA_FIXED, valueOf(0), valueOf(0), NET,
                valueOf(0), valueOf(0), valueOf(0), "127.0.0.1", null,
                "http://google.co.jp", null, "Mozilla/5.0 iPhone", null, null, 1L,
                valueOf(1500), null, null, DESKTOP, null, 1, null, "sessionId",
                "uuid", IPHONE, "categoryId", valueOf(0),
                "2017-01-22$4$test$2$customerType", valueOf(1000), "JPY",
                "https://google1.com?test1=123&test2=#asdf",
                "https://google2.com?test1=123&test2=#asdf", "clickUserAgent", "English");
        List<IntegrationTestParameter> actualClickParameters = clickParametersMapper
                .findAll();
        assertEquals(4, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "language", "en_US");
        assertFields(actualClickParameters.get(2), "click_url",
                "https://google2.com?test1=123&test2=#asdf");
        assertFields(actualClickParameters.get(3), "click_referer",
                "https://google1.com?test1=123&test2=#asdf");
    }

    private void assertFields(IntegrationTestConversion actual, int insertFlag,
            long conversionId, long creativeId, long campaignId, String nodeId,
            LocalDateTime clickDate, LocalDateTime conversionDate, LocalDateTime logDate,
            LocalDateTime confirmedDate, String transactionIdPrefix, long siteId,
            int rank, String identifier, int resultId, String productId,
            ConversionStatus conversionStatus, long quantity, BigDecimal price,
            BigDecimal totalPrice, RewardType rewardType, BigDecimal reward,
            BigDecimal totalPriceReward, CommissionType commissionType,
            BigDecimal atCommission, BigDecimal agentCommission,
            BigDecimal publisherAgentCommission, String ipAddress, String mediaUrl,
            String referer, Long repeatCount, String userAgent,
            LocalDateTime rewardEditDate, Integer trackingType, long defaultQuantity,
            BigDecimal defaultPrice, Integer defaultResultId, String lpUrl,
            DeviceType deviceType, String pointbackId, int pbIdDuplicativeFlag,
            String pbIdOldestConversionDate, String sessionId, String uuid,
            DeviceOs deviceOs, String categoryId, BigDecimal discount,
            String internalTransactionId, BigDecimal originalCurrencyTotalPrice,
            String originalCurrency, String clickReferer, String clickUrl,
            String clickUserAgent, String language) {
        assertNotNull(actual);
        assertTrue(actual.getConversionId().longValue() > 0);
        assertEquals(creativeId, actual.getCreativeId().longValue());
        assertEquals(campaignId, actual.getCampaignId().longValue());
        assertEquals(clickDate, actual.getClickDate());
        assertEquals(conversionDate, actual.getConversionDate());
        assertEquals(logDate, actual.getLogDate());
        assertEquals(confirmedDate, actual.getConfirmedDate());
        assertTrue(StringUtils.startsWith(actual.getTransactionId(),
                transactionIdPrefix));
        assertEquals(siteId, actual.getSiteId().longValue());
        assertEquals(rank, actual.getRank().intValue());
        assertEquals(identifier, actual.getIdentifier());
        assertEquals(resultId, actual.getResultId().intValue());
        assertEquals(productId, actual.getProductId());
        assertEquals(conversionStatus, actual.getConversionStatus());
        assertEquals(quantity, actual.getQuantity().longValue());
        assertEquals(price.doubleValue(), actual.getPrice().doubleValue(), 0);
        assertEquals(totalPrice.doubleValue(), actual.getTotalPrice().doubleValue(), 0);
        assertEquals(rewardType, actual.getRewardType());
        assertEquals(reward.doubleValue(), actual.getReward().doubleValue(), 0);
        assertEquals(totalPriceReward.doubleValue(),
                actual.getTotalPriceReward().doubleValue(), 0);
        assertEquals(commissionType, actual.getCommissionType());
        assertEquals(atCommission.doubleValue(), actual.getAtCommission().doubleValue(),
                0);
        assertEquals(agentCommission.doubleValue(),
                actual.getAgentCommission().doubleValue(), 0);
        assertEquals(publisherAgentCommission.doubleValue(),
                actual.getPublisherAgentCommission().doubleValue(), 0);
        assertEquals(ipAddress, actual.getIpAddress());
        assertEquals(referer, actual.getReferer());
        assertEquals(userAgent, actual.getUserAgent());
        assertNotNull(actual.getRewardEditDate());
        assertEquals(trackingType, actual.getTrackingType());
        assertEquals(defaultQuantity, actual.getDefaultQuantity().longValue());
        assertEquals(defaultPrice.doubleValue(), actual.getDefaultPrice().doubleValue(),
                0);
        assertEquals(defaultResultId, actual.getDefaultResultId());
        assertEquals(lpUrl, actual.getLpUrl());
        assertEquals(deviceType, actual.getDeviceType());
        assertEquals(pointbackId, actual.getPointbackId());
        assertEquals(pbIdDuplicativeFlag, actual.getPbIdDuplicativeFlag().intValue());
        assertEquals(sessionId, actual.getSessionId());
        assertEquals(uuid, actual.getUuid());
        assertEquals(deviceOs, actual.getDeviceOs());
        assertEquals(categoryId, actual.getCategoryId());
        assertEquals(discount.doubleValue(), actual.getDiscount().doubleValue(), 0);
        assertEquals(internalTransactionId, actual.getInternalTransactionId());
        assertEquals(originalCurrencyTotalPrice.doubleValue(),
                actual.getOriginalCurrencyTotalPrice().doubleValue(), 0);
        assertEquals(originalCurrency, actual.getOriginalCurrency());
        assertEquals(clickReferer, actual.getClickReferer());
        assertEquals(clickUrl, actual.getClickUrl());
        assertEquals(clickUserAgent, actual.getClickUserAgent());
        assertEquals(CUSTOMER_TYPE, actual.getCustomerType());
        assertEquals(language, actual.getLanguage());
        assertEquals(CLICK_IP_ADDRESS, actual.getClickIpAddress());
    }

    private void assertFields(IntegrationTestParameter actual, String expectedName,
            String expectedValue) {
        assertNotNull(actual);
        assertEquals(expectedName, actual.getName());
        assertEquals(expectedValue, actual.getValue());
    }

    private String createMessage(String resultId, String quantity, String referer,
            String identifier, String conversionDate, String categoryId, String sessionId,
            String discount, String campaignId ) {
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:" + identifier
                + "&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:" + resultId
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM#Referer=" + referer + "#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=" + identifier + "#Session_ID=" + sessionId
                + "#Sales_Date=" + conversionDate + "#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=" + campaignId
                + "#Banner_Id=4973#Partner_Site_No=1#Goods_ID=productId#Quantity="
                + quantity + "#Unit_Price=100#Category_ID=" + categoryId
                + "#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=" + discount
                + "#Pbid=pointbackId#Customer_Type=" + CUSTOMER_TYPE + "#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        return message;
    }

    private String createMessageHasValue(String resultId, String quantity, String referer,
            String identifier, String conversionDate, String categoryId, String sessionId,
            String discount, String campaignId, int value) {
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:" + identifier + "&value:" + value
                + "&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1" + "&result_id:" + resultId
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM#Referer=" + referer + "#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=" + identifier + "#Session_ID=" + sessionId
                + "#Sales_Date=" + conversionDate + "#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=" + campaignId
                + "#Banner_Id=4973#Partner_Site_No=1#Goods_ID=productId#Quantity="
                + quantity + "#Unit_Price=100#Category_ID=" + categoryId
                + "#Original_total_price=100#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=" + discount
                + "#Pbid=pointbackId#Customer_Type=" + CUSTOMER_TYPE + "#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        return message;
    }

    private String createMessageHasPrice(String resultId, String quantity, String referer,
            String identifier, String conversionDate, String categoryId, String sessionId,
            String discount, String campaignId, int price ) {
        String message = "IP_address=127.0.0.1#Request_Time=2017-01-21 16:51:17#Request"
                + "=identifier:" + identifier
                + "&mcn:9cfdf10e8fc047a44b08ed031e1f0ed1&result_id:" + resultId
                + "&goods:productId;1;100&rk:ckxmPVcZE2TUKI0Y9SIAi2ofbtZp0dj5OqiOpCFpkNWH"
                + "QgKM#Referer=" + referer + "#User-agent=Mozilla/5.0 iPhone"
                + "#Device_Type=1#Identifier=" + identifier + "#Session_ID=" + sessionId
                + "#Sales_Date=" + conversionDate + "#Rk=0003u500002b#Click_Date="
                + "2017-01-21 16:46:00#UUID=uuid#Merchant_Campaign_No=" + campaignId
                + "#Banner_Id=4973#Partner_Site_No=1#Goods_ID=productId#Quantity="
                + quantity + "#Unit_Price=" + price + "#Category_ID=" + categoryId
                + "#Original_total_price=" + price + "#test=test"
                + "#CV_PARAM_ONE=1#Click_Referer=clickReferer#Click_Url=clickUrl"
                + "#Click_User_Agent=clickUserAgent#Discount=" + discount
                + "#Pbid=pointbackId#Customer_Type=" + CUSTOMER_TYPE + "#Language=en_US"
                + "#Click_IP_Address=" + CLICK_IP_ADDRESS;
        return message;
    }

    private void processSingleConversionLog() throws Exception {
        logRecordReader.open();
        SqsRecord inputRecord = logRecordReader.readRecord();
        LogRecord outputRecord = logRecordProcessor.processRecord(inputRecord);
        logRecordWriter.open();
        logRecordWriter.writeRecords(new Batch(outputRecord));
        logRecordWriter.close();
    }

    private void processMultipleConversionLogs() throws Exception {
        newJob(null)
                .named("ConversionLogLoaderIntegrationTest")
                .reader(logRecordReader)
                .processor(logRecordProcessor)
                .writer(logRecordWriter)
                .build()
                .call();
    }

    private void assertConversionIsNotSaved() {
        ReceiveMessageRequest request = new ReceiveMessageRequest(logQueueByDefault.getQueueUrl());
        ReceiveMessageResult result = logQueueByDefault.receiveMessage(request);
        assertEquals(0, result.getMessages().size());
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertTrue(actualConversions.isEmpty());

        List<IntegrationTestParameter> actualClickParameters = clickParametersMapper
                .findAll();
        assertTrue(actualClickParameters.isEmpty());

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertTrue(actualConversionParameters.isEmpty());
    }

    private void assertConversionIsSaved(int resultId, BigDecimal price,
            BigDecimal totalPrice, BigDecimal defaultPrice, BigDecimal discount,
            BigDecimal originalCurrencyTotalPrice, RewardType rewardType,
            BigDecimal totalPriceReward, CommissionType commissionType,
            BigDecimal atCommission, BigDecimal agentCommission) {
        ReceiveMessageRequest request = new ReceiveMessageRequest(logQueueByDefault.getQueueUrl());
        ReceiveMessageResult result = logQueueByDefault.receiveMessage(request);
        assertEquals(0, result.getMessages().size());
        List<IntegrationTestConversion> actualConversions = conversionMapper.findAll();
        assertEquals(1, actualConversions.size());
        String expectedTransactionIdPrefix = "2017-01-22 00:00:00-test-" + resultId + "-"
                + CUSTOMER_TYPE + "-productId";
        String expectedInternalTransactionId = "1$test$" + resultId + "$" + CUSTOMER_TYPE;
        assertFields(actualConversions.get(0), 0, 1L, 4973L, 1L, null,
                of(2017, 1, 21, 17, 46), EXPECTED_CONVERSION_TIME,
                of(2017, 1, 21, 17, 51, 17), null, expectedTransactionIdPrefix, 1L, 5,
                "test", resultId, "productId", PENDING, 1L, price,
                totalPrice, rewardType, valueOf(0), totalPriceReward, commissionType,
                atCommission, agentCommission, valueOf(0), "127.0.0.1",
                null, "http://google.co.jp", null, "Mozilla/5.0 iPhone", null, null, 1L,
                defaultPrice, null, null, DESKTOP, "pointbackId", 1, null,
                "sessionId", "uuid", IPHONE, "categoryId", discount,
                expectedInternalTransactionId, originalCurrencyTotalPrice, "USD",
                "clickReferer", "clickUrl", "clickUserAgent", "English");

        List<IntegrationTestParameter> actualClickParameters = clickParametersMapper
                .findAll();
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "click_user_agent", "clickUserAgent");
        assertFields(actualClickParameters.get(1), "test", "test");
        assertFields(actualClickParameters.get(2), "pbid", "pointbackId");
        assertFields(actualClickParameters.get(3), "language", "en_US");
        assertFields(actualClickParameters.get(4), "click_url", "clickUrl");
        assertFields(actualClickParameters.get(5), "click_referer", "clickReferer");

        List<IntegrationTestParameter> actualConversionParameters = conversionParametersMapper
                .findAll();
        assertEquals(1, actualConversionParameters.size());
        assertFields(actualConversionParameters.get(0), "one", "1");
    }

    private void verifyLogQueueIsEmpty() {
        ReceiveMessageRequest request = new ReceiveMessageRequest(logQueueByDefault.getQueueUrl());
        ReceiveMessageResult result = logQueueByDefault.receiveMessage(request);
        assertTrue(result.getMessages().isEmpty());
    }
}
