<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>jp.ne.interspace</groupId>
  <artifactId>taekkyeon</artifactId>
  <version>1.0.0</version>
  <packaging>pom</packaging>

  <name>Taekkyeon Project</name>

  <modules>
    <module>taekkyeon-affiliation-approval-batch</module>
    <module>taekkyeon-affiliation-dynamodb-sync-batch</module>
    <module>taekkyeon-async-staff-conversion-updater-batch</module>
    <module>taekkyeon-async-staff-report-exporter-batch</module>
    <module>taekkyeon-campaign-budget-calculation-batch</module>
    <module>taekkyeon-campaign-statistics-batch</module>
    <module>taekkyeon-campaign-status-auto-updater-batch</module>
    <module>taekkyeon-campaign-stopper-batch</module>
    <module>taekkyeon-click-log-loader-batch</module>
    <module>taekkyeon-conversion-approval-batch</module>
    <module>taekkyeon-conversion-city-detection-batch</module>
    <module>taekkyeon-conversion-log-loader-batch</module>
    <module>taekkyeon-conversion-rank-auto-updater-batch</module>
    <module>taekkyeon-core</module>
    <module>taekkyeon-creative-access-log-registration-batch</module>
    <module>taekkyeon-creative-dynamodb-sync-batch</module>
    <module>taekkyeon-email-report-loader-batch</module>
    <module>taekkyeon-email-sender-batch</module>
    <module>taekkyeon-global-conversion-status-sync-batch</module>
    <module>taekkyeon-global-conversion-sync-batch</module>
    <module>taekkyeon-impression-log-loader-batch</module>
    <module>taekkyeon-log-common</module>
    <module>taekkyeon-log-provider-batch</module>
    <module>taekkyeon-merchant-api-conversion-back-up-data-batch</module>
    <module>taekkyeon-monthly-closure-batch</module>
    <module>taekkyeon-monthly-transition-batch</module>
    <module>taekkyeon-old-data-deletion-batch</module>
    <module>taekkyeon-pointback-csv-uploader</module>
    <module>taekkyeon-pointback-ftp-uploader-batch</module>
    <module>taekkyeon-pointback-s3-uploader-batch</module>
    <module>taekkyeon-postback-sender-batch</module>
    <module>taekkyeon-postback-provider-batch</module>
    <module>taekkyeon-product-feed-elasticsearch-sync-batch</module>
    <module>taekkyeon-promo-sync-batch</module>
    <module>taekkyeon-publisher-funnel-trend-loader-batch</module>
    <module>taekkyeon-publisher-funnel-trend-provider-batch</module>
    <module>taekkyeon-publisher-onboarding-email-sender-batch</module>
    <module>taekkyeon-publisher-payment-calculator-batch</module>
    <module>taekkyeon-publisher-payment-history-status-updater-batch</module>
    <module>taekkyeon-publisher-payment-notifier-batch</module>
    <module>taekkyeon-publisher-rank-calculator-batch</module>
    <module>taekkyeon-publisher-site-dynamodb-sync-batch</module>
    <module>taekkyeon-publisher-tax-calculator-batch</module>
    <module>taekkyeon-publisher-tax-refund-provider-batch</module>
    <module>taekkyeon-publisher-traffic-source-data-batch</module>
    <module>taekkyeon-referral-campaign-conversion-registration-batch</module>
    <module>taekkyeon-referral-reward-calculator-batch</module>
    <module>taekkyeon-report-data-mv-refresher-batch</module>
    <module>taekkyeon-reward-calculator</module>
    <module>taekkyeon-reward-calculator-batch</module>
    <module>taekkyeon-scheduled-creative-settings-updater-batch</module>
    <module>taekkyeon-site-affiliated-campaign-ids-dynamodb-sync-batch</module>
    <module>taekkyeon-system-monitoring-provider-batch</module>
    <module>taekkyeon-system-monitoring-batch</module>
    <module>taekkyeon-tracking-data-sync-batch</module>
    <module>taekkyeon-user-data-sync-batch</module>
    <module>taekkyeon-publisher-tax-refund-calculator-batch</module>
    <module>taekkyeon-conversion-city-detection-provider-batch</module>
    <module>taekkyeon-google-ads-scraper-provider-batch</module>
    <module>taekkyeon-conversion-rank-auto-update-provider-batch</module>
    <module>taekkyeon-upserted-conversions-sender-batch</module>
    <module>taekkyeon-payment-data-sync-batch</module>
    <module>taekkyeon-google-keyword-analytics-provider-batch</module>
    <module>taekkyeon-publisher-traffic-source-data-provider-batch</module>
    <module>taekkyeon-report-data-mv-type-updater-batch</module>
    <module>taekkyeon-recreate-lazada-landing-page-batch</module>
    <module>taekkyeon-oracle-db-redshift-data-checker-batch</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven.compiler.plugin.optimize>true</maven.compiler.plugin.optimize>
    <maven.compiler.plugin.version>3.3</maven.compiler.plugin.version>
    <maven.compiler.target>1.8</maven.compiler.target>
    <maven.compiler.source>1.8</maven.compiler.source>
  </properties>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven.compiler.plugin.version}</version>
          <configuration>
            <source>${jdk.version}</source>
            <target>${jdk.version}</target>
            <annotationProcessors>
              <annotationProcessor>
                lombok.launch.AnnotationProcessorHider$AnnotationProcessor
              </annotationProcessor>
            </annotationProcessors>
            <encoding>${project.build.sourceEncoding}</encoding>
            <optimize>${maven.compiler.plugin.optimize}</optimize>
            <showWarnings>true</showWarnings>
            <showDeprecation>true</showDeprecation>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.sonarsource.scanner.maven</groupId>
          <artifactId>sonar-maven-plugin</artifactId>
          <version>3.6.0.1398</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
</project>
