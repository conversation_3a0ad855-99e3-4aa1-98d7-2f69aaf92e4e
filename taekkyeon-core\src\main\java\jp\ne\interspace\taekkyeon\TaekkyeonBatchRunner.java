/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Guice;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.name.Named;

import org.apache.ibatis.session.SqlSessionManager;
import org.easybatch.core.job.Job;
import org.easybatch.core.job.JobExecutor;
import org.easybatch.core.job.JobReport;
import org.easybatch.core.job.JobStatus;
import org.easybatch.core.processor.RecordProcessor;
import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.module.BatchNameBinding;
import jp.ne.interspace.taekkyeon.module.CustomBatchSizeBinding;
import jp.ne.interspace.taekkyeon.module.EmbeddedHsqldbInitializer;
import jp.ne.interspace.taekkyeon.module.MainRecordProcessorBinding;
import jp.ne.interspace.taekkyeon.module.MainRecordReaderBinding;
import jp.ne.interspace.taekkyeon.module.MainRecordWriterBinding;
import jp.ne.interspace.taekkyeon.module.MaxWaitForLogProcessingTimesBinding;
import jp.ne.interspace.taekkyeon.module.OracleResolver;
import jp.ne.interspace.taekkyeon.module.TaekkyeonMainModule;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.MyBatisSessionManagerRepository;

import static com.github.speedwing.log4j.cloudwatch.appender.CloudwatchAppender.keepDaemonActive;
import static java.lang.Thread.sleep;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.ENABLE_MULTIPLE_DATABASES;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.SHUTDOWN_PERIOD;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.SHUT_DOWN_MESSAGE;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.TARGET_COUNTRY;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonPropertiesModule.BIND_KEY_IS_BATCH_TRANSACTION;
import static org.easybatch.core.job.JobBuilder.newJob;
import static org.easybatch.core.job.JobParameters.DEFAULT_BATCH_SIZE;
import static org.easybatch.core.job.JobStatus.COMPLETED;
import static org.easybatch.core.job.JobStatus.FAILED;
import static org.slf4j.LoggerFactory.getLogger;

/**
 * Main class for running the various batches.
 *
 * <AUTHOR> Varga
 */
public class TaekkyeonBatchRunner {

    @Inject
    private Injector injector;

    @Inject(optional = true) @OracleResolver
    private SqlSessionManager sqlSessionManager;

    @Inject(optional = true)
    private MyBatisSessionManagerRepository sessionManagerRepository;

    @Inject @BatchNameBinding
    private String batchName;

    @Inject(optional = true) @CustomBatchSizeBinding
    private Integer customBatchSize;

    @Inject @MainRecordReaderBinding
    private RecordReader reader;

    @Inject @MainRecordProcessorBinding
    private RecordProcessor<? extends Record<?>, ? extends Record<?>> processor;

    @Inject @MainRecordWriterBinding
    private RecordWriter writer;

    @Inject @MaxWaitForLogProcessingTimesBinding
    private int maxWaitForLogProcessingTimes;

    @Inject @Named(BIND_KEY_IS_BATCH_TRANSACTION)
    private boolean isBatchTransaction;

    /**
     * The main method for running batches.
     *
     * @param args
     *            command line arguments
     */
    public static void main(String[] args) {
        TaekkyeonBatchRunner batchRunner = new TaekkyeonBatchRunner();
        createInjectorFor(batchRunner);
        batchRunner.verifyInMemoryRedshiftContextInjectability();
        Job jobToRun = batchRunner.buildJob();
        JobReport jobReport = batchRunner.executeJob(jobToRun);
        batchRunner.lazyLoadLogger().info(jobReport.toString());
        batchRunner.forceShutDown(jobReport.getStatus());
    }

    private static void createInjectorFor(TaekkyeonBatchRunner batchRunner) {
        try {
            Guice.createInjector(new TaekkyeonMainModule()).injectMembers(batchRunner);
        } catch (Exception ex) {
            batchRunner.lazyLoadLogger().error("Guice injection failed.", ex);
            batchRunner.forceShutDown(FAILED);
        }
    }

    @VisibleForTesting
    void verifyInMemoryRedshiftContextInjectability() {
        if (getCurrentEnvironment() == DEV) {
            try {
                injector.getInstance(EmbeddedHsqldbInitializer.class);
            } catch (Exception e) {
                // do nothing.
            }
        }
    }

    @VisibleForTesting
    Job buildJob() {
        int batchSize = customBatchSize != null ? customBatchSize : DEFAULT_BATCH_SIZE;
        return newJob(getSqlSessionManager()).named(batchName).reader(reader)
                .processor(processor).writer(writer).batchSize(batchSize).build();
    }

    @VisibleForTesting
    JobReport executeJob(Job job) {
        JobExecutor jobExecutor = new JobExecutor();
        JobReport jobReport = jobExecutor.execute(job);
        jobExecutor.shutdown();
        return jobReport;
    }

    private SqlSessionManager getSqlSessionManager() {
        if (isBatchTransaction) {
            return ENABLE_MULTIPLE_DATABASES
                    ? sessionManagerRepository.getSessionManagerOf(TARGET_COUNTRY)
                    : sqlSessionManager;
        }
        return null;
    }

    private void forceShutDown(JobStatus status) {
        lazyLoadLogger().info(SHUT_DOWN_MESSAGE);
        try {
            int waitDaemonActiveCount = 0;
            while (keepDaemonActive.get()
                    && waitDaemonActiveCount++ < maxWaitForLogProcessingTimes) {
                sleep(SHUTDOWN_PERIOD);
            }
        } catch (Exception ex) {
            // do nothing
        } finally {
            if (status == COMPLETED) {
                System.exit(0);
            } else {
                System.exit(1);
            }
        }
    }

    private Logger lazyLoadLogger() {
        return getLogger(TaekkyeonBatchRunner.class);
    }
}
